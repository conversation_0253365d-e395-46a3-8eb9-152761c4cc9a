# Custom AI Prompt Template System for P&L Analysis

## 🎯 Overview

The Custom AI Prompt Template System allows administrators to customize the AI prompts used for generating P&L (Profit & Loss) analysis in the Lumen Simulation platform. This system provides flexibility to tailor AI-generated content to match specific business contexts while leveraging real user interaction data.

## 🏗️ System Architecture

### Core Components

1. **Decision Schemes** - Top-level containers for simulation scenarios
2. **Decision Pages** - Individual pages within a scheme containing sliders, incentives, and P&L data
3. **User Selections** - FTE changes and investment amounts selected by users
4. **AI Analysis Engine** - OpenAI GPT-4 powered analysis with customizable prompts
5. **Template System** - Variable substitution and hierarchical template selection

### Data Flow

```
User Interactions → Decision Results → AI Analysis → P&L Display
     ↓                    ↓               ↓            ↓
  Sliders &          Selected         Custom       Enhanced
  Incentives         Values          Templates      P&L Data
```

## 🔧 How It Works

### 1. User Interaction Flow

1. **User Access**: Users access a simulation through a client-specific URL
2. **Decision Making**: Users interact with sliders and incentives on decision pages
3. **P&L Generation**: System collects user selections (FTE changes, investments)
4. **AI Analysis**: Custom prompts are used to generate AI-powered P&L analysis
5. **Results Display**: Enhanced P&L data with AI insights is shown to users

### 2. Template System

#### Template Hierarchy
- **Page-Level Templates**: Override scheme-level templates for specific pages
- **Scheme-Level Templates**: Global templates applied to all pages in a scheme
- **Default Templates**: Fallback templates when no custom templates are defined

#### Variable Substitution
Templates support dynamic variables using `{{VARIABLE_NAME}}` syntax:

- `{{PNL_DATA}}` - The raw P&L data being analyzed
- `{{TOTAL_FTE}}` - Total FTE changes selected by user
- `{{TOTAL_INVESTMENT}}` - Total investment amount
- `{{FORMATTED_INVESTMENT}}` - Formatted investment with currency symbols
- `{{ANALYSIS_INSTRUCTIONS}}` - Standard analysis instructions
- `{{RESPONSE_FORMAT}}` - Expected JSON response format
- `{{FORMAT_NOTE}}` - Additional formatting guidelines

### 3. Database Schema

#### Decision Schemes (`lumen_decision_scheme`)
```sql
scheme_id                    SERIAL PRIMARY KEY
scheme_name                  VARCHAR(253)
scheme_pnl                   JSONB
global_pnl_prompt_template   TEXT              -- Custom global template
use_custom_global_template   BOOLEAN           -- Enable/disable custom template
```

#### Decision Pages (`lumen_decision_page`)
```sql
page_id                     SERIAL PRIMARY KEY
scheme_id                   INTEGER REFERENCES lumen_decision_scheme
page_name                   VARCHAR(255)
sliders                     JSONB
incentives                  JSONB
page_pnl                    JSONB
page_pnl_prompt_template    TEXT              -- Custom page template
use_custom_page_template    BOOLEAN           -- Enable/disable custom template
```

#### Decision Results (`decision_results`)
```sql
id                    SERIAL PRIMARY KEY
user_id               INTEGER
decision_scheme_id    INTEGER
selected_values       JSONB                   -- User selections
total_fte             NUMERIC
total_investment      NUMERIC
ai_analysis           JSONB                   -- AI-generated analysis
```

## 🚀 API Endpoints

### P&L Analysis
```
POST /user/analyze-pnl
```

**Request Body:**
```json
{
  "pnlDataArray": [/* Array of P&L data objects */],
  "userSelections": {
    "totalFTE": 10,
    "totalInvestment": 500000
  },
  "schemeId": 123,
  "pages": [/* Array of page objects */]
}
```

**Response:**
```json
{
  "analyses": [
    {
      "updatedPnL": [/* Enhanced P&L data with AI adjustments */],
      "analysis": "AI-generated analysis text",
      "keyMetrics": {/* Key financial metrics */}
    }
  ]
}
```

## 📁 File Structure

```
api/
├── src/
│   ├── controllers/
│   │   ├── openai.js              # AI analysis endpoints
│   │   ├── decision.js            # Decision scheme management
│   │   └── decisionResults.js     # Results storage
│   ├── db/
│   │   ├── schema.js              # Database schema definitions
│   │   ├── queries/
│   │   │   ├── decision.js        # Decision-related queries
│   │   │   └── decisionResults.js # Results queries
│   │   └── migrations/
│   │       └── add_pnl_prompt_templates.sql
│   ├── utils/
│   │   └── prompts.js             # Prompt generation utilities
│   └── router.js                  # API route definitions
├── prompts/
│   ├── system.js                  # System prompts
│   └── pnl-templates.js          # P&L template constants
└── config.json                   # Configuration

web/
├── src/
│   ├── actions/
│   │   └── user.js               # API action creators
│   └── components/
│       └── Decisions/
│           ├── DecisionPNL.jsx   # Main P&L component
│           └── Decisions.jsx     # Decision interface
```

## 🎨 Frontend Components

### DecisionPNL Component
- Displays P&L data with AI analysis
- Handles regeneration of AI analysis
- Shows key metrics and charts
- Supports multiple P&L displays (main + pages)

### Key Features:
- **Real-time Analysis**: Generate AI analysis on demand
- **Multiple P&L Support**: Handle main P&L + individual page P&Ls
- **Key Metrics**: Extract and display important financial metrics
- **Interactive Charts**: Bar charts, pie charts, and waterfall charts
- **Responsive Design**: Works across different screen sizes

## 🔄 Template Processing Flow

1. **Template Selection**:
   ```javascript
   getPromptTemplate(schemeData, pagesData, pnlIndex, pages)
   ```

2. **Variable Substitution**:
   ```javascript
   substituteTemplateVariables(template, variables)
   ```

3. **AI Prompt Generation**:
   ```javascript
   generatePNLAnalysisPrompt({
     pnlData,
     userSelections,
     customTemplate,
     templateVariables
   })
   ```

## 📊 Example Template

```javascript
const CUSTOM_TEMPLATE = `
Analyze this P&L data for {{COMPANY_NAME}}:

P&L Data: {{PNL_DATA}}

Changes:
- FTE: {{TOTAL_FTE}} employees
- Investment: {{FORMATTED_INVESTMENT}}

Focus on {{BUSINESS_FOCUS}} metrics and provide insights on {{ANALYSIS_FOCUS}}.

{{ANALYSIS_INSTRUCTIONS}}
`;
```

## 🛠️ Setup & Deployment

### Prerequisites
- Node.js 18+
- PostgreSQL 12+
- OpenAI API Key

### Installation
1. Run database migration:
   ```bash
   cd api && npm run migrate
   ```

2. Configure OpenAI API key in `config.json`

3. Start services:
   ```bash
   # API
   cd api && npm run dev
   
   # Web Frontend  
   cd web && npm run dev
   ```

## 🔧 Configuration

### Environment Variables
- `OPENAI_API_KEY` - OpenAI API key for AI analysis
- `DATABASE_URL` - PostgreSQL connection string

### Default Template
The system includes a comprehensive default template that works out-of-the-box, ensuring backward compatibility while providing a foundation for customization.

## 🎯 Benefits

- **Customizable AI Analysis**: Tailor AI responses to specific business contexts
- **Hierarchical Templates**: Page-level customization with scheme-level fallbacks
- **Variable Substitution**: Dynamic content based on user interactions
- **Backward Compatibility**: Existing functionality remains unchanged
- **Scalable Architecture**: Clean separation of concerns and modular design
