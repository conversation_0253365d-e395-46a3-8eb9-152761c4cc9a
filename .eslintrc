{"parser": "@babel/eslint-parser", "parserOptions": {"ecmaVersion": 9, "sourceType": "module", "ecmaFeatures": {"jsx": true}, "babelOptions": {"presets": ["@babel/preset-react"]}, "requireConfigFile": false}, "env": {"browser": true}, "settings": {"react": {"version": "detect"}}, "globals": {"location": true, "Stripe": true, "gtag": true, "twq": true, "fbq": true, "twttr": true}, "extends": ["prettier"]}