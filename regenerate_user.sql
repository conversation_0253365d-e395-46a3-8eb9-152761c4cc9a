-- Simple Admin User Creation SQL Script
-- Run with: psql -d your_db -f regenerate_user.sql

-- Remove existing admin if exists
DELETE FROM lumen_admin WHERE LOWER(admin_email) = LOWER('<EMAIL>');

-- Create new admin user
INSERT INTO lumen_admin (
    admin_email,
    admin_password,
    admin_created_at
) VALUES (
    '<EMAIL>',
    MD5('2183-0jkl,dsaQW.Puihdjnvs'),
    current_timestamp
);

-- Show success message
SELECT 'Admin user created successfully!' as message;
SELECT admin_id, admin_email, admin_created_at
FROM lumen_admin
WHERE admin_email = '<EMAIL>';
