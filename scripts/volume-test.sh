#!/bin/bash

# Configuration
LOGIN_URL="https://api-simulation.1st90.com/user/signin"
WORKSHOPS_URL="https://api-simulation.1st90.com/user/workshops"
USER_EMAIL="<EMAIL>"
USER_PASSWORD="password"
REQUESTS_PER_CYCLE=500
SLEEP_DURATION=5
TOTAL_DURATION=60
CONCURRENT_SESSIONS=350

# Headers (constant)
HEADERS=(
  -H 'Accept: application/json, text/plain, */*'
  -H 'Content-Type: application/json'
)

# Function to login and retrieve the JWT token
get_jwt_token() {
  response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "${HEADERS[@]}" --data-raw "{\"email\":\"$USER_EMAIL\",\"password\":\"$USER_PASSWORD\"}" $LOGIN_URL)
  http_code=$(echo "$response" | grep "HTTP_CODE:" | awk -F':' '{print $2}')
  json_response=$(echo "$response" | sed '/HTTP_CODE/d')
  token=$(echo "$json_response" | grep -o '"token":"[^"]*' | sed 's/"token":"//')
  # echo "Login Response - HTTP Code: $http_code" #, Token: $token"
  echo $token
}

# Function to send requests to the workshops endpoint
send_workshops_requests() {
  for ((i=1; i<=$REQUESTS_PER_CYCLE; i++)); do
    response=$(curl -X GET -s -w "\nHTTP_CODE:%{http_code}" -H "Authorization: bearer $1" "${HEADERS[@]}" $WORKSHOPS_URL)
    # echo "-H 'Authorization: Bearer $1' ${HEADERS[@]}"
    # echo "$response"
    http_code=$(echo "$response" | grep "HTTP_CODE:" | awk -F':' '{print $2}')
    json_response=$(echo "$response" | sed '/HTTP_CODE/d' | head -c 150)
    echo "Regular Response $i - HTTP Code: $http_code, Response: $json_response"
  done
}

# Calculate the number of cycles based on total duration and sleep duration
CYCLES=$(($TOTAL_DURATION / $SLEEP_DURATION))

# Perform the volume test
simulate_session() {
  for ((cycle=1; cycle<=$CYCLES; cycle++)); do
    echo "Cycle $cycle: Logging in to retrieve JWT token..."
    jwt_token=$(get_jwt_token)

    if [ -z "$jwt_token" ]; then
      echo "Failed to retrieve JWT token. Exiting..."
      exit 1
    fi

    echo "JWT Token: $jwt_token"

    echo "Cycle $cycle: Sending $REQUESTS_PER_CYCLE requests to workshops endpoint..."
    send_workshops_requests "$jwt_token"

    echo "Sleeping for $SLEEP_DURATION seconds..."
    sleep $SLEEP_DURATION
  done
}

# Run the sessions concurrently
for ((session=1; session<=$CONCURRENT_SESSIONS; session++)); do
  simulate_session $session &
done

# Wait for all background processes to finish
wait

echo "Volume testing completed."