const axios = require('axios');
const fs = require('fs');
const path = require('path');

const sgMail = require('@sendgrid/mail');
sgMail.setApiKey("*********************************************************************"); // process.env.SENDGRID_API_KEY

// Function to call the exportPDF endpoint and retrieve the PDF
async function exportPDF(clientId, teamId) {
  try {
    const response = await axios.get(`http://localhost:8080/user/self-assessment-pdf?clientId=${clientId}&teamId=${teamId}`, {
      // params: { clientId, teamId },
      responseType: 'arraybuffer'
    });

    const pdfPath = path.join(__dirname, 'selfAssessment.pdf');

    fs.writeFileSync(pdfPath, response.data);

    return pdfPath;
  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw error;
  }
}

// Function to send an email with the PDF attached
async function sendEmail(clientId, teamId, pdfPath, recipientEmail) {
  const msg = {
    to: recipientEmail,
    // bcc: [],
    from: '<EMAIL>', // process.env.SENDGRID_SENDER,
    subject: 'Self Assessment PDF',
    dynamic_template_data: {
      subject: 'Download Your Self Assessment Report',
      client: {
        logo:
          'https://s3.amazonaws.com/lumen-simulation-api-files/uploads/5H40qaOkqilTjm',
      },
      name: 'ARMY National Guard',
      link: `https://api-simulation.1st90.com/user/self-assessment-pdf?clientId=${clientId}&teamId=${teamId}`,
    },
    templateId: 'd-1a8476d80e054a329e72fd7292245145',
    attachments: [
      {
        content: fs.readFileSync(pdfPath).toString('base64'),
        filename: 'selfAssessment.pdf',
        type: 'application/pdf',
        disposition: 'attachment'
      }
    ]
  };

  console.log('Sending email...', msg);

  try {
    await sgMail.send(msg);
    console.log('Email sent successfully');
  } catch (error) {
    console.error('Error sending email:', error);

    if (error.response) {
      console.error('Response body:', error.response.body);
    }

    throw error;
  }
}

// Main function to export PDF and send email
async function main() {
  const data = [
    // ['92', '2203', '<EMAIL>'],
    // ['92', '2080', '<EMAIL>'],

    ['92','2203','<EMAIL>'],
    ['92','2200','<EMAIL>'],
    ['92','2199','<EMAIL>'],
    ['92','2198','<EMAIL>'],
    ['92','2195','<EMAIL>'],
    ['92','2194','<EMAIL>'],
    ['92','2191','<EMAIL>'],
    ['92','2190','<EMAIL>'],
    ['92','2189','<EMAIL>'],
    ['92','2188','<EMAIL>'],
    ['92','2187','<EMAIL>'],
    ['92','2183','<EMAIL>'],
    ['92','2182','<EMAIL>'],
    ['92','2181','<EMAIL>'],
    ['92','2178','<EMAIL>'],
    ['92','2177','<EMAIL>'],
    ['92','2176','<EMAIL>'],
    ['92','2175','<EMAIL>'],
    ['92','2174','<EMAIL>'],
    ['92','2173','<EMAIL>'],
    ['92','2172','<EMAIL>'],
    ['92','2171','<EMAIL>'],
    ['92','2170','<EMAIL>'],
    ['92','2169','<EMAIL>'],
    ['92','2168','<EMAIL>'],
    ['92','2167','<EMAIL>'],
    ['92','2165','<EMAIL>'],
    ['92','2164','<EMAIL>'],
    ['92','2163','<EMAIL>'],
    ['92','2162','<EMAIL>'],
    ['92','2160','<EMAIL>'],
    ['92','2159','<EMAIL>'],
    ['92','2154','<EMAIL>'],
    ['92','2153','<EMAIL>'],
    ['92','2152','<EMAIL>'],
    ['92','2151','<EMAIL>'],
    ['92','2148','<EMAIL>'],
    ['92','2144','<EMAIL>'],
    ['92','2141','<EMAIL>'],
    ['92','2137','<EMAIL>'],
    ['92','2135','<EMAIL>'],
    ['92','2129','<EMAIL>'],
    ['92','2127','<EMAIL>'],
    ['92','2124','<EMAIL>'],
    ['92','2119','<EMAIL>'],
    ['92','2115','<EMAIL>'],
    ['92','2114','<EMAIL>'],
    ['92','2113','<EMAIL>'],
    ['92','2112','<EMAIL>'],
    ['92','2111','<EMAIL>'],
    ['92','2109','<EMAIL>'],
    ['92','2108','<EMAIL>'],
    ['92','2107','<EMAIL>'],
    ['92','2104','<EMAIL>'],
    ['92','2103','<EMAIL>'],
    ['92','2102','<EMAIL>'],
    ['92','2101','<EMAIL>'],
    ['92','2100','<EMAIL>'],
    ['92','2099','<EMAIL>'],
    ['92','2098','<EMAIL>'],
    ['92','2095','<EMAIL>'],
    ['92','2094','<EMAIL>'],
    ['92','2091','<EMAIL>'],
    ['92','2090','<EMAIL>'],
    ['92','2088','<EMAIL>'],
    ['92','2087','<EMAIL>'],
    ['92','2086','<EMAIL>'],
    ['92','2085','<EMAIL>'],
    ['92','2084','<EMAIL>'],
    ['92','2083','<EMAIL>'],
    ['92','2082','<EMAIL>'],
    ['92','2081','<EMAIL>'],
    ['92','2080','<EMAIL>'],
    ['92','2079','<EMAIL>'],
    ['92','2078','<EMAIL>'],
    ['92','2077','<EMAIL>'],
    ['92','2076','<EMAIL>'],
    ['92','2074','<EMAIL>'],
    ['92','2073','<EMAIL>'],
    ['92','2071','<EMAIL>'],
    ['92','2069','<EMAIL>'],
    ['92','2065','<EMAIL>'],
    ['92','2064','<EMAIL>'],
    ['92','2063','<EMAIL>'],
    ['92','2062','<EMAIL>'],
    ['92','2055','<EMAIL>'],
    ['92','2054','<EMAIL>'],
    ['92','2051','<EMAIL>'],
    ['92','2045','<EMAIL>'],
    ['92','2042','<EMAIL>'],
    ['92','2041','<EMAIL>'],
    ['92','2040','<EMAIL>'],
    ['92','2038','<EMAIL>'],
    ['92','2035','<EMAIL>'],
    ['92','2034','<EMAIL>'],
    ['92','2033','<EMAIL>'],
    ['92','2032','<EMAIL>'],
    ['92','2031','<EMAIL>'],
    ['92','2030','<EMAIL>'],
    ['92','2029','<EMAIL>'],
    ['92','2028','<EMAIL>'],
    ['92','2027','<EMAIL>'],
    ['92','2026','<EMAIL>'],
    ['92','2025','<EMAIL>'],
    ['92','2024','<EMAIL>'],
    ['92','2023','<EMAIL>'],
    ['92','2020','<EMAIL>'],
    ['92','2019','<EMAIL>'],
    ['92','2018','<EMAIL>'],
    ['92','2017','<EMAIL>'],
    ['92','2016','<EMAIL>'],
    ['92','2015','<EMAIL>'],
    ['92','2014','<EMAIL>'],
    ['92','2013','<EMAIL>'],
    ['92','2011','<EMAIL>'],
    ['92','2003','<EMAIL>'],
    ['92','2002','<EMAIL>'],
  ];

  for (const i in data) {
    const clientId = data[i][0];
    const teamId = data[i][1];
    const recipientEmail = data[i][2];

    try {
      const pdfPath = await exportPDF(clientId, teamId);

      if (pdfPath) {
        await sendEmail(clientId, teamId, pdfPath, recipientEmail);
      } else {
        console.error('PDF path not found for:', clientId, teamId);
      }
    } catch (error) {
      console.error(`Error processing clientId: ${clientId}, teamId: ${teamId}`, error);
    }
  }
}

main();