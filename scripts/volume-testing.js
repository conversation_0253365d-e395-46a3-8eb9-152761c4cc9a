// brew install k6  # macOS
// k6 run scripts/volume-testing.js

import http from 'k6/http';
import { check, sleep } from 'k6';

// List of users (replace these with real emails/passwords)
const users = [
    { email: "<EMAIL>", password: "<EMAIL>" },
    { email: "<EMAIL>", password: "<EMAIL>" },
    { email: "<EMAIL>", password: "<EMAIL>" },
    { email: "<EMAIL>", password: "<EMAIL>" },
    { email: "<EMAIL>", password: "<EMAIL>" },
    { email: "<EMAIL>", password: "<EMAIL>" },
    { email: "<EMAIL>", password: "<EMAIL>" },
    { email: "<EMAIL>", password: "<EMAIL>" },
    { email: "<EMAIL>", password: "<EMAIL>" },
    { email: "<EMAIL>", password: "<EMAIL>" }
];

export let options = {
    vus: users.length,  
    iterations: 10,
};

export default function () {
    let baseURL = "https://api-simulation.1st90.com";
    let user = users[__VU % users.length];

    let loginPayload = JSON.stringify({
        email: user.email,
        password: user.password
    });

    let loginHeaders = {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json'
    };

    let loginRes = http.post(`${baseURL}/user/signin`, loginPayload, { headers: loginHeaders });

    check(loginRes, { "Login successful": (r) => r.status === 200 });

    console.log(`DEBUG: Full Login Response: ${loginRes.body}`); // Logs response body to check structure

    let authToken = loginRes.json("token");
    if (!authToken) {
        console.error(`❌ Login failed for ${user.email}, no token found.`);
        return;
    }

    console.log(`✅ ${user.email} logged in with token: ${authToken}`);

    let authHeaders = {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
    };

    let endpoints = [
        "/user/workshops",
        "/user/welcome-page?clientId=118",
        "/user/team?clientId=118",
        "/user/team?clientId=118",
        "/user/strategic-initiatives?clientId=118",
        "/user/challenges?clientId=118",
        "/user/leaderboard?clientId=118",
        "/user/self-assessments?clientId=118"
    ];

    for (let endpoint of endpoints) {
        console.log(`DEBUG: Making request to ${baseURL}${endpoint}`); // with headers: ${JSON.stringify(authHeaders)}`);
        let response = http.get(`${baseURL}${endpoint}`, { headers: authHeaders });
        check(response, { [`${endpoint} loaded`]: (r) => r.status === 200 });

        // if (response.status !== 200) {
        //     console.error(`❌ Failed Request: ${endpoint} - Status ${response.status} - Response: ${response.body}`);
        // }
        sleep(0.5);
    }

    console.log(`🔄 ${user.email} completed API sequence`);
    sleep(1);
}

// k6 run ../api/scripts/volume-testing.js

//          /\      Grafana   /‾‾/  
//     /\  /  \     |\  __   /  /   
//    /  \/    \    | |/ /  /   ‾‾\ 
//   /          \   |   (  |  (‾)  |
//  / __________ \  |_|\_\  \_____/ 

//      execution: local
//         script: ../api/scripts/volume-testing.js
//         output: -

//      scenarios: (100.00%) 1 scenario, 10 max VUs, 10m30s max duration (incl. graceful stop):
//               * default: 10 iterations shared among 10 VUs (maxDuration: 10m0s, gracefulStop: 30s)

// INFO[0000] ✅ <EMAIL> logged in with token: eyJhbGciOiJIUzI1NiIs...  source=console
// INFO[0000] ✅ <EMAIL> logged in with token: eyJhbGciOiJIUzI1NiIs...  source=console
// INFO[0000] ✅ <EMAIL> logged in with token: eyJhbGciOiJIUzI1NiIs...  source=console
// INFO[0000] ✅ <EMAIL> logged in with token: eyJhbGciOiJIUzI1NiIs...  source=console
// INFO[0000] ✅ <EMAIL> logged in with token: eyJhbGciOiJIUzI1NiIs...  source=console
// INFO[0000] ✅ <EMAIL> logged in with token: eyJhbGciOiJIUzI1NiIs...  source=console
// INFO[0000] ✅ <EMAIL> logged in with token: eyJhbGciOiJIUzI1NiIs...  source=console
// INFO[0000] ✅ <EMAIL> logged in with token: eyJhbGciOiJIUzI1NiIs...  source=console
// INFO[0000] ✅ <EMAIL> logged in with token: eyJhbGciOiJIUzI1NiIs...  source=console
// INFO[0000] ✅ <EMAIL> logged in with token: eyJhbGciOiJIUzI1NiIs...  source=console
// INFO[0005] 🔄 <EMAIL> completed API sequence  source=console
// INFO[0005] 🔄 <EMAIL> completed API sequence  source=console
// INFO[0005] 🔄 <EMAIL> completed API sequence  source=console
// INFO[0005] 🔄 <EMAIL> completed API sequence  source=console
// INFO[0005] 🔄 <EMAIL> completed API sequence  source=console
// INFO[0005] 🔄 <EMAIL> completed API sequence  source=console
// INFO[0005] 🔄 <EMAIL> completed API sequence  source=console
// INFO[0005] 🔄 <EMAIL> completed API sequence  source=console
// INFO[0005] 🔄 <EMAIL> completed API sequence  source=console
// INFO[0005] 🔄 <EMAIL> completed API sequence  source=console

//      ✓ Login successful
//      ✗ /user/workshops loaded
//       ↳  0% — ✓ 0 / ✗ 10
//      ✗ /user/welcome-page?clientId=118 loaded
//       ↳  0% — ✓ 0 / ✗ 10
//      ✗ /user/team?clientId=118 loaded
//       ↳  0% — ✓ 0 / ✗ 20
//      ✗ /user/strategic-initiatives?clientId=118 loaded
//       ↳  0% — ✓ 0 / ✗ 10
//      ✗ /user/challenges?clientId=118 loaded
//       ↳  0% — ✓ 0 / ✗ 10
//      ✗ /user/leaderboard?clientId=118 loaded
//       ↳  0% — ✓ 0 / ✗ 10
//      ✗ /user/self-assessments?clientId=118 loaded
//       ↳  0% — ✓ 0 / ✗ 10

//      checks.........................: 11.11% 10 out of 90
//      data_received..................: 79 kB  13 kB/s
//      data_sent......................: 40 kB  6.4 kB/s
//      http_req_blocked...............: avg=36.37ms  min=0s       med=6µs      max=333.81ms p(90)=321.4ms  p(95)=325.17ms
//      http_req_connecting............: avg=11.86ms  min=0s       med=0s       max=110.31ms p(90)=102.76ms p(95)=106.69ms
//      http_req_duration..............: avg=103.58ms min=95.24ms  med=102.47ms max=118.66ms p(90)=109.57ms p(95)=113.98ms
//        { expected_response:true }...: avg=112.1ms  min=106.22ms med=112.27ms max=118.66ms p(90)=117.34ms p(95)=118ms   
//      http_req_failed................: 88.88% 80 out of 90
//      http_req_receiving.............: avg=42.48µs  min=13µs     med=31µs     max=298µs    p(90)=78.7µs   p(95)=134.59µs
//      http_req_sending...............: avg=33.22µs  min=3µs      med=24.5µs   max=178µs    p(90)=65.3µs   p(95)=95.34µs 
//      http_req_tls_handshaking.......: avg=24.36ms  min=0s       med=0s       max=223.36ms p(90)=216.41ms p(95)=217.62ms
//      http_req_waiting...............: avg=103.5ms  min=95.1ms   med=102.4ms  max=118.58ms p(90)=109.5ms  p(95)=113.84ms
//      http_reqs......................: 90     14.329805/s
//      iteration_duration.............: avg=6.26s    min=6.24s    med=6.27s    max=6.28s    p(90)=6.27s    p(95)=6.27s   
//      iterations.....................: 10     1.592201/s
//      vus............................: 10     min=10       max=10
//      vus_max........................: 10     min=10       max=10
