{"port": 8080, "bodyLimit": "100kb", "corsHeaders": ["Link"], "jwtPassword": "fdswerDSF", "SENDGRID_API_KEY": "SG.123", "OPENAI_API_KEY": "sk-123", "confirmationLinkSalt": "se234343er-hytn,", "appUrl": "http://localhost:3000", "apiUrl": "http://localhost:8080", "latency": 0, "admin": {"email": "<EMAIL>", "password": "password"}, "mail": {"service": "gmail", "user": "<EMAIL>", "pass": "fds324GS.LHJ2_389-47sdf,ahgd", "sender": "<PERSON><PERSON> <<EMAIL>>"}, "db": {"host": "localhost", "user": "postgres", "password": "postgres", "database": "lumen_db", "port": 5432}, "aws": {"bucketName": "lumen-simulation-api-files", "dirName": "uploads/", "region": "us-east-2", "accessKeyId": "********************", "secretAccessKey": "ixWTS2VOWqrfpvfEaTg/jSSX/FUj/msdFtVNoata"}}