{"name": "lumen-simulation-admin", "version": "0.1.1", "private": true, "dependencies": {"await-to-js": "^3.0.0", "axios": "~0.27.2", "bootstrap": "~3.3.7", "caniuse-lite": "^1.0.30001695", "classnames": "^2.3.1", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "formik": "^2.4.5", "guid": "~0.0.12", "html-to-draftjs": "^1.5.0", "immutable": "^5.1.1", "js-file-download": "~0.4.12", "lodash": "~4.17.21", "nanoid": "^4.0.0", "pdfjs-dist": "^2.16.105", "react": "~18.2.0", "react-beautiful-dnd": "^13.1.1", "react-block-ui": "~1.3.3", "react-bootstrap": "~0.31.5", "react-bs-notifier": "~6.0.0", "react-color": "^2.19.3", "react-dom": "~18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-easy-chart": "~1.0.0", "react-rangeslider": "~2.2.0", "react-redux": "~8.0.2", "react-router": "^6.3.0", "react-router-bootstrap": "~0.26.2", "react-router-dom": "~6.3.0", "react-scripts": "~5.0.1", "react-select": "^5.4.0", "react-use": "^17.4.0", "recharts": "~2.1.14", "redux": "~4.2.0", "redux-form": "~8.3.8", "redux-promise": "~0.6.0", "yup": "^1.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --env=jsdom", "eject": "react-scripts eject", "prettier": "prettier . --write"}, "devDependencies": {"@babel/eslint-parser": "^7.22.15", "@babel/preset-react": "^7.22.15", "@types/pdfjs-dist": "^2.10.378", "@types/react-bootstrap": "^0.32.32", "eslint-config-prettier": "^8.5.0", "node-sass": "^7.0.3", "prettier": "^2.7.1", "sass": "^1.83.4"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}