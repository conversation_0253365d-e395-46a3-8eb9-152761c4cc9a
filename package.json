{"name": "lumen-simulation-api", "version": "0.1.0", "private": true, "type": "module", "scripts": {"init": "babel-node ./ops/init_db.js", "migrate": "babel-node ./ops/migrate.js", "dev": "nodemon -w src --exec \"babel-node src\"", "start:dev": "tsx watch ./src/index.js", "staging": "pm2 start ecosystem.config.cjs", "build": "babel src -s -D -d dist", "start": "node dist", "prestart": "npm run -s build", "test": "eslint src", "mocha": "./node_modules/mocha/bin/mocha test/*.js --require babel-core/register --reporter spec", "prettier": "prettier . --write"}, "dependencies": {"@sendgrid/mail": "^7.6.0", "async": "3.2.4", "await-to-js": "^3.0.0", "aws-sdk": "2.1212.0", "axios": "0.27.2", "body-parser": "1.20.0", "compression": "1.7.4", "cors": "2.8.5", "csv-parser": "^3.0.0", "d3-node": "^3.0.0", "date-fns": "^2.30.0", "drizzle-orm": "^0.38.4", "express": "4.18.1", "express-validation": "4.1.0", "forever": "4.0.3", "joi": "17.6.0", "jsonwebtoken": "8.5.1", "lodash": "4.17.21", "md5": "2.3.0", "morgan": "1.10.0", "multer": "1.4.5-lts.1", "node-excel-export": "1.4.4", "openai": "^4.104.0", "pdf-lib": "^1.17.1", "pg": "8.8.0", "postgres": "^3.4.5", "puppeteer": "^21.3.8", "read-excel-file": "^5.4.7", "should": "^13.2.3", "tsx": "^4.19.2"}, "devDependencies": {"@babel/cli": "^7.23.0", "@babel/core": "^7.23.0", "@babel/node": "^7.22.19", "@babel/plugin-proposal-export-default-from": "^7.22.17", "@babel/plugin-syntax-import-attributes": "^7.22.5", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/preset-env": "^7.22.20", "@babel/preset-react": "^7.26.3", "babel-preset-es2015": "6.24.1", "babel-preset-stage-0": "6.24.1", "eslint": "8.23.0", "mocha": "^10.0.0", "nodemon": "^3.0.1", "prettier": "2.7.1"}, "main": "index.js", "repository": "https://<EMAIL>/bloodandtreasure/lumen-simulation-api.git", "author": "aminfarooq38 <<EMAIL>>", "license": "MIT"}