{"name": "lumen-simulation-app", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@vitejs/plugin-react": "^4.3.4", "await-to-js": "^3.0.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^10.16.4", "guid": "0.0.12", "lodash": "^4.17.21", "lodash.memoize": "^4.1.2", "lucide-react": "^0.471.0", "react": "~18.2.0", "react-dom": "~18.2.0", "react-easy-chart": "^0.3.0", "react-organizational-chart": "^2.2.1", "react-rangeslider": "~2.2.0", "react-redux": "~8.0.2", "react-router": "^6.3.0", "react-router-bootstrap": "~0.26.2", "react-router-dom": "~6.3.0", "react-tooltip": "^4.2.21", "recharts": "~2.1.14", "redux": "~4.2.0", "redux-form": "~8.3.8", "redux-promise": "~0.6.0", "sonner": "^1.7.1", "stickybits": "^3.7.11", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vite": "^6.0.7"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.10.5", "@types/react": "^19.0.4", "@types/react-dom": "^19.0.2", "autoprefixer": "^10.4.20", "node-sass": "^7.0.3", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5.7.3"}, "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}