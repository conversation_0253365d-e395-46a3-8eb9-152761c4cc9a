import axios from "axios";
import config from "../config.json" with { type: 'json' };
import should from "should";
const test = it;

const testData = {
  testTeam: {
    name: "Test team",
    email: "<EMAIL>",
    password: "test",
    goal1: "",
    goal2: "",
    goal3: "",
    revenueNumbers: [
      {
        fixed: true,
        name: "Net Interest Income",
        companyValue: 0,
        industryValue: 0,
        plannedValue: 0,
      },
      {
        fixed: true,
        name: "Non-interest Income",
        companyValue: 0,
        industryValue: 0,
        plannedValue: 0,
      },
      {
        fixed: true,
        name: "Revenue",
        companyValue: 0,
        industryValue: 0,
        plannedValue: 0,
      },
      {
        fixed: true,
        name: "Non-interest Expense",
        companyValue: 0,
        industryValue: 0,
        plannedValue: 0,
      },
      {
        fixed: true,
        name: "Net Charge Offset",
        companyValue: 0,
        industryValue: 0,
        plannedValue: 0,
      },
      {
        fixed: false,
        name: "Profitability",
        companyValue: 0,
        industryValue: 0,
        plannedValue: 0,
      },
      {
        fixed: false,
        name: "Deposits",
        companyValue: 0,
        industryValue: 0,
        plannedValue: 0,
      },
      {
        fixed: false,
        name: "Loans",
        companyValue: 0,
        industryValue: 0,
        plannedValue: 0,
      },
      {
        fixed: false,
        name: "Assets",
        companyValue: 0,
        industryValue: 0,
        plannedValue: 0,
      },
    ],
    metrics: [
      {
        fixed: false,
        name: "Customer Satisfaction",
        companyValue: 0,
        industryValue: 0,
      },
      {
        fixed: false,
        name: "Employee Engagement",
        companyValue: 0,
        industryValue: 0,
      },
      {
        fixed: false,
        name: "Regulatory Risk",
        companyValue: 0,
        industryValue: 0,
      },
      {
        fixed: false,
        name: "Efficiency Ratio",
        companyValue: 0,
        industryValue: 0,
      },
      {
        fixed: false,
        name: "Fee % of Revenue",
        companyValue: 0,
        industryValue: 0,
      },
    ],
  },

  testInitiativeScheme: {
    name: "Test Initiative Scheme",
    teamId: 0,
    initiativeNumber: 2,
    initiatives: [
      {
        name: "Test Initiative 1",
        description: "Test Initiative 1 description",
      },
    ],
  },

  testChallengeScheme: {
    name: "Test Challenge Scheme",
    teamId: 0,
    challenges: [
      {
        description: "Challenge description",
        optionA: "Option A",
        consequenceA: "Consequence A",
        optionB: "Option B",
        consequenceB: "Consequence B",
        optionC: "Option C",
        consequenceC: "Consequence C",
      },
    ],
  },

  testDecisionGroup: {
    name: "Test Decision Group",
  },
};

// ----------------
//   Test
// ----------------

describe("API - Lumen :", () => {
  // --
  // Suite Setup
  // --

  before((done) => {
    axios
      .post(`${config.apiUrl}/admin/signin`, {
        email: config.admin.email,
        password: config.admin.password,
      })
      .then(
        (r) => {
          r.status.should.equal(200);
          r.data.token.should.be.ok;
          testData.token = r.data.token;
          done();
        },
        (e) => {
          console.log("sign-in error: ", e);
          done(e);
        }
      );
  });

  test("test token refresh", (done) => {
    axios({
      method: "post",
      url: `${config.apiUrl}/admin/token/refresh`,
      data: {
        token: testData.token,
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        r.data.token.should.be.ok;
        testData.token = r.data.token;
        done();
      },
      (e) => {
        console.log("token refresh error: ", e);
        done(e);
      }
    );
  });

  test("create a team without a valid token (should fail)", (done) => {
    axios({
      method: "post",
      url: `${config.apiUrl}/admin/team`,
      headers: {
        Authorization: `bearer ##`,
      },
      data: {
        ...testData.testTeam,
      },
    }).then(
      (r) => {
        r.status.should.equal(401);
        done();
      },
      (e) => {
        e.should.be.ok;
        done();
      }
    );
  });

  test("create a team", (done) => {
    axios({
      method: "post",
      url: `${config.apiUrl}/admin/team`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
      data: {
        ...testData.testTeam,
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("list teams", (done) => {
    axios({
      method: "put",
      url: `${config.apiUrl}/admin/team/list`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
      data: {
        ascending: false,
        limit: 20,
        offset: 0,
        query: "",
        sort: "team_created_at",
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        r.data.should.be.ok;
        r.data[0].name.should.equal(testData.testTeam.name);
        r.data[0].id.should.be.ok;
        testData.testTeam.id = r.data[0].id;
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("update a team", (done) => {
    const data = {
      id: testData.testTeam.id,
      name: testData.testTeam.name + " updated",
      email: testData.testTeam.email,
      goal1: testData.testTeam.goal1,
      goal1: testData.testTeam.goal1,
      goal2: testData.testTeam.goal2,
      goal3: testData.testTeam.goal3,
      metrics: testData.testTeam.metrics,
      revenueNumbers: testData.testTeam.revenueNumbers,
    };
    axios({
      method: "put",
      url: `${config.apiUrl}/admin/team`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
      data: {
        ...data,
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("create an initiative scheme", (done) => {
    axios({
      method: "post",
      url: `${config.apiUrl}/admin/initiative`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
      data: {
        ...testData.testInitiativeScheme,
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("list initiative schemes", (done) => {
    axios({
      method: "put",
      url: `${config.apiUrl}/admin/initiative/list`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
      data: {
        ascending: false,
        limit: 20,
        offset: 0,
        query: "",
        sort: "scheme_created_at",
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        r.data.should.be.ok;
        r.data[0].name.should.equal(testData.testInitiativeScheme.name);
        r.data[0].id.should.be.ok;
        testData.testInitiativeScheme.id = r.data[0].id;
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("update an initiative scheme", (done) => {
    const data = {
      id: testData.testInitiativeScheme.id,
      name: testData.testInitiativeScheme.name + " updated",
      initiativeNumber: testData.testInitiativeScheme.initiativeNumber,
      initiatives: testData.testInitiativeScheme.initiatives,
    };
    axios({
      method: "put",
      url: `${config.apiUrl}/admin/initiative`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
      data: {
        ...data,
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("create a challenge scheme", (done) => {
    axios({
      method: "post",
      url: `${config.apiUrl}/admin/challenge`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
      data: {
        ...testData.testChallengeScheme,
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("list challenge schemes", (done) => {
    axios({
      method: "put",
      url: `${config.apiUrl}/admin/challenge/list`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
      data: {
        ascending: false,
        limit: 20,
        offset: 0,
        query: "",
        sort: "scheme_created_at",
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        r.data.should.be.ok;
        r.data[0].name.should.equal(testData.testChallengeScheme.name);
        r.data[0].id.should.be.ok;
        testData.testChallengeScheme.id = r.data[0].id;
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("update a challenge scheme", (done) => {
    const data = {
      id: testData.testChallengeScheme.id,
      name: testData.testChallengeScheme.name + " updated",
      challenges: testData.testChallengeScheme.challenges,
    };
    axios({
      method: "put",
      url: `${config.apiUrl}/admin/challenge`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
      data: {
        ...data,
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("reset team history", (done) => {
    axios({
      method: "post",
      url: `${config.apiUrl}/admin/team/reset-history`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
      data: {
        teamId: testData.testTeam.id,
        source: 'admin',
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("create a decision group", (done) => {
    axios({
      method: "post",
      url: `${config.apiUrl}/admin/decision-groups`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
      data: {
        ...testData.testDecisionGroup,
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        r.data.should.be.ok;
        r.data.name.should.equal(testData.testDecisionGroup.name);
        r.data.id.should.be.ok;
        testData.testDecisionGroup.id = r.data.id;
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("list all decision groups", (done) => {
    axios({
      method: "get",
      url: `${config.apiUrl}/admin/decision-groups`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        r.data.should.be.ok;
        r.data.should.be.an.Array();
        r.data.length.should.be.greaterThan(0);
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("get a decision group by id", (done) => {
    axios({
      method: "get",
      url: `${config.apiUrl}/admin/decision-groups/id/${testData.testDecisionGroup.id}`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        r.data.should.be.ok;
        r.data.name.should.equal(testData.testDecisionGroup.name);
        r.data.id.should.equal(testData.testDecisionGroup.id);
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  test("delete a decision group", (done) => {
    axios({
      method: "delete",
      url: `${config.apiUrl}/admin/decision-groups/id/${testData.testDecisionGroup.id}`,
      headers: {
        Authorization: `bearer ${testData.token}`,
      },
    }).then(
      (r) => {
        r.status.should.equal(200);
        r.data.should.be.ok;
        r.data.id.should.equal(testData.testDecisionGroup.id);
        done();
      },
      (e) => {
        done(e);
      }
    );
  });

  after((done) => {
    done();
  });
});
