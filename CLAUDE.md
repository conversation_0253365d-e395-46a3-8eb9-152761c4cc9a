# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Local Development
- `npm run dev` - Start Vite dev server on port 3000 (configured in vite.config.ts)
- `npm install` - Install dependencies
- `npm run build` - Build for production (outputs to /dist directory)
- `npm run serve` - Preview production build

### Deployment Commands
```bash
# Staging deployment to AWS S3
npm run build
aws s3 sync ./build s3://app-staging.simulation.lumenconsultinggroup.com --region us-east-1 --acl public-read --exclude .DS_Store

# Production deployment
aws s3 sync ./build s3://app.simulation.lumenconsultinggroup.com --region us-east-1 --acl public-read
aws cloudfront create-invalidation --distribution-id XXXXX --paths "/*"
```

## Project Architecture

### Tech Stack
- **React 18** with functional components and hooks
- **Redux** for state management (single user store)
- **React Router v6** for client-side routing
- **Vite** as build tool (replacing Create React App)
- **Tailwind CSS + Radix UI** for styling and components
- **TypeScript** (gradual adoption - tsconfig allows JS files)

### Project Structure
```
src/
├── components/          # React components organized by feature
│   ├── ui/             # Reusable Radix UI components (Button, Dialog, etc.)
│   ├── common/         # Shared components (Loader.tsx)
│   └── [FeatureName]/  # Feature-specific components (Dashboard, Challenges, etc.)
├── containers/         # Redux-connected component containers
├── actions/           # Redux actions (currently just user.js)
├── reducers/          # Redux reducers (user state management)
├── contexts/          # React contexts (PageBackgroundContext)
├── hooks/             # Custom hooks (useCreateNotification)
├── lib/               # Utility libraries (constants.ts, utils.ts)
├── utils/             # Helper functions (formatters.js)
└── store/             # Redux store configuration
```

### Key Architecture Patterns

#### State Management
- Single Redux store with user slice containing authentication, client config, and loading states
- Redux Promise middleware for async actions
- Client configuration determines UI theming and feature visibility

#### Routing & Navigation
- Container/Component pattern: Smart containers handle Redux, dumb components handle UI
- Routes defined in `routes.jsx` with main navigation through ScrollWrapperContainer
- Authentication-based routing (workshops selection → main app)

#### Component Architecture
- Mixed .jsx and .tsx files (gradual TypeScript migration)
- Radix UI components with Tailwind styling in `/ui` folder
- Feature components grouped by business domain (Dashboard, Challenges, Decisions, etc.)
- Theme system via ThemeProvider with client-specific customization

#### API Integration
- Axios for HTTP requests
- RESTful API endpoints (documented in README/TECHNICAL-ARCHITECTURE.md)
- JWT authentication with token-based user sessions

### Business Domain Features
The app is a business simulation platform with these core modules:
- **Workshops**: Multi-client simulation environments
- **Goals**: Team objective setting and tracking
- **Challenges**: Sequential interactive scenarios
- **Decisions**: P&L analysis with charting (Recharts)
- **Leaderboard**: Team rankings with regional filtering
- **Self Assessment**: PDF-exportable assessments
- **Org Chart**: Interactive organizational visualization
- **Metrics**: Real-time performance tracking

### Development Notes
- Vite dev server runs on port 3000 (not default 5173)
- Path alias `@/` maps to `src/` directory
- TypeScript strict mode disabled for gradual migration
- No testing framework currently configured
- Uses `bitbucket-pipelines.yml` for CI/CD to AWS S3

### File Extensions & Conventions
- Components use `.jsx` or `.tsx` extensions
- Utilities and libs prefer `.ts` for TypeScript
- CSS modules used occasionally (e.g., `Challenges.module.css`)
- Images stored in component-specific folders or `/img`