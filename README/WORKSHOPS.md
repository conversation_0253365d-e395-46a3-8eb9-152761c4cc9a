# Workshops System

The Workshops system is the gateway to different simulation environments in the Lumen Simulation App. After authentication, users select from available workshops, each representing a unique business simulation scenario with customized branding, content, and configuration.

## 🏢 Overview

The workshop selection system allows users to choose from multiple simulation environments, each tailored for specific learning objectives, industries, or organizational contexts. Each workshop has its own branding, background imagery, and feature configuration.

## 🎨 User Interface

### Design Features
- **Grid Layout**: Responsive grid displaying workshop cards
- **Workshop Cards**: Individual cards for each available workshop
- **Active Indicator**: Visual indicator showing the currently selected workshop
- **Loading States**: Smooth loading animations during data fetching
- **Responsive Design**: Optimized for all screen sizes

### Visual Elements
- Clean, modern card design with hover effects
- Workshop-specific branding and imagery
- Clear typography and consistent spacing
- Loading spinners and state indicators

## 🚀 Features

### 1. Workshop Selection Interface (`/workshops`)
**File**: `src/components/Workshops/Workshops.jsx`

#### Functionality
- Display all available workshops in a responsive grid
- Show active workshop with visual indicator
- Handle workshop selection and navigation
- Store selected workshop in localStorage and Redux state

#### User Flow
1. User lands on workshops page after authentication
2. System fetches available workshops from API
3. Workshops displayed in responsive grid layout
4. User clicks on desired workshop card
5. Workshop selection confirmed with toast notification
6. User automatically redirected to main simulation (`/home`)

### 2. Workshop Card Component
**File**: `src/components/Workshops/components/Workshop/Workshop.jsx`

#### Features
- Workshop name and description display
- Custom background imagery
- Interactive hover effects
- Selection state management
- Responsive design for all screen sizes

## 🔧 Technical Implementation

### Workshop Data Structure
```javascript
// Workshop object structure
{
  id: number,
  name: string,
  backgroundImage: string,
  // Client configuration
  homeTabName: string,
  homeTabVisibility: boolean,
  goalsTabName: string,
  goalsTabVisibility: boolean,
  strategicInitiativesTabName: string,
  initiativesTabVisibility: boolean,
  challengesTabName: string,
  challengesTabVisibility: boolean,
  leaderboardTabName: string,
  leaderboardTabVisibility: boolean,
  // Theming
  lightHighlightColor: string,
  darkHighlightColor: string,
  // Additional configuration...
}
```

### Workshop Selection Process
```javascript
function setCurrentWorkshop(id) {
  const relatedClient = workshops.find(
    ({ id: workshopId }) => workshopId === id
  );

  if (!relatedClient) return;

  localStorage.setItem("clientId", id);
  const { name, backgroundImage } = relatedClient;

  toast.success(`You have selected ${name} workshop!`);
  dispatch({ type: SET_CLIENT, payload: relatedClient });

  navigate("/home");
}
```

### Redux Integration
- Workshop data stored in Redux state as `client`
- Client configuration affects entire application
- Persistent storage in localStorage for session continuity

## 🎯 Workshop Configuration

### Feature Visibility Control
Each workshop can customize which features are available:
- **Welcome Page**: Custom welcome content and imagery
- **Goals**: Team goal setting functionality
- **Strategic Initiatives**: Initiative selection system
- **Challenges**: Interactive challenge scenarios
- **Leaderboard**: Team ranking and competition
- **OrgChart**: Organizational structure visualization
- **Self Assessment**: Assessment and evaluation tools
- **Decisions**: Decision-making scenarios

### Custom Branding
- **Tab Names**: Customizable names for each feature tab
- **Colors**: Custom highlight colors for theming
- **Background Images**: Workshop-specific background imagery
- **Content**: Tailored content for each simulation environment

### Example Configuration
```javascript
// Example workshop configuration
{
  name: "Healthcare Leadership Simulation",
  homeTabName: "Welcome to Healthcare Sim",
  goalsTabName: "Patient Care Goals",
  challengesTabName: "Clinical Challenges",
  lightHighlightColor: "#00A3E3",
  darkHighlightColor: "#004864",
  backgroundImage: "healthcare-bg.jpg"
}
```

## 📱 Responsive Design

### Grid Layout
- **Desktop**: 3 columns grid layout
- **Tablet**: 2 columns grid layout
- **Mobile**: Single column layout
- **Consistent Spacing**: Responsive gaps and padding

### Workshop Cards
- **Flexible Sizing**: Cards adapt to content and screen size
- **Touch Friendly**: Optimized for touch interactions
- **Hover Effects**: Enhanced desktop experience
- **Loading States**: Skeleton loading for better UX

## 🔄 State Management

### Workshop Selection State
```javascript
// Redux state structure
{
  user: {
    client: {
      id: number,
      name: string,
      // Full workshop configuration
    }
  }
}
```

### Persistence
- Selected workshop ID stored in `localStorage`
- Workshop configuration persists across browser sessions
- Automatic workshop restoration on app reload

## 🚀 Recent Updates

### UI/UX Improvements
- **Enhanced Grid Layout**: Improved responsive design with better spacing
- **Loading States**: Added skeleton loading and spinner animations
- **Active Indicators**: Clear visual feedback for selected workshop
- **Toast Notifications**: Success feedback on workshop selection

### Technical Enhancements
- **Performance**: Optimized workshop data fetching and rendering
- **Error Handling**: Improved error states and user feedback
- **State Management**: Enhanced Redux integration and persistence
- **Responsive Design**: Better mobile and tablet experience

## 🔗 Integration with Main App

### Navigation Flow
1. **Authentication** → **Workshops** → **Main Simulation**
2. Workshop selection determines available features
3. Custom branding applied throughout application
4. Feature visibility controlled by workshop configuration

### Feature Impact
- **Dashboard**: Layout and available sections determined by workshop
- **Navigation**: Tab names and visibility controlled by workshop
- **Theming**: Colors and styling customized per workshop
- **Content**: Workshop-specific content and imagery

## 🛠️ Development Notes

### Adding New Workshops
1. Create workshop configuration in admin panel
2. Upload workshop-specific assets (images, content)
3. Configure feature visibility and custom branding
4. Test workshop selection and feature availability

### Customization Options
- Feature tab names and visibility
- Custom color schemes and theming
- Background images and branding
- Content customization for each feature
- Metrics and assessment configuration

## 📊 Analytics & Tracking

The workshop system tracks:
- Workshop selection frequency
- User engagement per workshop type
- Feature usage patterns by workshop
- Performance metrics across different workshops

## 🔗 Related Components

- **Authentication**: Pre-requisite for workshop access
- **Dashboard**: Main interface customized by workshop selection
- **App Container**: Applies workshop configuration globally
- **Theme Provider**: Handles workshop-specific theming

---

*For technical implementation details, see [Technical Architecture](./TECHNICAL-ARCHITECTURE.md)*
