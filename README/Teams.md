# Teams Management

The Teams Management feature is the core user management system of the Lumen Simulation Admin Panel. It handles the creation, configuration, and management of team accounts that participate in simulations and assessments.

## Overview

Teams represent user accounts or groups of users who will participate in the simulation activities. Each team has login credentials and can be assigned to one or more clients to access their specific content and configurations.

## Key Features

### Team Account Management
- **Create Teams**: Set up new team accounts with unique credentials
- **Update Teams**: Modify team information and client assignments
- **View Teams**: Detailed view of team information and progress
- **Delete/Disable Teams**: Manage team status and access

### Client Assignment
- **Multi-Client Support**: Teams can be assigned to multiple clients
- **Dynamic Assignment**: Change client assignments as needed
- **Access Control**: Teams only see content from their assigned clients

### Progress Tracking
- **Self-Assessment Status**: Track completion of self-assessments
- **Decision Results**: Monitor decision-making activities
- **History Management**: View and manage team activity history

### Data Management
- **Export Functionality**: Export team data to Excel format
- **Bulk Import**: Import multiple teams via Excel files
- **Report Generation**: Generate PDF reports for team assessments

## User Interface

### Teams List View (`/teams`)
The main teams listing page provides:

**Search and Filtering**
- Text search across team names and emails
- Client-based filtering
- Status filtering (active/disabled teams)
- Configurable results per page (10, 25, 50, 100)

**Table Columns**
- Team Name
- Email Address
- Assigned Clients
- Created Date
- Status (Active/Disabled)
- Actions (View, Edit, Delete)

**Bulk Operations**
- Export all teams to Excel
- Import teams from Excel file
- Toggle show/hide disabled teams

### Add Team Form (`/teams/add`)
**Required Fields**
- Team Name: Display name for the team
- Email: Login email (must be unique)
- Password: Login password
- Client Assignment: Select one or more clients

**Validation Rules**
- Email must be unique across all teams
- Password must meet security requirements
- At least one client must be assigned

### View/Edit Team (`/teams/id/:teamId`)
**Team Information**
- Editable team name, email, and password
- Client assignment management
- Progress indicators

**Special Actions**
- **Reset History**: Clears all team progress across all clients
- **Download Report**: Generates PDF report of self-assessment results
- **Download Decision Results**: Exports decision-making activity data

**Status Indicators**
- Self-assessment completion status
- Report availability based on completion

## Data Structure

### Team Object
```javascript
{
  id: number,
  name: string,
  email: string,
  password: string, // Encrypted
  selectedClients: array, // Array of client IDs
  isCompletedSelfAssessment: boolean,
  created_at: timestamp,
  updated_at: timestamp,
  disabled: boolean
}
```

## Business Rules

### Team Creation
1. Email addresses must be unique across the entire system
2. Teams must be assigned to at least one client upon creation
3. Passwords are encrypted before storage
4. New teams start with no completed assessments

### Client Assignment
1. Teams can be assigned to multiple clients simultaneously
2. Removing a client assignment may affect team's access to related content
3. Client assignments can be modified at any time by administrators

### History Reset
1. Resetting team history is irreversible
2. Affects all progress across all assigned clients
3. Includes self-assessments, challenges, initiatives, and decisions
4. Requires confirmation before execution

### Report Generation
1. Self-assessment reports are only available after completion
2. Decision results require active client assignments
3. Reports are generated in real-time and not cached

## Integration Points

### With Clients
- Teams are assigned to clients to access client-specific content
- Client branding and configuration affects team experience
- Teams can be assigned to multiple clients simultaneously

### With Self-Assessments
- Teams complete self-assessments within their assigned clients
- Completion status is tracked per team
- Results can be exported as PDF reports

### With Decisions
- Teams participate in decision-making activities
- Results are tracked and can be exported
- Performance data feeds into leaderboards

### With Challenges and Initiatives
- Teams participate in challenges and strategic initiatives
- Progress is tracked and contributes to overall performance metrics
- History reset affects all participation data

## Export/Import Functionality

### Excel Export
- **Individual Team**: Export single team's data and progress
- **All Teams**: Bulk export of all team information
- **Includes**: Basic info, client assignments, progress status

### Excel Import
- **Bulk Team Creation**: Import multiple teams from Excel file
- **Required Columns**: Name, Email, Password, Client assignments
- **Validation**: Checks for duplicate emails and valid client references
- **Error Handling**: Reports validation errors for manual correction

### PDF Reports
- **Self-Assessment Reports**: Detailed assessment results and analysis
- **Decision Results**: Summary of decision-making activities and outcomes
- **Generated On-Demand**: Reports are created when requested, not pre-generated

## Security Considerations

### Authentication
- Teams use email/password authentication
- Passwords are encrypted using industry-standard methods
- Session management via JWT tokens

### Access Control
- Teams can only access content from their assigned clients
- Administrative functions require admin-level authentication
- History reset requires additional confirmation

### Data Protection
- Sensitive team data is encrypted
- Export functions require proper authorization
- Import validation prevents data corruption

## Common Workflows

### Setting Up New Teams
1. Navigate to Teams → Add Team
2. Enter team name, email, and password
3. Select client assignments
4. Submit form and verify creation
5. Team can now log in and access assigned client content

### Managing Team Progress
1. Navigate to Teams list
2. Use search/filters to find specific teams
3. Click "View" to see detailed team information
4. Monitor self-assessment completion and other progress indicators
5. Generate reports as needed

### Bulk Team Management
1. Prepare Excel file with team data
2. Use Import function to upload teams
3. Review and correct any validation errors
4. Use Export function to backup team data
5. Monitor team activity through the main listing

## Troubleshooting

### Common Issues
- **Duplicate Email Error**: Ensure email addresses are unique
- **Client Assignment Error**: Verify client exists and is active
- **Report Generation Failure**: Check if team has completed required assessments
- **Import Validation Errors**: Review Excel file format and data validity

### Performance Considerations
- Large team lists may require pagination
- Export operations may take time for large datasets
- History reset operations are permanent and cannot be undone
