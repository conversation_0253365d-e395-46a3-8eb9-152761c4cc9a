# UI/UX & Theming System

The Lumen Simulation App features a sophisticated, modern UI/UX design system built on glass morphism principles, comprehensive theming capabilities, and responsive design patterns that create an immersive and professional user experience.

## 🎨 Design Philosophy

### Modern Aesthetic
- **Glass Morphism**: Translucent elements with backdrop blur effects
- **Gradient Overlays**: Dynamic gradient backgrounds with theme awareness
- **Smooth Animations**: Framer Motion powered transitions and interactions
- **Clean Typography**: Consistent font hierarchy and spacing
- **Minimalist Approach**: Focus on content with reduced visual clutter

### User Experience Principles
- **Intuitive Navigation**: Clear user flows and logical information architecture
- **Immediate Feedback**: Real-time responses to user interactions
- **Accessibility First**: WCAG compliant design with keyboard navigation
- **Performance Focused**: Optimized for fast loading and smooth interactions
- **Mobile First**: Responsive design starting from mobile constraints

## 🌈 Theming System

### Theme Provider Architecture
**File**: `src/components/ThemeProvider/ThemeProvider.jsx`

#### Core Features
- **Dark/Light Mode Toggle**: User-selectable theme preferences
- **System Theme Detection**: Automatic detection of OS theme preference
- **Persistent Storage**: Theme preference saved in localStorage
- **Context-Based**: React Context for global theme state management

#### Implementation
```javascript
const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState(() => {
    const stored = localStorage.getItem('theme');
    return stored || 'dark'; // Default to dark theme
  });

  const toggleTheme = () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
```

### Workshop-Specific Theming
Each workshop can customize:
- **Primary Colors**: `lightHighlightColor` and `darkHighlightColor`
- **Background Images**: Custom workshop imagery
- **Brand Colors**: Consistent color schemes throughout the app
- **Component Styling**: Theme-aware component variations

## 🎭 Glass Morphism Design

### Visual Elements
- **Backdrop Blur**: `backdrop-blur-xl` effects on cards and modals
- **Translucent Backgrounds**: `bg-white/5` and `bg-black/10` overlays
- **Border Styling**: Subtle borders with `border-white/10` opacity
- **Shadow Effects**: Layered shadows for depth perception
- **Gradient Overlays**: Complex gradient combinations for visual interest

### Implementation Examples
```css
/* Glass morphism card styling */
.glass-card {
  @apply backdrop-blur-xl bg-white/5 border border-white/10 rounded-xl shadow-xl;
}

/* Theme-aware glass effects */
.glass-card-dark {
  @apply bg-white/5 border-white/10 text-white;
}

.glass-card-light {
  @apply bg-white/60 border-gray-300/30 text-gray-900;
}
```

## 🎬 Animation System

### Framer Motion Integration
**Key Features**:
- **Page Transitions**: Smooth transitions between different sections
- **Component Animations**: Enter/exit animations for dynamic content
- **Gesture Support**: Touch and mouse gesture handling
- **Performance Optimized**: Hardware-accelerated animations

### Animation Patterns
```javascript
// Standard card animation
const cardVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.5 }
};

// Stagger animations for lists
const containerVariants = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};
```

## 📱 Responsive Design System

### Breakpoint Strategy
- **Mobile First**: Design starts with mobile constraints
- **Tailwind Breakpoints**: `sm:`, `md:`, `lg:`, `xl:` responsive utilities
- **Flexible Layouts**: CSS Grid and Flexbox for adaptive layouts
- **Touch Optimization**: Enhanced touch targets and interactions

### Layout Patterns
```javascript
// Responsive grid example
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
  {items.map(item => (
    <Card key={item.id} className="w-full" />
  ))}
</div>
```

### Component Responsiveness
- **Adaptive Typography**: Responsive text sizing with `text-sm md:text-base lg:text-lg`
- **Flexible Spacing**: Responsive padding and margins
- **Layout Switching**: Different layouts for different screen sizes
- **Image Optimization**: Responsive images with proper aspect ratios

## 🎨 Component Design System

### Radix UI Integration
**Components Used**:
- **Accordion**: Collapsible content sections
- **Alert Dialog**: Modal confirmations and alerts
- **Checkbox**: Custom styled checkboxes
- **Dialog**: Modal windows and overlays
- **Dropdown Menu**: Context menus and dropdowns
- **Progress**: Progress bars and loading indicators
- **Tabs**: Tabbed interfaces
- **Tooltip**: Contextual help and information

### Custom UI Components
**File**: `src/components/ui/`

#### Button System
```javascript
// Button variants with theme support
const buttonVariants = {
  default: "bg-primary text-primary-foreground hover:bg-primary/90",
  destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
  outline: "border border-input bg-background hover:bg-accent",
  ghost: "hover:bg-accent hover:text-accent-foreground"
};
```

#### Card Components
- **Base Card**: Standard card with glass morphism styling
- **Interactive Cards**: Hover effects and click states
- **Content Cards**: Specialized layouts for different content types
- **Loading Cards**: Skeleton loading states

## 🌟 Interactive Elements

### Hover Effects
- **Smooth Transitions**: CSS transitions for all interactive elements
- **Scale Transforms**: Subtle scale effects on hover
- **Color Transitions**: Smooth color changes
- **Shadow Enhancements**: Dynamic shadow effects

### Loading States
- **Skeleton Screens**: Content placeholders during loading
- **Spinner Animations**: Lucide React loading spinners
- **Progressive Loading**: Staged content loading
- **Error States**: Clear error messaging with recovery options

## 🎯 Accessibility Features

### WCAG Compliance
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and roles
- **Focus Management**: Visible focus indicators
- **Color Contrast**: WCAG AA compliant color ratios
- **Alternative Text**: Comprehensive alt text for images

### Implementation
```javascript
// Accessible button example
<button
  aria-label="Toggle theme"
  aria-pressed={theme === 'dark'}
  className="focus:ring-2 focus:ring-blue-500 focus:outline-none"
  onClick={toggleTheme}
>
  {theme === 'dark' ? <Sun /> : <Moon />}
</button>
```

## 🚀 Performance Optimizations

### CSS Optimization
- **Tailwind CSS**: Utility-first CSS with purging unused styles
- **CSS Modules**: Scoped styling for component isolation
- **Critical CSS**: Above-the-fold CSS optimization
- **Lazy Loading**: Deferred loading of non-critical styles

### Animation Performance
- **Hardware Acceleration**: GPU-accelerated animations
- **Reduced Motion**: Respect for user motion preferences
- **Efficient Transitions**: Optimized animation properties
- **Frame Rate Optimization**: Smooth 60fps animations

## 🔧 Development Tools

### Styling Tools
- **Tailwind CSS**: Utility-first CSS framework
- **PostCSS**: CSS processing and optimization
- **CSS Modules**: Component-scoped styling
- **Tailwind Merge**: Dynamic class name merging

### Design Tokens
```javascript
// Theme configuration
const theme = {
  colors: {
    primary: {
      50: '#eff6ff',
      500: '#3b82f6',
      900: '#1e3a8a'
    },
    // Additional color scales...
  },
  spacing: {
    // Consistent spacing scale
  },
  typography: {
    // Font size and line height scales
  }
};
```

## 🎨 Recent Updates

### Major UI Enhancements
- **Glass Morphism Redesign**: Complete visual overhaul with modern glass effects
- **Enhanced Animations**: Framer Motion integration throughout the app
- **Improved Responsiveness**: Better mobile and tablet experiences
- **Theme System Upgrade**: More sophisticated theming capabilities

### Component Improvements
- **Radix UI Integration**: Professional, accessible component library
- **Custom UI Components**: Tailored components for specific use cases
- **Loading States**: Enhanced loading and skeleton screens
- **Error Handling**: Improved error states and user feedback

### Performance Enhancements
- **CSS Optimization**: Reduced bundle size and improved loading
- **Animation Performance**: Smoother, more efficient animations
- **Responsive Images**: Optimized image loading and display
- **Code Splitting**: Better component lazy loading

## 🔗 Integration Points

### Theme Context Usage
```javascript
// Using theme in components
const { theme } = useTheme();

return (
  <div className={`
    ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'}
    transition-colors duration-200
  `}>
    {/* Component content */}
  </div>
);
```

### Workshop Integration
- **Dynamic Theming**: Workshop-specific color schemes
- **Background Images**: Custom workshop imagery
- **Brand Consistency**: Consistent styling across workshop types
- **Configuration Driven**: Theme settings from workshop configuration

---

*For technical implementation details, see [Technical Architecture](./TECHNICAL-ARCHITECTURE.md)*
