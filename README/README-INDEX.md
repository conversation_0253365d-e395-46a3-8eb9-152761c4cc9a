# Lumen Simulation App - Documentation Index

This folder contains comprehensive documentation for all features and components of the Lumen Simulation App.

## 📚 Feature Documentation

### Core Features
- [**Authentication System**](./AUTHENTICATION.md) - Login, signup, password recovery
- [**Workshops**](./WORKSHOPS.md) - Workshop selection and environment setup
- [**Dashboard & Navigation**](./DASHBOARD.md) - Main interface and navigation system

### Simulation Features
- [**Goals**](./GOALS.md) - Team goal setting and management
- [**Strategic Initiatives**](./STRATEGIC-INITIATIVES.md) - Initiative selection with metrics impact
- [**Challenges**](./CHALLENGES.md) - Interactive challenge system with animations
- [**Decisions**](./DECISIONS.md) - Decision-making with P&L analysis
- [**Self Assessment**](./SELF-ASSESSMENT.md) - Assessment system with PDF export

### Analytics & Visualization
- [**Leaderboard**](./LEADERBOARD.md) - Team rankings and regional filtering
- [**OrgChart**](./ORGCHART.md) - Organizational structure visualization
- [**Metrics**](./METRICS.md) - Performance tracking across all features

### Technical Documentation
- [**UI/UX & Theming**](./UI-UX-THEMING.md) - Modern interface and theme system
- [**Technical Architecture**](./TECHNICAL-ARCHITECTURE.md) - Stack, APIs, and deployment

## 🚀 Quick Start

1. **Setup**: See main [README.md](../README.md) for installation instructions
2. **First Time Users**: Start with [Authentication](./AUTHENTICATION.md) and [Workshops](./WORKSHOPS.md)
3. **Feature Overview**: Check [Dashboard & Navigation](./DASHBOARD.md) for user flow
4. **Development**: See [Technical Architecture](./TECHNICAL-ARCHITECTURE.md)

## 📋 Documentation Standards

Each feature README includes:
- **Overview** - What the feature does
- **User Interface** - How users interact with it
- **Functionality** - Key capabilities and workflows
- **Technical Details** - Implementation specifics
- **Recent Changes** - Latest updates and improvements

## 🔄 Last Updated

This documentation was last updated on: **December 2024**

For the most current information, always refer to the source code and this documentation index.
