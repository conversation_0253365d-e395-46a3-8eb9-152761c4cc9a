# Challenges System

The Challenges system is one of the most sophisticated features of the Lumen Simulation App, providing an interactive, sequential challenge experience with real-time metrics tracking, smooth animations, and immediate feedback on decision consequences.

## ⚡ Overview

The Challenges feature presents users with a series of business scenarios where they must make strategic decisions. Each challenge offers multiple options (A, B, C), and selections immediately impact team metrics. The system features a modern, animated interface with sequential progression through challenges.

## 🎨 User Interface

### Design Features
- **Sequential Presentation**: One active challenge at a time with smooth transitions
- **Framer Motion Animations**: Smooth enter/exit animations for challenge cards
- **Real-time Metrics**: Live metrics panel showing impact of decisions
- **Consequence Revelation**: Immediate display of decision consequences
- **Progress Tracking**: Visual indication of completed vs. remaining challenges
- **Responsive Layout**: Split-screen design with metrics sidebar

### Visual Elements
- Glass morphism cards with backdrop blur
- Smooth animations between challenge states
- Color-coded option selection
- Consequence boxes with highlighted text
- Modern checkbox interactions
- Theme-aware styling (dark/light mode)

## 🚀 Features

### 1. Interactive Challenge Interface
**File**: `src/components/Challenges/Challenges.jsx`

#### Core Functionality
- **Sequential Progression**: Challenges presented one at a time
- **Option Selection**: Radio-button style selection (only one option per challenge)
- **Immediate Feedback**: Consequences displayed upon selection
- **Metrics Integration**: Real-time impact on team performance metrics
- **Auto-progression**: Automatic advancement to next challenge after selection

#### Challenge Structure
```javascript
// Challenge data structure
{
  id: number,
  description: string,
  optionA: string,
  optionB: string,
  optionC: string,
  consequenceA: string,
  consequenceB: string,
  consequenceC: string,
  optionMetric1A: number, // Impact on Metric 1
  optionMetric1B: number,
  optionMetric1C: number,
  // Additional metric impacts...
  selectedA: boolean,
  selectedB: boolean,
  selectedC: boolean,
  submitted: boolean
}
```

### 2. Real-time Metrics Integration
The challenges system dynamically calculates and displays metrics impact:

```javascript
// Metrics calculation logic
globalTeamMetrics.forEach(metric => {
  const [, metricIndex] = metric.alias.split(" ");
  const alias = `optionMetric${metricIndex}${option.toUpperCase()}`;
  metric.value = (metric.value || 0) + (challenge[alias] || 0);
});
```

### 3. Animation System
**Powered by Framer Motion**

#### Animation Features
- **Enter Animations**: Smooth fade-in and slide-up for new challenges
- **Exit Animations**: Fade-out and slide-down for completed challenges
- **State Transitions**: Smooth transitions between challenge states
- **Loading States**: Animation during data processing

#### Animation Implementation
```javascript
<motion.div
  key={challenge.id}
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  exit={{ opacity: 0, y: -20 }}
  transition={{ duration: 0.5 }}
>
  {/* Challenge content */}
</motion.div>
```

## 🔧 Technical Implementation

### State Management
```javascript
const [challengeData, setChallengeData] = useState({
  challengeNumber: 0,
  challenges: [],
  globalTeamMetrics: [],
  currentChallengeId: null,
});
const [activeChallenge, setActiveChallenge] = useState(0);
const [animating, setAnimating] = useState(false);
```

### Option Selection Logic
```javascript
const handleOptionChange = async (option, challengeIndex, checked) => {
  // Prevent multiple selections
  if (!checked || challenge.submitted) return;

  // Reset all options for this challenge
  challenge.selectedA = false;
  challenge.selectedB = false;
  challenge.selectedC = false;
  challenge[`selected${option}`] = checked;
  challenge.submitted = true;

  // Recalculate metrics
  // Save to backend
  // Animate to next challenge
};
```

### Metrics Recalculation
The system recalculates all metrics whenever a selection is made:
1. Reset all metric values to 0
2. Iterate through all challenges
3. Add metric impacts for selected options
4. Update UI with new values

## 📊 Metrics Integration

### Real-time Impact Display
- **Sidebar Metrics Panel**: Shows current team performance
- **Dynamic Updates**: Metrics update immediately upon selection
- **Visual Feedback**: Pie charts show percentage completion
- **Color Coding**: Workshop-specific colors for metrics display

### Metrics Calculation
Each challenge option has specific impacts on different metrics:
- **Metric 1**: Could represent "Customer Satisfaction"
- **Metric 2**: Could represent "Financial Performance"
- **Metric 3**: Could represent "Team Morale"
- Values can be positive or negative based on decision quality

## 🎯 User Experience Flow

### Challenge Progression
1. **Initial Load**: All challenges loaded, first challenge displayed
2. **Decision Making**: User reads scenario and evaluates options
3. **Option Selection**: User selects one option (A, B, or C)
4. **Consequence Display**: Immediate feedback on decision impact
5. **Metrics Update**: Real-time metrics recalculation and display
6. **Auto-progression**: Smooth animation to next challenge
7. **Completion**: Final summary when all challenges completed

### Visual Feedback
- **Selection State**: Selected options highlighted with custom styling
- **Consequence Boxes**: Expandable sections showing decision outcomes
- **Progress Indication**: Completed challenges shown in collapsed state
- **Completion Message**: Final congratulations message

## 🎨 Styling System

### CSS Modules
**File**: `src/components/Challenges/Challenges.module.css`

#### Key Style Features
- **Responsive Grid**: Metrics sidebar and main content area
- **Card Animations**: Hover effects and transitions
- **Theme Support**: Dark and light mode variations
- **Interactive Elements**: Custom checkbox and button styling

### Theme Integration
```javascript
const { theme } = useTheme();

// Apply theme-specific classes
className={`${styles.challengeCard} ${
  theme === 'dark' 
    ? `${styles.darkCard} ${styles.darkCardHover}` 
    : `${styles.lightCard} ${styles.lightCardHover}`
}`}
```

## 🚀 Recent Updates

### Major Enhancements
- **Sequential Presentation**: Redesigned from multi-challenge view to one-at-a-time
- **Framer Motion Integration**: Added smooth animations throughout
- **Enhanced Metrics**: Real-time calculation and display improvements
- **Consequence System**: Immediate feedback on decision impacts
- **Auto-progression**: Automatic advancement between challenges

### UI/UX Improvements
- **Modern Card Design**: Glass morphism with backdrop blur
- **Improved Animations**: Smooth transitions and state changes
- **Better Feedback**: Clear consequence display and metrics impact
- **Responsive Design**: Enhanced mobile and tablet experience

### Performance Optimizations
- **Efficient Rendering**: Optimized component re-renders
- **State Management**: Improved state updates and calculations
- **Animation Performance**: Smooth 60fps animations
- **Memory Usage**: Efficient component lifecycle management

## 📱 Responsive Design

### Layout Adaptation
- **Desktop**: Side-by-side metrics and challenge layout
- **Tablet**: Stacked layout with collapsible metrics
- **Mobile**: Single-column layout with optimized spacing
- **Touch Optimization**: Enhanced touch targets and interactions

## 🔗 Integration Points

### Backend Integration
- **Challenge Data**: Fetched from `/user/challenges` endpoint
- **Progress Saving**: Automatic saving of selections to backend
- **Metrics Sync**: Real-time synchronization with server
- **Team Data**: Integration with team performance tracking

### Component Integration
- **Metrics Component**: Shared metrics display system
- **Theme Provider**: Consistent theming across interface
- **Toast Notifications**: Success/error feedback system
- **Loading States**: Integrated loading and error handling

## 📊 Analytics & Tracking

The challenges system tracks:
- **Decision Patterns**: Which options are selected most frequently
- **Completion Rates**: How many users complete all challenges
- **Time Metrics**: Time spent on each challenge
- **Performance Impact**: Correlation between decisions and final metrics

## 🔗 Related Components

- **Metrics System**: Real-time performance tracking
- **Dashboard**: Integration with main navigation
- **Strategic Initiatives**: Related decision-making features
- **Leaderboard**: Performance comparison with other teams

---

*For technical implementation details, see [Technical Architecture](./TECHNICAL-ARCHITECTURE.md)*
