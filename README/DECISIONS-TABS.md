# Decision Groups Tab Interface

## Overview
The Decisions component now supports displaying decision groups as horizontal tabs across the top of the component instead of using dropdown menus within the list view.

## Features

### Tab-Based Group Navigation
- **Multiple Groups**: When a decision page contains sliders with multiple groups, tabs are displayed at the top
- **Single Group/No Groups**: Falls back to the original accordion-based interface for backward compatibility
- **"Other" Tab**: Automatically created for ungrouped sliders when some sliders have groups but others don't

### Implementation Details

#### New State Management
```javascript
const [activeGroupTab, setActiveGroupTab] = useState('');
```

#### Helper Functions
- `getGroupTabs(sliders)`: Returns array of available tab names
- `getSlidersForActiveTab(sliders, activeTab)`: Filters sliders for the active tab
- `groupSliders(sliders)`: Original grouping function (maintained for compatibility)

#### Tab Interface Components
- `FilterButton`: Reusable tab button component with theme support
- Automatic tab initialization when page changes
- Responsive design matching existing UI patterns

### User Experience Improvements
1. **Prominent Navigation**: Groups are now immediately visible as tabs
2. **Reduced Clicks**: No need to open dropdowns to access different groups
3. **Clear Organization**: Visual separation of grouped vs ungrouped content
4. **Consistent Styling**: Matches existing design patterns from OrgChart component

### Backward Compatibility
- Pages with no groups: Display sliders normally
- Pages with single group: Use accordion interface
- Pages with multiple groups: Use new tab interface
- Existing functionality preserved for all scenarios

### Technical Implementation
The component intelligently switches between rendering modes:

```javascript
if (availableTabs.length > 1) {
  // Tab-based rendering for multiple groups
  const slidersForTab = getSlidersForActiveTab(currentPage.sliders, activeGroupTab);
  // Render sliders for active tab
} else {
  // Original accordion-based rendering
  // Maintains existing grouped/ungrouped logic
}
```

## Configuration
No additional configuration required. The feature automatically activates when:
- A decision page contains sliders with multiple different group values
- Some sliders have groups while others don't (creates "Other" tab)

## Memory Preferences Applied
- Displays page names rather than tab names in the decision feature interface
- Maintains existing FTE limit display preferences
- Follows established UI/UX patterns from the codebase
