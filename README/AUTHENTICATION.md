# Authentication System

The Lumen Simulation App features a comprehensive authentication system that provides secure access to the simulation platform with modern UI/UX design.

## 🔐 Overview

The authentication system handles user registration, login, password recovery, and session management with JWT tokens. It features a modern glass morphism design with responsive layouts and comprehensive error handling.

## 🎨 User Interface

### Design Features
- **Glass Morphism Design**: Translucent cards with backdrop blur effects
- **Responsive Layout**: Optimized for desktop, tablet, and mobile devices
- **Modern Icons**: Lucide React icons for visual clarity
- **Loading States**: Smooth loading animations and disabled states
- **Error Handling**: Clear error messages with toast notifications

### Visual Elements
- Gradient backgrounds with dynamic theming
- Smooth transitions and hover effects
- Consistent spacing and typography
- Accessible form controls with proper labeling

## 🚀 Features

### 1. User Login (`/`)
**File**: `src/components/Login/Login.jsx`

#### Functionality
- Email and password authentication
- Form validation with real-time feedback
- "Remember me" checkbox functionality
- Automatic redirect to workshops after successful login
- JWT token storage in sessionStorage

#### User Flow
1. User enters email and password
2. Form validates input fields
3. Authentication request sent to backend
4. On success: JWT token stored, user redirected to `/workshops`
5. On error: Clear error message displayed

#### Key Features
- Case-insensitive email handling (automatically converts to lowercase)
- Loading states with spinner animation
- Comprehensive error handling with user-friendly messages
- Link to forgot password functionality

### 2. User Registration (`/sign-up`)
**File**: `src/components/SignUp/SignUp.jsx`

#### Functionality
- New user account creation
- Form validation for all required fields
- Password confirmation matching
- Automatic login after successful registration

#### Form Fields
- Full name
- Email address
- Password
- Password confirmation
- Terms and conditions acceptance

### 3. Forgot Password (`/forgot-password`)
**File**: `src/components/ForgotPassword/ForgotPassword.jsx`

#### Functionality
- Password reset request by email
- Email validation
- Confirmation message after successful request
- Integration with email service (SendGrid)

#### User Flow
1. User enters email address
2. System validates email format
3. Password reset email sent to user
4. Confirmation message displayed
5. User receives email with reset link

### 4. Password Recovery (`/recover-password/:teamId/:code`)
**File**: `src/components/RecoverPassword/RecoverPassword.jsx`

#### Functionality
- Secure password reset with token validation
- New password setting
- Password confirmation matching
- Automatic redirect after successful reset

#### Security Features
- Token-based validation (teamId and code parameters)
- Secure password requirements
- Token expiration handling
- One-time use tokens

## 🔧 Technical Implementation

### Authentication Flow
```javascript
// Login process
const handleSubmit = async (event) => {
  event.preventDefault();
  setIsLoading(true);
  
  const body = {
    email: formData.emailInput.toLowerCase(),
    password: formData.passwordInput,
  };

  const [err, res] = await to(signIn(body).payload);
  
  if (err) {
    // Handle error
    return;
  }

  const { token, user } = res.data;
  sessionStorage.setItem("jwtToken", token);
  dispatch(signInUserSuccess({ user }));
  navigate("/workshops");
};
```

### JWT Token Management
- Tokens stored in `sessionStorage` for security
- Automatic token validation on protected routes
- Token refresh handling
- Secure logout with token cleanup

### Redux Integration
- User state management with Redux
- Authentication actions and reducers
- Persistent user session handling
- Global authentication state

## 🛡️ Security Features

### Password Security
- Secure password transmission
- Server-side password hashing
- Password strength requirements
- Protection against common attacks

### Token Security
- JWT tokens with expiration
- Secure token storage
- Token validation middleware
- Automatic token cleanup on logout

### Form Security
- Input sanitization
- CSRF protection
- Rate limiting on authentication endpoints
- Secure error handling (no sensitive data exposure)

## 📱 Responsive Design

### Mobile Optimization
- Touch-friendly form controls
- Optimized keyboard navigation
- Responsive typography and spacing
- Mobile-first design approach

### Accessibility
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- High contrast support

## 🔄 Recent Updates

### UI/UX Improvements
- **Glass Morphism Design**: Modern translucent card design with backdrop blur
- **Enhanced Loading States**: Smooth loading animations with Lucide icons
- **Improved Error Handling**: Clear, user-friendly error messages
- **Responsive Enhancements**: Better mobile and tablet experience

### Technical Enhancements
- **Toast Notifications**: Sonner integration for better user feedback
- **Form Validation**: Enhanced client-side validation
- **Security Updates**: Improved token handling and security measures
- **Performance**: Optimized component rendering and state management

## 🚀 Usage Examples

### Basic Login
```jsx
// User enters credentials and submits form
// System validates and authenticates
// Successful login redirects to workshops
```

### Password Recovery
```jsx
// User requests password reset
// Email sent with secure token
// User clicks link and sets new password
// Automatic redirect to login
```

## 🔗 Related Components

- **App Container**: Main application wrapper with authentication checks
- **Navbar**: Authentication status and logout functionality
- **Protected Routes**: Route guards for authenticated users
- **Workshop Selection**: Post-authentication landing page

## 📊 Analytics & Monitoring

The authentication system includes:
- Login success/failure tracking
- Password reset request monitoring
- User registration analytics
- Security event logging

---

*For technical implementation details, see [Technical Architecture](./TECHNICAL-ARCHITECTURE.md)*
