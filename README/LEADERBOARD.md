# Leaderboard System

The Leaderboard system provides competitive elements to the simulation by displaying team rankings, regional comparisons, and performance metrics. It features a sophisticated ranking system with visual podium displays and regional filtering capabilities.

## 🏆 Overview

The Leaderboard creates engagement through competition by showing how teams perform relative to each other. It supports multiple regions, custom scoring systems, and provides both top performer highlights and comprehensive rankings.

## 🎨 User Interface

### Design Features
- **Podium Display**: Special highlighting for top 3 performers
- **Regional Filtering**: Filter rankings by geographic or organizational regions
- **Tiered Layout**: Distinguished display for top candidates vs. others
- **Performance Metrics**: Points-based ranking system
- **Responsive Cards**: Modern card-based layout for each participant

### Visual Elements
- **Trophy Icons**: Gold, silver, bronze styling for top 3 positions
- **Gradient Badges**: Color-coded position indicators
- **Glass Morphism Cards**: Translucent cards with backdrop blur
- **Regional Filters**: Interactive filter buttons
- **Performance Indicators**: Points display and ranking positions

## 🚀 Features

### 1. Leaderboard Display Interface
**File**: `src/components/Leaderboard/Leaderboard.jsx`

#### Core Functionality
- **Regional Filtering**: Filter participants by region/category
- **Top 3 Highlighting**: Special podium-style display for top performers
- **Comprehensive Rankings**: Full list of all participants
- **Real-time Updates**: Dynamic ranking updates based on performance
- **Responsive Design**: Optimized for all screen sizes

#### Data Structure
```javascript
// Leaderboard data structure
{
  regions: [
    {
      id: number,
      name: string,
      users: [
        {
          id: number,
          name: string,
          type: string,
          points: number
        }
      ]
    }
  ]
}
```

### 2. Top Candidate Component
**File**: `src/components/Leaderboard/components/TopCandidate/TopCandidate.jsx`

#### Features
- **Position-based Styling**: Gold, silver, bronze color schemes
- **Trophy Icons**: Lucide React trophy icons
- **Gradient Backgrounds**: Position-specific gradient styling
- **Points Display**: Prominent points/score display
- **Responsive Layout**: Adapts to different screen sizes

#### Styling System
```javascript
const getPlaceStyles = (place) => {
  switch (place) {
    case 1:
      return {
        background: "rgba(255, 215, 0, 0.08)",
        border: "border-yellow-500/20",
        trophyColor: "#FFD700",
        badgeGradient: "from-yellow-400 to-yellow-500"
      };
    case 2:
      return {
        background: "rgba(192, 192, 192, 0.08)",
        border: "border-gray-400/20",
        trophyColor: "#C0C0C0",
        badgeGradient: "from-gray-300 to-gray-400"
      };
    // Bronze styling for 3rd place...
  }
};
```

### 3. Regional Filtering System
**File**: `src/components/FiltersGroup/FiltersGroup.jsx`

#### Functionality
- **Dynamic Filter Generation**: Filters created from available regions
- **Active State Management**: Visual indication of selected filter
- **Workshop Theming**: Custom colors based on workshop configuration
- **Responsive Layout**: Adapts to different screen sizes

## 🔧 Technical Implementation

### State Management
```javascript
const [currentLeaderboardRegionId, setCurrentLeaderboardRegionId] = useState(-1);
const [leaderboard, setLeaderboard] = useState({});

// Filter logic for selected region
const selectedLeaderboardRegion = useMemo(() => {
  if (currentLeaderboardRegionId === -1) {
    // Show all regions combined
    return {
      users: leaderboard?.regions?.flatMap(region => region.users) || []
    };
  }
  return leaderboard?.regions?.find(region => region.id === currentLeaderboardRegionId);
}, [leaderboard, currentLeaderboardRegionId]);
```

### Ranking Logic
```javascript
// Separate top 3 from other candidates
const topCandidates = selectedLeaderboardRegion?.users?.slice(0, 3) || [];
const notTopCandidates = selectedLeaderboardRegion?.users?.slice(3) || [];
```

### Data Fetching
```javascript
const fetchLeaderboard = async () => {
  setIsLoading(true);
  const [err, res] = await to(getLeaderboard().payload);

  if (err) {
    toast.error("Failed to load leaderboard");
    setIsLoading(false);
    return;
  }

  setLeaderboard(res.data);
  setIsLoading(false);
};
```

## 🎯 Regional System

### Multi-Region Support
- **Geographic Regions**: Different geographical areas
- **Organizational Units**: Different departments or divisions
- **Custom Categories**: Workshop-specific groupings
- **Combined View**: Option to view all regions together

### Filter Implementation
```javascript
const filters = useMemo(() => {
  if (!leaderboard?.regions?.length) return [];
  return leaderboard.regions.map(({ id, name }) => ({
    value: id,
    label: name,
  }));
}, [leaderboard]);
```

## 🏅 Ranking System

### Points-Based Scoring
- **Performance Metrics**: Points awarded based on simulation performance
- **Challenge Completion**: Points for completing challenges
- **Decision Quality**: Points based on decision outcomes
- **Time Factors**: Bonus points for timely completion

### Ranking Display
- **Position Numbers**: Clear ranking positions (1st, 2nd, 3rd, etc.)
- **Points Display**: Total points earned by each participant
- **Visual Hierarchy**: Distinguished styling for different rank levels
- **Tie Handling**: Consistent handling of tied scores

## 🎨 Visual Design

### Top 3 Podium
- **Gold (1st Place)**: Yellow/gold color scheme with trophy icon
- **Silver (2nd Place)**: Silver/gray color scheme
- **Bronze (3rd Place)**: Bronze/copper color scheme
- **Special Layout**: Larger cards with enhanced styling

### Other Participants
- **Consistent Cards**: Uniform styling for positions 4+
- **Clear Hierarchy**: Position numbers and points clearly displayed
- **Hover Effects**: Interactive hover states
- **Responsive Grid**: Adapts to screen size

## 📱 Responsive Design

### Layout Adaptation
- **Desktop**: Multi-column layout for top 3, list view for others
- **Tablet**: Adjusted spacing and card sizes
- **Mobile**: Single-column layout with optimized touch targets
- **Flexible Cards**: Cards adapt to content and screen constraints

### Filter Responsiveness
- **Desktop**: Horizontal filter bar
- **Mobile**: Responsive filter buttons with proper spacing
- **Touch Optimization**: Enhanced touch targets for mobile users

## 🚀 Recent Updates

### UI/UX Improvements
- **Enhanced Visual Hierarchy**: Better distinction between top performers and others
- **Improved Filtering**: More intuitive regional filtering system
- **Modern Card Design**: Glass morphism with backdrop blur effects
- **Better Mobile Experience**: Optimized for touch interactions

### Performance Enhancements
- **Efficient Rendering**: Optimized component re-renders
- **Memoized Calculations**: Cached filtering and sorting operations
- **Lazy Loading**: Performance optimization for large leaderboards
- **State Management**: Improved state updates and data flow

## 🔗 Backend Integration

### API Endpoints
- **GET /user/leaderboard**: Fetch leaderboard data with regions and users
- **Real-time Updates**: Periodic refresh of leaderboard data
- **Regional Data**: Support for multiple regions and categories

### Data Synchronization
- **Automatic Updates**: Regular refresh of leaderboard data
- **Performance Tracking**: Integration with other simulation features
- **Score Calculation**: Server-side calculation of points and rankings

## 📊 Analytics & Tracking

The leaderboard system tracks:
- **Engagement Metrics**: How often users check rankings
- **Regional Performance**: Performance patterns by region
- **Competition Effects**: Impact of leaderboard on user behavior
- **Score Distributions**: Analysis of point distributions

## 🔗 Integration Points

### Feature Integration
- **Challenges**: Points awarded for challenge completion
- **Decisions**: Performance impacts leaderboard rankings
- **Goals**: Goal achievement affects scoring
- **Metrics**: Overall metrics contribute to leaderboard position

### Workshop Configuration
- **Custom Regions**: Workshop-specific regional definitions
- **Scoring Rules**: Customizable point systems per workshop
- **Display Options**: Configurable leaderboard features
- **Branding**: Workshop-specific colors and styling

## 🔗 Related Components

- **Challenges System**: Contributes to leaderboard scoring
- **Metrics System**: Performance data feeds into rankings
- **Dashboard**: Integrated into main navigation flow
- **FiltersGroup**: Shared filtering component

---

*For technical implementation details, see [Technical Architecture](./TECHNICAL-ARCHITECTURE.md)*
