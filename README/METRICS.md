# Metrics System

The Metrics system is the backbone of performance tracking in the Lumen Simulation App, providing real-time visualization of team performance across all simulation activities. It uses interactive pie charts to display progress and integrates seamlessly with all major features.

## 📈 Overview

The Metrics system tracks key performance indicators (KPIs) that are affected by user decisions throughout the simulation. Each metric is displayed as a percentage-based pie chart, showing current progress toward goals and providing immediate visual feedback on the impact of strategic decisions.

## 🎨 User Interface

### Design Features
- **Pie Chart Visualization**: Interactive pie charts using Recharts library
- **Real-time Updates**: Metrics update immediately when decisions are made
- **Responsive Grid**: Adaptive grid layout for different screen sizes
- **Color-coded Display**: Workshop-specific colors for consistent branding
- **Sticky Positioning**: Metrics panel stays visible during scrolling
- **Glass Morphism**: Modern translucent cards with backdrop blur

### Visual Elements
- Circular progress indicators with percentage labels
- Workshop-specific color schemes
- Clean, minimal design with focus on data
- Consistent spacing and typography
- Smooth transitions when values change

## 🚀 Features

### 1. Real-time Metrics Display
**File**: `src/components/Metrics/Metrics.jsx`

#### Core Functionality
- **Dynamic Calculation**: Metrics recalculated based on user decisions
- **Visual Representation**: Pie charts showing progress percentages
- **Multiple Metrics**: Support for multiple KPIs simultaneously
- **Workshop Integration**: Customizable metrics per workshop type
- **Responsive Layout**: Adapts to different screen sizes

#### Metrics Structure
```javascript
// Metric data structure
{
  name: string,           // Display name (e.g., "Customer Satisfaction")
  alias: string,          // Internal identifier (e.g., "Metric 1")
  value: number,          // Current value from decisions
  defaultValue: number,   // Base value before any decisions
  maxValue: number        // Maximum possible value (usually 100)
}
```

### 2. Integration with Decision Systems
The metrics system integrates with multiple features:

#### Challenges Integration
```javascript
// Metrics impact from challenge decisions
globalTeamMetrics.forEach(metric => {
  const [, metricIndex] = metric.alias.split(" ");
  const alias = `optionMetric${metricIndex}${option.toUpperCase()}`;
  metric.value = (metric.value || 0) + (challenge[alias] || 0);
});
```

#### Strategic Initiatives Integration
```javascript
// Metrics impact from initiative selections
updatedMetrics.forEach((metric) => {
  const alias = metric.alias ? metric.alias.split(" ").join("") : "";
  if (alias) {
    metric.value = (metric.value || 0) + (initiative[alias] || 0);
  }
});
```

### 3. Visual Chart System
**Powered by Recharts**

#### Chart Configuration
```javascript
const data = [
  { 
    name: "Current", 
    value: currentValue, 
    color: lightHighlightColor 
  },
  {
    name: "Remaining",
    value: remainingValue,
    color: darkHighlightColor,
  },
];

<PieChart width={128} height={128}>
  <Pie
    data={data}
    cx={64}
    cy={64}
    innerRadius={40}
    outerRadius={60}
    startAngle={90}
    endAngle={-270}
    dataKey="value"
    stroke="none"
  >
    <Label
      value={`${currentValue}%`}
      position="center"
      className="text-base font-semibold fill-white"
    />
  </Pie>
</PieChart>
```

## 🔧 Technical Implementation

### State Management
```javascript
const Metrics = ({
  metrics: globalTeamMetrics = [],
  setGlobalMetrics = () => {},
  client,
  location,
}) => {
  const [metrics, setMetrics] = useState([]);
  const metricsRef = useRef(null);

  // Default colors if client is undefined
  const darkHighlightColor = client?.darkHighlightColor || "#004864";
  const lightHighlightColor = client?.lightHighlightColor || "#00A3E3";
```

### Sticky Positioning
```javascript
useEffect(() => {
  if (metricsRef.current && location?.pathname === "/home") {
    const stickyInstance = stickybits(metricsRef.current, {
      stickyBitStickyOffset: 20,
    });

    return () => {
      stickyInstance.cleanup();
    };
  }
}, [location]);
```

### Data Processing
```javascript
// Calculate current value including base and decision impacts
const currentValue = parseInt(metric.value || 0, 10) + parseInt(metric.defaultValue || 0, 10);
const remainingValue = Math.max(0, 100 - currentValue);
```

## 📊 Metrics Types

### Common Metric Categories
1. **Customer Satisfaction**: Impact of decisions on customer experience
2. **Financial Performance**: Revenue, profit, and cost implications
3. **Team Morale**: Employee satisfaction and engagement
4. **Operational Efficiency**: Process improvement and productivity
5. **Market Position**: Competitive advantage and market share
6. **Innovation Index**: Research, development, and innovation metrics

### Workshop-Specific Metrics
Different workshops can define custom metrics:
- **Healthcare**: Patient satisfaction, clinical outcomes, staff retention
- **Manufacturing**: Quality metrics, safety scores, efficiency ratings
- **Retail**: Customer loyalty, inventory turnover, sales performance
- **Technology**: User adoption, system reliability, innovation metrics

## 🎯 Real-time Updates

### Decision Impact Flow
1. **User Makes Decision**: Selection in Challenges, Initiatives, etc.
2. **Metric Calculation**: System calculates impact on each metric
3. **State Update**: Metrics state updated with new values
4. **Visual Update**: Charts re-render with new percentages
5. **Smooth Transitions**: Animated transitions between values

### Calculation Logic
```javascript
// Reset all metrics to base values
updatedMetrics.forEach((metric) => {
  metric.value = 0;
});

// Recalculate based on all decisions
allDecisions.forEach((decision) => {
  updatedMetrics.forEach((metric) => {
    const impact = getDecisionImpact(decision, metric);
    metric.value += impact;
  });
});
```

## 🎨 Visual Design

### Chart Styling
- **Color Scheme**: Workshop-specific light and dark highlight colors
- **Typography**: Clean, readable percentage labels
- **Sizing**: Consistent 128x128px charts with 40px inner radius
- **Animation**: Smooth transitions when values change
- **Accessibility**: High contrast colors and clear labeling

### Layout System
```javascript
// Responsive grid layout
<div className="grid grid-cols-1 sm:grid-cols-1 gap-4 p-4">
  {metrics.map((metric, index) => (
    <div
      key={index}
      className="flex flex-col items-center p-4 rounded-lg bg-transparent border border-white/10"
    >
      {/* Chart and label content */}
    </div>
  ))}
</div>
```

## 📱 Responsive Design

### Layout Adaptation
- **Desktop**: Multi-column grid with optimal spacing
- **Tablet**: Adjusted grid with appropriate sizing
- **Mobile**: Single-column layout with touch-optimized spacing
- **Flexible Charts**: Charts maintain aspect ratio across devices

### Sticky Behavior
- **Desktop**: Sticky positioning during scroll
- **Mobile**: Standard flow to avoid layout issues
- **Context-Aware**: Only sticky on main dashboard page

## 🚀 Recent Updates

### Performance Improvements
- **Efficient Rendering**: Optimized chart re-rendering
- **State Management**: Improved state update patterns
- **Memory Usage**: Better cleanup of chart instances
- **Animation Performance**: Smooth 60fps transitions

### Visual Enhancements
- **Modern Design**: Updated to glass morphism styling
- **Better Typography**: Improved label readability
- **Color Consistency**: Workshop-specific theming throughout
- **Responsive Improvements**: Better mobile experience

### Integration Enhancements
- **Real-time Updates**: Immediate response to decision changes
- **Cross-feature Integration**: Seamless integration with all features
- **Error Handling**: Robust error handling and fallbacks
- **Accessibility**: Enhanced screen reader support

## 🔗 Integration Points

### Feature Integration
- **Challenges**: Real-time updates from challenge decisions
- **Strategic Initiatives**: Impact from initiative selections
- **Decisions**: P&L analysis affects financial metrics
- **Goals**: Metrics help track progress toward team goals

### Data Sources
```javascript
// Metrics data can come from multiple sources
const metrics = globalTeamMetrics || teamMetrics || defaultMetrics;

// Workshop configuration affects display
const colors = {
  light: client?.lightHighlightColor || "#00A3E3",
  dark: client?.darkHighlightColor || "#004864"
};
```

## 📊 Analytics & Tracking

The metrics system tracks:
- **Performance Trends**: How metrics change over time
- **Decision Correlation**: Which decisions most impact metrics
- **Team Comparisons**: Relative performance across teams
- **Workshop Effectiveness**: Metric improvements by workshop type

## 🔗 Related Components

- **Challenges System**: Primary source of metric updates
- **Strategic Initiatives**: Another source of metric impacts
- **Dashboard**: Main container for metrics display
- **Leaderboard**: Uses metrics for team rankings

---

*For technical implementation details, see [Technical Architecture](./TECHNICAL-ARCHITECTURE.md)*
