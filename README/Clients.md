# Clients Management

The Clients Management feature is the central configuration system for organizations using the Lumen Simulation platform. It handles client setup, branding customization, and content scheme assignments that define the simulation experience for teams.

## Overview

Clients represent organizations or departments that use the simulation platform. Each client has its own branding, content schemes, and configuration settings that create a customized experience for their assigned teams.

## Key Features

### Client Configuration
- **Basic Information**: Client name and identification
- **Branding Customization**: Logo, background images, and color schemes
- **Tab Naming**: Custom names for navigation tabs
- **Content Schemes**: Assignment of challenges, initiatives, assessments, and other content

### Visual Branding
- **Logo Upload**: Custom client logos for branding
- **Background Images**: Customizable background imagery
- **Color Schemes**: Light and dark highlight colors for theming
- **Tab Customization**: Rename navigation tabs to match client terminology

### Content Management
- **Scheme Assignment**: Link clients to specific content schemes
- **Global Team Metrics**: Configure performance tracking schemes
- **Multi-Content Support**: Assign different schemes for different content types

### Data Export
- **Excel Export**: Export client configuration and team data
- **JSON Export**: Export client settings for backup or migration

## User Interface

### Clients List View (`/clients`)
The main clients listing page provides:

**Search and Filtering**
- Text search across client names
- Status filtering (active/disabled clients)
- Configurable results per page
- Sortable columns

**Table Columns**
- Client Name
- Created Date
- Status (Active/Disabled)
- Actions (View, Edit, Delete)

**Management Actions**
- Add new client
- Toggle show/hide disabled clients
- Bulk operations

### Client Details View (`/clients/:clientId`)
**Configuration Sections**

1. **Basic Information**
   - Client name (editable)
   - Creation and modification dates

2. **Branding Settings**
   - Logo image upload
   - Background image upload
   - Light highlight color picker
   - Dark highlight color picker

3. **Tab Naming**
   - Home tab name
   - Challenges tab name
   - Goals tab name
   - Strategic Initiatives tab name
   - Org Chart tab name
   - Self Assessment tab name
   - Leaderboard tab name

4. **Content Scheme Assignment**
   - Home scheme selection
   - Initiative scheme selection
   - Challenge scheme selection
   - Leaderboard scheme selection
   - Org Chart scheme selection
   - Self Assessment scheme selection

5. **Global Team Metrics**
   - Performance tracking scheme configuration
   - Metrics display settings

## Data Structure

### Client Object
```javascript
{
  id: number,
  name: string,
  logoImage: string, // File path or URL
  backgroundImage: string, // File path or URL
  homeTabName: string,
  challengesTabName: string,
  goalsTabName: string,
  strategicTabName: string,
  orgChartTabName: string,
  selfAssessmentTabName: string,
  leaderboardTabName: string,
  darkHighlightColor: string, // Hex color code
  lightHighlightColor: string, // Hex color code
  homeSchemeId: number,
  initiativeSchemeId: number,
  challengeSchemeId: number,
  leaderboardSchemeId: number,
  orgChartSchemeId: number,
  selfAssessmentSchemeId: number,
  globalTeamMetricsSchemes: array,
  created_at: timestamp,
  updated_at: timestamp,
  disabled: boolean
}
```

## Business Rules

### Client Creation
1. Client names should be unique for clarity
2. Default tab names are provided but can be customized
3. Default color schemes are applied initially
4. Content schemes must be assigned for full functionality

### Branding Guidelines
1. Logo images should be in standard web formats (PNG, JPG, SVG)
2. Background images should be optimized for web display
3. Color codes must be valid hexadecimal values
4. Images are uploaded and stored securely

### Content Scheme Assignment
1. Each content type can have one assigned scheme per client
2. Schemes must exist before they can be assigned
3. Changing schemes affects all teams assigned to the client
4. Some content types may be optional depending on client needs

### Team Integration
1. Teams assigned to a client see the client's branding
2. Teams access content based on client's scheme assignments
3. Multiple teams can be assigned to the same client
4. Client changes affect all assigned teams immediately

## Integration Points

### With Teams
- Teams are assigned to clients to access customized content
- Client branding appears in team interfaces
- Client scheme assignments determine available content for teams

### With Content Schemes
- **Challenges**: Client selects which challenge scheme teams will use
- **Strategic Initiatives**: Assignment of initiative schemes for goal-setting
- **Self-Assessments**: Selection of assessment tools available to teams
- **Leaderboards**: Configuration of performance tracking and display
- **Org Charts**: Assignment of organizational structure schemes
- **Welcome Pages**: Custom landing page configurations

### With Global Team Metrics
- Performance tracking configuration
- Metrics display customization
- Cross-team comparison settings

## Branding and Customization

### Visual Identity
**Logo Management**
- Upload custom client logos
- Automatic resizing and optimization
- Display across team interfaces
- Fallback to default if not provided

**Background Imagery**
- Custom background images for enhanced branding
- Responsive image handling
- Overlay compatibility with interface elements

**Color Theming**
- Light highlight color for primary interface elements
- Dark highlight color for secondary elements
- Automatic contrast adjustment for readability
- Color picker interface for easy selection

### Interface Customization
**Tab Naming**
- Rename navigation tabs to match client terminology
- Maintain functionality while improving user familiarity
- Support for different languages or industry terms

**Content Labeling**
- Customize how different content types are presented
- Align with client's internal terminology
- Improve user adoption through familiar language

## Export Functionality

### Excel Export
- **Client Configuration**: Basic settings and scheme assignments
- **Team Data**: All teams assigned to the client with their progress
- **Performance Metrics**: Aggregated team performance data
- **Usage Statistics**: Activity and engagement metrics

### JSON Export
- **Configuration Backup**: Complete client settings for backup
- **Migration Support**: Export for moving between environments
- **Integration Data**: Structured data for external system integration

## Common Workflows

### Setting Up a New Client
1. Navigate to Clients → Add Client
2. Enter basic client information
3. Upload logo and background images
4. Configure color scheme
5. Customize tab names if needed
6. Assign content schemes
7. Configure global team metrics
8. Save and test with a sample team

### Customizing Client Branding
1. Access client details page
2. Upload new logo/background images
3. Adjust color scheme using color pickers
4. Preview changes in interface
5. Save and verify across team interfaces

### Managing Content Schemes
1. Review available schemes for each content type
2. Select appropriate schemes based on client needs
3. Test scheme assignments with sample teams
4. Monitor team engagement and adjust as needed

### Updating Client Configuration
1. Access client details
2. Modify settings as needed
3. Consider impact on assigned teams
4. Communicate changes to relevant stakeholders
5. Monitor for any issues post-update

## Security and Access Control

### Data Protection
- Client branding assets are stored securely
- Access controls prevent unauthorized modifications
- Audit trails track configuration changes

### Image Upload Security
- File type validation for uploaded images
- Size limits to prevent abuse
- Virus scanning for uploaded content
- Secure storage with access controls

## Performance Considerations

### Image Optimization
- Automatic image compression for web delivery
- Responsive image serving based on device
- CDN integration for fast global delivery
- Caching strategies for improved performance

### Configuration Loading
- Efficient loading of client settings
- Caching of frequently accessed configurations
- Lazy loading of non-critical branding elements

## Troubleshooting

### Common Issues
- **Image Upload Failures**: Check file size and format requirements
- **Color Display Issues**: Verify hex color code validity
- **Scheme Assignment Errors**: Ensure selected schemes exist and are active
- **Team Access Problems**: Verify team-client assignments are correct

### Best Practices
- Test branding changes with sample teams before full deployment
- Keep backup copies of original branding assets
- Document customization choices for future reference
- Regular review of scheme assignments for relevance
