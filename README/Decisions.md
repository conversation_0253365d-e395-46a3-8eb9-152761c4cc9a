# Decisions

The Decisions feature provides a comprehensive system for tracking and managing decision-making activities within the simulation environment. It enables teams to participate in structured decision scenarios and allows administrators to monitor decision patterns and outcomes.

## Overview

Decisions represent structured decision-making scenarios that teams encounter during their simulation experience. This feature tracks team choices, analyzes decision patterns, and provides insights into decision-making processes and outcomes.

## Key Features

### Decision Management
- **Create Decisions**: Build decision scenarios and frameworks
- **Edit Decisions**: Modify existing decision structures and content
- **View Decisions**: Detailed view of decision scenarios and results
- **Status Management**: Enable/disable decisions as needed

### Decision Tracking
- **Choice Recording**: Track team decisions and selections
- **Outcome Analysis**: Monitor decision results and consequences
- **Pattern Recognition**: Identify decision-making trends and patterns
- **Performance Impact**: Assess how decisions affect team performance

### Results and Analytics
- **Individual Results**: Detailed decision history for each team
- **Aggregate Analysis**: Summary statistics across teams and decisions
- **Export Functionality**: Export decision data for external analysis
- **Trend Reporting**: Track decision patterns over time

## User Interface

### Decisions List (`/decisions`)
The main listing page provides:

**Search and Filtering**
- Text search across decision names
- Status filtering (active/disabled decisions)
- Configurable results per page
- Sortable columns

**Table Columns**
- Decision Name
- Created Date
- Status (Active/Disabled)
- Actions (View, Edit, Delete)

**Management Actions**
- Add new decision scenario
- Toggle show/hide disabled decisions
- Bulk operations and exports

### Add Decision (`/decisions/add`)
**Configuration Options**

1. **Basic Information**
   - Decision Name: Descriptive name for the decision scenario
   - Description: Context and background for the decision
   - Instructions: Guidance for teams making the decision

2. **Decision Structure**
   - Decision options and choices available
   - Consequences and outcomes for each choice
   - Scoring or evaluation criteria

3. **Settings**
   - Time limits for decision-making
   - Team assignment options
   - Result visibility settings

### View/Edit Decision (`/decisions/:decisionId`)
**Editable Components**
- Decision name and description
- Choice options and consequences
- Evaluation criteria and scoring
- Status management (active/disabled)

**Results Management**
- View team decision history
- Access individual team choices
- Generate decision reports
- Export decision data

## Data Structure

### Decision Object
```javascript
{
  id: number,
  name: string,
  description: string,
  instructions: string,
  options: [
    {
      id: number,
      text: string,
      consequences: string,
      score: number,
      weight: number
    }
  ],
  timeLimit: number,
  allowRevisions: boolean,
  created_at: timestamp,
  updated_at: timestamp,
  disabled: boolean
}
```

### Decision Response Object
```javascript
{
  id: number,
  teamId: number,
  decisionId: number,
  selectedOption: number,
  reasoning: string,
  decisionTime: timestamp,
  timeSpent: number,
  revised: boolean,
  finalChoice: boolean
}
```

## Business Rules

### Decision Creation
1. Decision names should be descriptive and unique
2. At least two decision options must be provided
3. Consequences should be clearly defined for each option
4. Scoring criteria must be consistent and fair

### Decision Options
1. Options should be mutually exclusive
2. Each option should have clear consequences
3. Options should be balanced in terms of difficulty/appeal
4. Scoring should reflect the quality or appropriateness of choices

### Team Participation
1. Teams can only access decisions assigned through their clients
2. Decision choices are tracked with timestamps
3. Revision policies are enforced consistently
4. Teams may provide reasoning for their choices

### Results and Analysis
1. All team decisions are recorded and timestamped
2. Decision patterns are analyzed for insights
3. Results contribute to overall team performance metrics
4. Historical decision data is maintained for trend analysis

## Integration Points

### With Clients
- Decisions are assigned to clients through client configuration
- Client branding applies to decision interfaces
- Multiple decisions can be available per client

### With Teams
- Teams access decisions based on their client assignments
- Decision history is tracked per team
- Choices contribute to team performance profiles

### With Leaderboards
- Decision quality and speed may contribute to rankings
- Performance metrics include decision-making effectiveness
- Comparative analysis across teams is supported

### With Strategic Initiatives
- Decisions may relate to strategic initiative objectives
- Decision outcomes can impact initiative progress
- Integrated approach to strategic thinking development

## Decision Design Best Practices

### Creating Effective Scenarios
**Realistic Context**
- Base decisions on real-world situations
- Provide sufficient background information
- Include relevant constraints and considerations

**Balanced Options**
- Ensure options are viable and realistic
- Avoid obviously correct or incorrect choices
- Include trade-offs and competing priorities

**Clear Consequences**
- Define outcomes for each decision option
- Explain short-term and long-term implications
- Connect consequences to learning objectives

### Scenario Structure
**Appropriate Complexity**
- Match complexity to team experience level
- Provide enough information without overwhelming
- Include relevant data and context

**Engaging Content**
- Use scenarios relevant to team interests
- Include stakeholder perspectives
- Create meaningful dilemmas and challenges

## Common Workflows

### Creating a New Decision Scenario
1. Navigate to Decisions → Add Decision
2. Enter decision name, description, and context
3. Define decision options with consequences
4. Set evaluation criteria and scoring
5. Configure time limits and revision policies
6. Test scenario with sample teams

### Managing Decision Results
1. Monitor team participation and completion rates
2. Review individual team decision patterns
3. Analyze aggregate decision trends
4. Generate reports for stakeholders
5. Export data for external analysis

### Updating Existing Decisions
1. Review decision effectiveness and team feedback
2. Modify options or consequences as needed
3. Update scoring criteria if required
4. Consider impact on historical comparisons
5. Communicate changes to affected teams

### Results Analysis
1. Export decision data for detailed analysis
2. Compare decision patterns across teams
3. Identify successful decision-making strategies
4. Generate insights for organizational development

## Analytics and Reporting

### Individual Team Analysis
- Complete decision history and patterns
- Decision quality and consistency metrics
- Time spent on decision-making
- Reasoning and justification analysis

### Aggregate Insights
- Popular choices across teams
- Decision speed and efficiency metrics
- Success rates for different decision types
- Correlation between decisions and performance

### Trend Analysis
- Decision-making improvement over time
- Seasonal or cyclical decision patterns
- Impact of training on decision quality
- Comparative analysis across different periods

## Export and Integration

### Data Export
- **Excel Export**: Decision data and team responses
- **JSON Export**: Structured data for external systems
- **CSV Export**: Raw data for statistical analysis
- **PDF Reports**: Formatted reports for stakeholders

### External Integration
- API endpoints for external system integration
- Data feeds for business intelligence tools
- Integration with learning management systems
- Connection to performance management platforms

## Security and Access Control

### Data Protection
- Decision responses are encrypted and secure
- Access controls protect sensitive information
- Audit trails track data access and modifications

### Privacy Considerations
- Individual team decisions are confidential
- Aggregate data is anonymized appropriately
- Teams control access to their decision history
- Compliance with data protection regulations

## Troubleshooting

### Common Issues
- **Decision Access Problems**: Verify client-decision assignments
- **Response Tracking Errors**: Check team-client relationships
- **Export Failures**: Ensure sufficient data for export operations
- **Timing Issues**: Verify time limit configurations

### Best Practices
- Test decision scenarios thoroughly before deployment
- Provide clear instructions and context to teams
- Monitor participation rates and follow up as needed
- Regular review and update of decision content

## Future Enhancements

### Potential Features
- Multi-stage decision scenarios with branching paths
- Collaborative decision-making for team scenarios
- Real-time decision analytics and dashboards
- Integration with external decision support tools
- Advanced AI analysis of decision patterns and outcomes
