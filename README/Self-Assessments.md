# Self Assessments

The Self Assessments feature provides a comprehensive system for creating and managing assessment tools that teams use to evaluate their skills, competencies, and performance within the simulation environment.

## Overview

Self Assessments are structured evaluation tools that allow teams to assess their capabilities, identify strengths and development areas, and track progress over time. These assessments provide valuable insights for both teams and administrators.

## Key Features

### Assessment Management
- **Create Assessments**: Build comprehensive assessment frameworks
- **Edit Assessments**: Modify existing assessments and their content
- **View Assessments**: Detailed view of assessment structures and results
- **Status Management**: Enable/disable assessments as needed

### Assessment Configuration
- **Question Types**: Support for various question formats and response types
- **Scoring Systems**: Configurable scoring and evaluation methods
- **Categories**: Organize questions into logical groupings
- **Customization**: Tailor assessments to specific organizational needs

### Results and Reporting
- **Individual Results**: Detailed results for each team
- **Aggregate Analysis**: Summary statistics across teams
- **PDF Reports**: Generate comprehensive assessment reports
- **Progress Tracking**: Monitor improvement over time

## User Interface

### Self Assessments List (`/self-assessments`)
The main listing page provides:

**Search and Filtering**
- Text search across assessment names
- Client-based filtering
- Status filtering (active/disabled assessments)
- Configurable results per page

**Table Columns**
- Assessment Name
- Created Date
- Status (Active/Disabled)
- Actions (View, Edit, Delete)

**Management Actions**
- Add new assessment
- Client selection for filtering
- Toggle show/hide disabled assessments

### Add Assessment (`/self-assessments/add`)
**Configuration Options**

1. **Basic Information**
   - Assessment Name: Descriptive name for the assessment
   - Description: Purpose and scope of the assessment
   - Instructions: Guidance for teams taking the assessment

2. **Question Configuration**
   - Question types (multiple choice, rating scales, text responses)
   - Question categories and groupings
   - Scoring methods and weightings

3. **Assessment Settings**
   - Time limits (if applicable)
   - Retake policies
   - Result visibility settings

### View/Edit Assessment (`/self-assessments/:id`)
**Editable Components**
- Assessment name and description
- Question content and structure
- Scoring and evaluation criteria
- Status management (active/disabled)

**Results Management**
- View team completion status
- Access individual team results
- Generate aggregate reports
- Export results data

## Data Structure

### Assessment Object
```javascript
{
  id: number,
  name: string,
  description: string,
  instructions: string,
  questions: [
    {
      id: number,
      text: string,
      type: string,
      category: string,
      options: array,
      weight: number,
      required: boolean
    }
  ],
  scoringMethod: string,
  timeLimit: number,
  allowRetakes: boolean,
  created_at: timestamp,
  updated_at: timestamp,
  disabled: boolean
}
```

### Assessment Response Object
```javascript
{
  id: number,
  teamId: number,
  assessmentId: number,
  responses: [
    {
      questionId: number,
      answer: string,
      score: number
    }
  ],
  totalScore: number,
  completedAt: timestamp,
  timeSpent: number
}
```

## Business Rules

### Assessment Creation
1. Assessment names should be descriptive and unique
2. At least one question must be included in each assessment
3. Questions should be clearly worded and unambiguous
4. Scoring methods must be defined and consistent

### Question Design
1. Questions should be relevant to assessment objectives
2. Response options should be comprehensive and mutually exclusive
3. Required questions must be clearly indicated
4. Question categories help organize content logically

### Team Participation
1. Teams can only access assessments assigned through their clients
2. Assessment completion is tracked per team
3. Results are confidential to the team and administrators
4. Retake policies are enforced consistently

### Results and Scoring
1. Scoring is calculated based on defined methods
2. Results are available immediately upon completion
3. Aggregate data excludes personally identifiable information
4. Historical results are maintained for progress tracking

## Integration Points

### With Clients
- Self assessments are assigned to clients through client configuration
- Client branding applies to assessment interfaces
- Multiple assessments can be available per client

### With Teams
- Teams access assessments based on their client assignments
- Completion status is tracked and reported
- Results contribute to overall team performance profiles

### With Reporting System
- Assessment results feed into comprehensive team reports
- PDF reports can be generated for individual teams
- Aggregate data supports organizational analysis

### With Leaderboards
- Assessment scores may contribute to leaderboard rankings
- Performance metrics include assessment participation and results
- Comparative analysis across teams is supported

## Assessment Design Best Practices

### Creating Effective Questions
**Clear and Specific**
- Use simple, direct language
- Avoid ambiguous or leading questions
- Focus on specific behaviors or competencies

**Relevant and Valid**
- Align questions with assessment objectives
- Ensure questions measure what they intend to measure
- Include a range of difficulty levels

**Balanced Response Options**
- Provide balanced scale options (e.g., 1-5 rating scales)
- Include "not applicable" options where appropriate
- Avoid bias in response choices

### Assessment Structure
**Logical Organization**
- Group related questions into categories
- Order questions from general to specific
- Consider question flow and user experience

**Appropriate Length**
- Balance comprehensiveness with user engagement
- Consider time constraints and attention spans
- Include progress indicators for longer assessments

## Common Workflows

### Creating a New Assessment
1. Navigate to Self Assessments → Add Assessment
2. Enter assessment name, description, and instructions
3. Create questions with appropriate types and categories
4. Configure scoring methods and settings
5. Test assessment with sample responses
6. Assign to clients and make available to teams

### Managing Assessment Results
1. Monitor team completion rates
2. Review individual team results
3. Generate PDF reports for teams
4. Analyze aggregate data for insights
5. Identify trends and patterns

### Updating Existing Assessments
1. Review assessment effectiveness and team feedback
2. Modify questions or response options as needed
3. Update scoring methods if required
4. Consider impact on historical data
5. Communicate changes to affected teams

### Results Analysis
1. Export results data for external analysis
2. Compare performance across teams and time periods
3. Identify areas for organizational development
4. Generate reports for stakeholders

## Reporting and Analytics

### Individual Team Reports
- Detailed results breakdown by category
- Strengths and development areas identification
- Comparison to organizational benchmarks
- Recommendations for improvement

### Aggregate Analysis
- Overall completion rates and participation
- Average scores by category and question
- Distribution of responses across teams
- Trend analysis over time

### PDF Report Generation
- Professional formatting for team reports
- Customizable report templates
- Inclusion of charts and visualizations
- Secure distribution to authorized recipients

## Security and Privacy

### Data Protection
- Assessment responses are encrypted and secure
- Access controls protect sensitive information
- Audit trails track data access and modifications

### Privacy Considerations
- Individual team results are confidential
- Aggregate data is anonymized
- Teams control access to their own results
- Compliance with data protection regulations

## Troubleshooting

### Common Issues
- **Assessment Access Problems**: Verify client-assessment assignments
- **Completion Tracking Errors**: Check team-client relationships
- **Report Generation Failures**: Ensure sufficient data for reporting
- **Scoring Calculation Issues**: Verify scoring method configuration

### Best Practices
- Test assessments thoroughly before deployment
- Provide clear instructions and expectations to teams
- Monitor completion rates and follow up with non-participants
- Regular review and update of assessment content

## Future Enhancements

### Potential Features
- Advanced question types (drag-and-drop, multimedia)
- Adaptive assessments that adjust based on responses
- Integration with external assessment platforms
- Real-time analytics and dashboards
- Automated feedback and development recommendations
