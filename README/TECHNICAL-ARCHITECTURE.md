# Technical Architecture

The Lumen Simulation App is built on a modern, scalable architecture using React, Redux, and Node.js with a comprehensive API layer and PostgreSQL database. This document outlines the technical implementation, deployment strategies, and development practices.

## 🏗️ System Architecture

### Frontend Architecture
- **Framework**: React 18 with functional components and hooks
- **State Management**: Redux with Redux Toolkit for predictable state updates
- **Routing**: React Router v6 for client-side navigation
- **Build Tool**: Vite for fast development and optimized production builds
- **Language**: JavaScript/TypeScript with gradual TypeScript adoption

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: JWT tokens with middleware validation
- **API Design**: RESTful APIs with consistent response patterns
- **File Processing**: PDF generation and file upload handling

## 📦 Technology Stack

### Frontend Dependencies
```json
{
  "react": "^18.x",
  "react-dom": "^18.x",
  "react-redux": "^8.x",
  "react-router-dom": "^6.x",
  "@vitejs/plugin-react": "^4.x",
  "tailwindcss": "^3.x",
  "@radix-ui/react-*": "^1.x",
  "framer-motion": "^10.x",
  "recharts": "^2.x",
  "lucide-react": "^0.x",
  "sonner": "^1.x"
}
```

### Backend Dependencies
```json
{
  "express": "^4.x",
  "drizzle-orm": "^0.x",
  "pg": "^8.x",
  "jsonwebtoken": "^9.x",
  "@sendgrid/mail": "^7.x",
  "babel-node": "^7.x",
  "pm2": "^5.x"
}
```

## 🗄️ Database Schema

### Core Tables
**File**: `src/db/schema.js`

#### User Management
```sql
-- Users and Teams
CREATE TABLE lumen_team (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255),
  email VARCHAR(255) UNIQUE,
  password VARCHAR(255),
  created_at TIMESTAMP
);

-- Client/Workshop Configuration
CREATE TABLE clients (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255),
  background_image TEXT,
  light_highlight_color VARCHAR(7),
  dark_highlight_color VARCHAR(7),
  -- Feature visibility flags
  home_tab_visibility BOOLEAN DEFAULT true,
  goals_tab_visibility BOOLEAN DEFAULT true,
  -- Custom tab names
  home_tab_name VARCHAR(255),
  goals_tab_name VARCHAR(255)
);
```

#### Simulation Features
```sql
-- Challenges System
CREATE TABLE lumen_challenge_scheme (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255),
  disabled BOOLEAN DEFAULT false
);

CREATE TABLE lumen_challenge (
  id SERIAL PRIMARY KEY,
  description TEXT,
  option_a TEXT,
  option_b TEXT,
  option_c TEXT,
  consequence_a TEXT,
  consequence_b TEXT,
  consequence_c TEXT,
  -- Metrics impact
  option_metric1_a INTEGER DEFAULT 0,
  option_metric1_b INTEGER DEFAULT 0,
  option_metric1_c INTEGER DEFAULT 0
);

-- Leaderboard System
CREATE TABLE leaderboard_scheme (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255),
  disabled BOOLEAN DEFAULT false
);

CREATE TABLE leaderboard_user (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255),
  points INTEGER DEFAULT 0,
  leaderboard_region_id INTEGER REFERENCES leaderboard_region(id)
);
```

## 🔌 API Architecture

### RESTful Endpoints
**File**: `src/router.js`

#### Authentication Endpoints
```javascript
// User authentication
POST /user/sign-in          // User login
POST /user/sign-up          // User registration
POST /user/forgot-password  // Password reset request
POST /user/recover-password // Password reset confirmation
```

#### Simulation Feature Endpoints
```javascript
// Core features
GET  /user/workshops        // Available workshops
GET  /user/welcome-page     // Welcome page content
GET  /user/goals           // Team goals
POST /user/goals           // Update team goals

// Challenges system
GET  /user/challenges      // Challenge data
POST /user/challenges      // Save challenge responses

// Leaderboard
GET  /user/leaderboard     // Leaderboard data

// Self Assessment
GET  /user/self-assessments     // Assessment questions
POST /user/self-assessments     // Submit assessment
GET  /user/self-assessment-pdf  // Export PDF
```

### API Response Pattern
```javascript
// Consistent API response structure
{
  success: boolean,
  data: any,
  message?: string,
  error?: string
}
```

## 🔐 Security Implementation

### Authentication & Authorization
```javascript
// JWT Middleware
const jwtMiddleware = (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'No token provided' });
  }

  try {
    const decoded = jwt.verify(token, config.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ error: 'Invalid token' });
  }
};
```

### Security Features
- **JWT Tokens**: Stateless authentication with expiration
- **Password Hashing**: Secure password storage with bcrypt
- **CORS Configuration**: Proper cross-origin request handling
- **Input Validation**: Express-validation for request validation
- **SQL Injection Prevention**: Parameterized queries with Drizzle ORM

## 🚀 Deployment Architecture

### Development Environment
```bash
# Frontend development
npm run dev          # Vite dev server on localhost:5173

# Backend development  
npm run dev          # Nodemon with babel-node on localhost:8080
```

### Production Deployment
```bash
# Frontend build
npm run build        # Vite production build to /dist

# Backend build
npm run build        # Babel compilation to /dist
npm run start        # Production server with PM2
```

### AWS S3 Deployment
```bash
# Staging deployment
aws s3 sync ./build s3://app-staging.simulation.lumenconsultinggroup.com \
  --region us-east-1 --acl public-read --exclude .DS_Store

# Production deployment with CloudFront invalidation
aws s3 sync ./build s3://app.simulation.lumenconsultinggroup.com \
  --region us-east-1 --acl public-read
aws cloudfront create-invalidation --distribution-id XXXXX --paths "/*"
```

## 📊 State Management

### Redux Store Structure
```javascript
// Store configuration
{
  user: {
    isAuthenticated: boolean,
    userData: object,
    client: object,        // Workshop configuration
    loading: boolean,
    error: string
  }
}
```

### Redux Actions
```javascript
// User actions
export const signIn = (credentials) => ({
  type: 'SIGN_IN_REQUEST',
  payload: apiCall('/user/sign-in', 'POST', credentials)
});

export const setClient = (client) => ({
  type: 'SET_CLIENT',
  payload: client
});
```

## 🔧 Development Workflow

### Code Organization
```
src/
├── components/          # React components
│   ├── ui/             # Reusable UI components
│   ├── common/         # Shared components
│   └── [Feature]/      # Feature-specific components
├── containers/         # Redux-connected components
├── actions/           # Redux actions
├── reducers/          # Redux reducers
├── contexts/          # React contexts
├── hooks/             # Custom React hooks
├── lib/               # Utility libraries
└── utils/             # Helper functions
```

### Build Configuration
**File**: `vite.config.ts`
```javascript
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-accordion', '@radix-ui/react-dialog']
        }
      }
    }
  }
});
```

## 📈 Performance Optimizations

### Frontend Optimizations
- **Code Splitting**: Lazy loading with React.lazy()
- **Bundle Analysis**: Webpack bundle analyzer for optimization
- **Image Optimization**: Responsive images with proper formats
- **Caching**: Browser caching strategies for static assets
- **Tree Shaking**: Unused code elimination

### Backend Optimizations
- **Database Indexing**: Proper indexes on frequently queried columns
- **Connection Pooling**: PostgreSQL connection pool management
- **Caching**: Redis caching for frequently accessed data
- **API Rate Limiting**: Request rate limiting for security
- **Compression**: Gzip compression for API responses

## 🧪 Testing Strategy

### Frontend Testing
```javascript
// Component testing with React Testing Library
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '../store';

test('renders login form', () => {
  render(
    <Provider store={store}>
      <Login />
    </Provider>
  );
  expect(screen.getByText('Sign In')).toBeInTheDocument();
});
```

### Backend Testing
```javascript
// API endpoint testing with Mocha/Chai
describe('Authentication', () => {
  it('should authenticate valid user', async () => {
    const response = await request(app)
      .post('/user/sign-in')
      .send({ email: '<EMAIL>', password: 'password' });
    
    expect(response.status).to.equal(200);
    expect(response.body.data.token).to.exist;
  });
});
```

## 🔍 Monitoring & Analytics

### Application Monitoring
- **Error Tracking**: Sentry integration for error monitoring
- **Performance Monitoring**: Web Vitals tracking
- **User Analytics**: Google Analytics for user behavior
- **API Monitoring**: Response time and error rate tracking

### Database Monitoring
- **Query Performance**: Slow query logging and optimization
- **Connection Monitoring**: Database connection pool metrics
- **Backup Strategy**: Automated database backups
- **Health Checks**: Regular database health monitoring

## 🔄 CI/CD Pipeline

### Bitbucket Pipelines
**File**: `bitbucket-pipelines.yml`

```yaml
pipelines:
  branches:
    master:
      - step:
          name: Build and Deploy
          script:
            - npm install
            - npm run build
            - npm run test
            - aws s3 sync ./build s3://app.simulation.lumenconsultinggroup.com
```

### Deployment Environments
- **Development**: Local development with hot reload
- **Staging**: AWS S3 staging environment for testing
- **Production**: AWS S3 with CloudFront CDN for global distribution

## 🔗 Integration Points

### Third-Party Services
- **SendGrid**: Email service for password resets and notifications
- **AWS S3**: Static file hosting and deployment
- **AWS CloudFront**: CDN for global content delivery
- **PostgreSQL**: Primary database for all application data

### API Integrations
- **OpenAI**: AI-powered features and analysis
- **PDF Generation**: Server-side PDF creation for assessments
- **File Upload**: Secure file upload and processing

---

*This technical architecture supports the comprehensive feature set documented in the other README files.*
