# Goals System

The Goals system allows teams to define and manage their strategic objectives within the simulation environment. It provides a simple yet effective interface for setting team goals and tracking progress throughout the simulation experience.

## 🎯 Overview

The Goals feature enables teams to establish clear objectives at the beginning of their simulation journey. Teams can set up to three primary goals, which serve as guiding principles for their decision-making throughout the various simulation activities.

## 🎨 User Interface

### Design Features
- **Clean Form Layout**: Simple, intuitive goal-setting interface
- **Numbered Goals**: Clear organization with numbered goal sections (#1, #2, #3)
- **Large Text Areas**: Ample space for detailed goal descriptions
- **Save Functionality**: Prominent save button with loading states
- **Responsive Design**: Optimized for all screen sizes
- **Glass Morphism**: Modern translucent card design

### Visual Elements
- Modern card-based layout with backdrop blur
- Workshop-specific theming and colors
- Clear typography hierarchy
- Consistent spacing and padding
- Loading states with spinner animations

## 🚀 Features

### 1. Goal Setting Interface
**File**: `src/components/Goals/Goals.jsx`

#### Core Functionality
- **Three Goal Slots**: Teams can set up to three primary goals
- **Rich Text Input**: Large textarea fields for detailed goal descriptions
- **Team Name Display**: Shows the team name for context
- **Auto-save**: Goals are saved to the backend when submitted
- **Workshop Integration**: Customizable based on workshop configuration

#### Form Structure
```javascript
const [formData, setFormData] = useState({
  name: "",      // Team name
  goal1: "",     // First goal
  goal2: "",     // Second goal
  goal3: "",     // Third goal
});
```

### 2. Data Persistence
- **Backend Integration**: Goals saved to database via API
- **Real-time Updates**: Immediate saving when form is submitted
- **Data Retrieval**: Goals loaded from backend on component mount
- **Error Handling**: Comprehensive error handling with user feedback

### 3. Workshop Customization
- **Custom Tab Names**: Workshop-specific naming (e.g., "Patient Care Goals")
- **Visibility Control**: Goals can be disabled for specific workshops
- **Theme Integration**: Workshop-specific colors and styling
- **Navigation Flow**: Automatic navigation if goals are disabled

## 🔧 Technical Implementation

### State Management
```javascript
const Goals = ({ user: { client = {} }, navigate }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    goal1: "",
    goal2: "",
    goal3: "",
  });

  const { lightHighlightColor, goalsTabName, goalsTabVisibility } = client;
```

### Data Flow
```javascript
// Fetch existing team data
const fetchTeamData = async () => {
  setIsLoading(true);
  const [err, res] = await to(getTeam().payload);

  if (err) {
    toast.error("Failed to load team data");
    return;
  }

  setFormData(res.data);
  setIsLoading(false);
};

// Save goals to backend
const handleFormSubmit = async (event) => {
  event.preventDefault();
  setIsLoading(true);

  const [err] = await to(updateTeam(formData).payload);

  if (err) {
    toast.error("Failed to update goals");
    return;
  }

  toast.success("Goals saved successfully");
  setIsLoading(false);
};
```

### Workshop Integration
```javascript
// Check if goals are enabled for this workshop
useEffect(() => {
  if (client?.hasOwnProperty("goalsTabVisibility") && !goalsTabVisibility) {
    navigate("/strategic-initiatives");
    return;
  }
  fetchTeamData();
}, [client, goalsTabVisibility, navigate]);
```

### Form Input Handling
```javascript
const handleInputChange = (event) => {
  const { name, value, type, checked } = event.target;
  setFormData((prev) => ({
    ...prev,
    [name]: type === "checkbox" ? checked : value,
  }));
};
```

### UI Component Structure
The Goals interface uses modern UI components:
- **Card/CardHeader/CardContent**: Radix UI card components for layout
- **Button**: Custom button with loading states and workshop theming
- **Label**: Accessible form labels for each goal
- **Textarea**: Large text areas with custom styling and placeholder text
- **Loader**: Lucide React spinner for loading states

## 📱 Responsive Design

### Layout Adaptation
- **Desktop**: Full-width form with optimal spacing
- **Tablet**: Adjusted padding and text sizes
- **Mobile**: Single-column layout with touch-optimized inputs
- **Flexible Text Areas**: Responsive textarea sizing

### Form Optimization
- **Touch-Friendly**: Large touch targets for mobile users
- **Keyboard Navigation**: Full keyboard accessibility
- **Auto-resize**: Text areas expand with content
- **Focus Management**: Proper focus handling between fields

## 🎨 Visual Design

### Modern Interface
- **Glass Morphism Cards**: Translucent background with backdrop blur
- **Consistent Spacing**: Uniform padding and margins throughout
- **Typography Hierarchy**: Clear distinction between headers and content
- **Color Integration**: Workshop-specific highlight colors
- **Loading States**: Smooth loading animations with spinners

### Theme Support
```javascript
// Theme-aware styling with workshop customization
<CardTitle className="text-2xl font-bold text-white">
  {goalsTabName || "Goals"}
</CardTitle>

<Button
  onClick={handleFormSubmit}
  disabled={isLoading}
  style={{
    background: lightHighlightColor,
  }}
  className="font-semibold hover:opacity-90 transition-opacity"
>
  {isLoading ? (
    <>
      <Loader className="w-4 h-4 mr-2 animate-spin" />
      Saving...
    </>
  ) : (
    "Save Goals"
  )}
</Button>
```

### Form Field Styling
```javascript
// Individual goal text areas with modern styling
{[1, 2, 3].map((num) => (
  <div key={num} className="space-y-2">
    <Label
      htmlFor={`goal${num}`}
      className="text-sm font-medium text-gray-200"
    >
      #{num}
    </Label>
    <Textarea
      id={`goal${num}`}
      name={`goal${num}`}
      value={formData[`goal${num}`]}
      onChange={handleInputChange}
      placeholder="Write your goal here..."
      className="min-h-[120px] bg-white/5 border-white/10 text-white
                 placeholder:text-gray-400 resize-none"
      disabled={isLoading}
    />
  </div>
))}
```

## 🔄 User Experience Flow

### Goal Setting Process
1. **Page Load**: Goals interface loads with existing data (if any)
2. **Team Context**: Team name displayed for context
3. **Goal Entry**: Users enter up to three strategic goals
4. **Validation**: Form validates input before submission
5. **Saving**: Goals saved to backend with success feedback
6. **Confirmation**: Toast notification confirms successful save

### Navigation Integration
- **Conditional Display**: Only shown if enabled in workshop configuration
- **Auto-redirect**: Redirects to next section if goals are disabled
- **Metrics Integration**: Goals interface includes metrics display
- **Seamless Flow**: Integrates smoothly with overall dashboard navigation

## 🚀 Recent Updates

### UI/UX Improvements
- **Modern Card Design**: Updated to glass morphism styling
- **Enhanced Form Layout**: Improved spacing and visual hierarchy
- **Better Loading States**: Smooth loading animations with Lucide icons
- **Responsive Enhancements**: Better mobile and tablet experience

### Technical Enhancements
- **Error Handling**: Improved error states and user feedback
- **Performance**: Optimized component rendering and state management
- **Accessibility**: Enhanced keyboard navigation and screen reader support
- **Integration**: Better integration with workshop configuration system

## 🔗 Backend Integration

### API Endpoints
- **GET /user/team**: Retrieve team data including existing goals
- **PUT /user/team**: Update team information including goals (uses updateTeamDetails controller)
- **Data Structure**: Goals stored as part of team record

### Database Schema
```sql
-- Goals stored in lumen_team table
CREATE TABLE lumen_team (
  team_id SERIAL PRIMARY KEY,
  team_name VARCHAR(127),
  team_email VARCHAR(127),
  team_goal1 TEXT,
  team_goal2 TEXT,
  team_goal3 TEXT,
  client_id INTEGER,
  team_created_at TIMESTAMP,
  -- Additional team fields...
);
```

### Backend Controller
```javascript
// updateTeamDetails controller method
updateTeamDetails: async (req, res, next) => {
  const teamId = req.user.id;
  const { name, goal1, goal2, goal3 } = req.body;

  try {
    const team = await updateTeamDetails(teamId, name);

    const [teamGoalsData] = await db
      .update(teamGoals)
      .set({ goal_1: goal1, goal_2: goal2, goal_3: goal3 })
      .where(eq(teamGoals.team_id, teamId))
      .returning();

    return apiClient.success(req, res, next, 'Team details updated successfully');
  } catch (error) {
    return apiClient.serverError(req, res, next);
  }
}
```

### Data Validation
Goals are validated using Joi schema:
```javascript
// Team model validation
goal1: joi.string().allow('').min(0).max(64000).allow(null),
goal2: joi.string().allow('').min(0).max(64000).allow(null),
goal3: joi.string().allow('').min(0).max(64000).allow(null),
```

## 📊 Analytics & Tracking

The goals system tracks:
- **Goal Completion Rates**: How many teams set all three goals
- **Goal Content Analysis**: Common themes and objectives
- **Time to Complete**: How long teams spend setting goals
- **Revision Patterns**: How often teams update their goals

## 🔗 Integration Points

### Feature Integration
- **Metrics System**: Goals interface includes metrics display
- **Dashboard**: Integrated into main navigation flow
- **Workshop Configuration**: Customizable based on workshop settings
- **Team Management**: Goals tied to team identity and progress

### Related Components
- **Metrics Component**: Shared metrics display
- **Dashboard Navigation**: Part of main simulation flow
- **Strategic Initiatives**: Next step in simulation progression
- **Team Management**: Core team data and identity

---

*For technical implementation details, see [Technical Architecture](./TECHNICAL-ARCHITECTURE.md)*
