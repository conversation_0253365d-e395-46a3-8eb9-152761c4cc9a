# Dashboard & Navigation System

The Dashboard and Navigation system provides the main interface for the Lumen Simulation App, featuring a sophisticated scroll-based navigation system that seamlessly integrates all simulation features into a cohesive user experience.

## 🎯 Overview

The dashboard uses a unique ScrollWrapper component that creates a single-page application experience with smooth scrolling between different feature sections. Each section is dynamically loaded and configured based on the selected workshop's settings.

## 🎨 User Interface

### Design Features
- **Scroll-Based Navigation**: Smooth scrolling between feature sections
- **Dynamic Background**: Workshop-specific background imagery with gradient overlays
- **Glass Morphism**: Translucent sections with backdrop blur effects
- **Responsive Layout**: Optimized for all screen sizes
- **Theme Support**: Dark/light theme integration
- **Lazy Loading**: Performance-optimized component loading

### Visual Elements
- Gradient overlays on background images
- Consistent spacing and typography
- Smooth transitions between sections
- Modern card-based layouts
- Sticky navigation elements

## 🚀 Features

### 1. Main App Container (`/home`)
**File**: `src/components/App/App.jsx`

#### Functionality
- Global application wrapper with authentication checks
- Dynamic background image handling
- Theme provider integration
- Toast notification system
- Footer with copyright information

#### Key Features
```javascript
// Dynamic background styling
style={{
  background: theme === 'dark'
    ? `radial-gradient(125% 125% at 50% 10%, hsla(0, 0%, 0%, 0.8) 40%, hsla(255, 84%, 57%, 0.4) 100%), url(${backgroundImage}) center/cover no-repeat fixed, black`
    : `radial-gradient(125% 125% at 50% 10%, hsla(0, 0%, 100%, 0.9) 40%, hsla(255, 84%, 90%, 0.4) 100%), url(${backgroundImage}) center/cover no-repeat fixed, white`
}}
```

### 2. ScrollWrapper Navigation System
**File**: `src/components/ScrollWrapper.jsx`

#### Functionality
- Manages the main dashboard layout with scrollable sections
- Dynamically loads feature components based on workshop configuration
- Handles section visibility and lazy loading
- Integrates with theme and background context

#### Section Configuration
```javascript
const allSections = [
  {
    id: 'welcome',
    Component: WelcomePage,
    title: client.homeTabName || 'Welcome',
    isVisible: client.homeTabVisibility,
  },
  {
    id: 'goals',
    Component: Goals,
    title: client.goalsTabName || 'Goals',
    isVisible: client.goalsTabVisibility,
  },
  // Additional sections...
];
```

### 3. Dynamic Section Loading
Each section is conditionally rendered based on workshop configuration:

- **Welcome Page**: Custom welcome content and imagery
- **Goals**: Team goal setting interface
- **Strategic Initiatives**: Initiative selection with metrics
- **Challenges**: Interactive challenge system
- **Leaderboard**: Team rankings and competition
- **OrgChart**: Organizational structure visualization
- **Self Assessment**: Assessment and evaluation tools
- **Decisions**: Decision-making scenarios

## 🔧 Technical Implementation

### Lazy Loading System
```javascript
// Lazy load components for performance
const WelcomePage = lazy(() => import('../containers/WelcomePageContainer'));
const Goals = lazy(() => import('../containers/GoalsContainer'));
const StrategicInitiatives = lazy(() => import('../containers/StrategicInitiativesContainer'));
```

### Section Rendering Logic
```javascript
// Filter and render visible sections
const visibleSections = allSections.filter(section => section.isVisible);

return (
  <Suspense fallback={<Loader />}>
    {visibleSections.map(({ id, Component, title }) => (
      <section key={id} id={id}>
        <Component />
      </section>
    ))}
  </Suspense>
);
```

### Context Integration
- **Page Background Context**: Manages dynamic backgrounds
- **Theme Provider**: Handles dark/light theme switching
- **Redux State**: Global state management for user and client data

## 🎨 Theme System

### Dynamic Theming
The dashboard supports comprehensive theming:
- **Background Images**: Workshop-specific imagery
- **Color Schemes**: Custom highlight colors per workshop
- **Dark/Light Modes**: User-selectable theme preferences
- **Gradient Overlays**: Theme-aware gradient applications

### Theme Implementation
```javascript
const { theme } = useTheme();
const { pageImage } = usePageBackground();

// Apply theme-specific styling
const backgroundImage = pageImage || bgImage;
```

## 📱 Responsive Design

### Layout Adaptation
- **Desktop**: Full-width sections with optimal spacing
- **Tablet**: Adjusted layouts for medium screens
- **Mobile**: Single-column layouts with touch optimization
- **Flexible Components**: Each section adapts to screen size

### Performance Optimization
- **Lazy Loading**: Components loaded only when needed
- **Image Optimization**: Responsive image handling
- **Scroll Performance**: Optimized scroll event handling
- **Memory Management**: Efficient component mounting/unmounting

## 🔄 Navigation Flow

### User Journey
1. **Authentication** → User logs in
2. **Workshop Selection** → User chooses simulation environment
3. **Dashboard Loading** → Main interface loads with workshop configuration
4. **Section Navigation** → User scrolls through available features
5. **Feature Interaction** → User engages with specific simulation tools

### Section Transitions
- Smooth scrolling between sections
- Lazy loading of components as needed
- Consistent state management across sections
- Seamless integration of different features

## 🛠️ Configuration System

### Workshop-Based Configuration
Each workshop can customize:
- **Section Visibility**: Which features are available
- **Section Names**: Custom titles for each feature
- **Branding**: Colors, images, and styling
- **Content**: Workshop-specific content and data

### Example Configuration
```javascript
// Workshop configuration affects dashboard layout
{
  homeTabVisibility: true,
  homeTabName: "Welcome to Healthcare Sim",
  goalsTabVisibility: true,
  goalsTabName: "Patient Care Goals",
  challengesTabVisibility: true,
  challengesTabName: "Clinical Challenges",
  // Additional configuration...
}
```

## 🚀 Recent Updates

### UI/UX Improvements
- **Enhanced Scroll Experience**: Smoother transitions between sections
- **Improved Loading States**: Better lazy loading with skeleton screens
- **Theme Integration**: Enhanced dark/light theme support
- **Background System**: Dynamic background image handling

### Performance Enhancements
- **Lazy Loading**: Implemented for all major components
- **Memory Optimization**: Improved component lifecycle management
- **Scroll Performance**: Optimized scroll event handling
- **Bundle Splitting**: Code splitting for better load times

### Accessibility Improvements
- **Keyboard Navigation**: Enhanced keyboard support
- **Screen Reader Support**: Improved ARIA labels and roles
- **Focus Management**: Better focus handling across sections
- **High Contrast**: Support for high contrast themes

## 🔗 Integration Points

### Component Integration
- **Metrics System**: Integrated across multiple sections
- **Theme Provider**: Global theme management
- **Redux State**: Centralized state management
- **Router Integration**: Seamless navigation handling

### Data Flow
- Workshop configuration drives section availability
- User interactions update global state
- Metrics calculated and displayed across sections
- Real-time updates reflected throughout dashboard

## 📊 Analytics & Monitoring

The dashboard system tracks:
- Section engagement and time spent
- User navigation patterns
- Feature usage across different workshops
- Performance metrics and load times

## 🔗 Related Components

- **App Container**: Main application wrapper
- **Theme Provider**: Theme management system
- **Page Background Context**: Background image handling
- **Individual Feature Components**: Goals, Challenges, etc.
- **Metrics System**: Performance tracking across sections

---

*For technical implementation details, see [Technical Architecture](./TECHNICAL-ARCHITECTURE.md)*
