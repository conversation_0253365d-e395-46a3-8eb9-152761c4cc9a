html {
  height: 100%;
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

body {
  position: relative;
  margin: 0;
  padding-bottom: 6rem;
  min-height: 100%;
}

.app {
  min-height: 100%;
}

.absolute-center {
  margin: auto;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

.absolute-center.is-responsive {
  width: 50%;
  height: 50%;
  min-width: 400px;
  max-width: 600px;
  padding: 40px;
}

.body-container {
  height: 100%;
  padding-top: 85px;
  padding-bottom: 65px;
}

.border {
  border-radius: 5px;
  border: 1px solid #eee;
  height: 310px;
  padding: 10px 0px 0px 0px;
}

.save-button {
  margin-top: 15px;
}

.border-left {
  border-left: 1px solid #000;
}

.border-top {
  border-top: 1px solid #eee;
}

.background-gray {
  background-color: #d9d9d9;
  color: white !important;
  position: relative;
}

.background-grayer {
  background-color: #c9c9c9;
  color: white !important;
  position: relative;
}

.background-metric-1 {
  border-radius: 5px;
  background-color: #d9d9d9 !important;
  padding-bottom: 15px;
}

.background-metric-2 {
  border-radius: 5px;
  background-color: #c0c0c0 !important;
  padding-bottom: 15px;
}

.metric-div-1 {
  padding: 0px 0px 0px 15px !important;
}

.metric-div-2 {
  padding: 0px 15px 0px 0px !important;
}

.metric-chart-label {
  color: #252525;
}

.border-bottom {
  border-bottom: 1px solid #eee;
}

.plan-number {
  text-align: center;
  background: rgba(50, 50, 50, 0.4);
  border-radius: 5px;
  position: absolute;
  bottom: 0;
  width: 100%;
}

.header-bar-color-1 {
  color: #003f59;
}

.header-bar-color-2 {
  color: #005a7f;
}

.no-margin {
  margin: 0;
  padding: 0;
}

.chart-col-0 {
  padding: 0px 0px 0px 15px !important;
}

.chart-col-1 {
  padding: 0px 0px 0px 0px !important;
}

.chart-col-2 {
  padding: 0px 15px 0px 0px !important;
}

.height-220 {
  height: 220px;
}

.height-200 {
  height: 200px;
}

.full-height {
  height: 100%;
}

.margin-top-5 {
  margin-top: 5px;
}

.padding-0 {
  padding: 0px !important;
}

.padding-5 {
  padding: 5px;
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

.margin-bottom-15 {
  margin-bottom: 15px;
}

.margin-top-15 {
  margin-top: 15px;
}

.margin-bottom-20 {
  margin-bottom: 20px;
}

.margin-bottom-30 {
  margin-bottom: 30px;
}

color-white {
  color: white;
}

.spacer {
  height: 30px;
}

.small-text {
  font-size: 13px;
}

.large-text {
  font-size: 20px;
}

.larger-text {
  font-size: 30px;
}

.last-element-bordered div:last-child div {
  border-left: 1px solid #eee;
}

/**
 * Footer Styles
 */

.footer {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1rem;
  opacity: 0.6;
  text-align: center;
}
