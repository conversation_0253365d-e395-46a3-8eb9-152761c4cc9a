import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { db, disconnect } from "../src/db/index.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const migrationsDir = path.join(__dirname, "../src/db/migrations");

// PostgreSQL error codes we want to handle gracefully
const HANDLED_ERROR_CODES = {
  "42P07": "relation already exists",
  42701: "column already exists",
  "42P16": "index already exists",
  42710: "constraint already exists",
  42704: "constraint/object does not exist",
};

function splitSqlStatements(sql) {
  const statements = [];
  let currentStatement = "";
  let inDollarQuote = false;
  let dollarTag = "";

  const lines = sql.split("\n");

  for (const line of lines) {
    const trimmedLine = line.trim();

    // Skip empty lines and comments
    if (!trimmedLine || trimmedLine.startsWith("--")) {
      continue;
    }

    // Handle dollar quotes
    if (trimmedLine.includes("$$")) {
      const matches = trimmedLine.match(/\$\$|\$[a-zA-Z]*\$/g);
      if (matches) {
        for (const match of matches) {
          if (!inDollarQuote) {
            inDollarQuote = true;
            dollarTag = match;
          } else if (match === dollarTag) {
            inDollarQuote = false;
            dollarTag = "";
          }
        }
      }
    }

    currentStatement += line + "\n";

    // If we're not in a dollar quote and the line ends with a semicolon
    if (!inDollarQuote && trimmedLine.endsWith(";")) {
      statements.push(currentStatement.trim());
      currentStatement = "";
    }
  }

  // Add any remaining statement
  if (currentStatement.trim()) {
    statements.push(currentStatement.trim());
  }

  return statements;
}

async function runMigrations() {
  try {
    // Get all .sql files from migrations directory
    const files = fs
      .readdirSync(migrationsDir)
      .filter((file) => file.endsWith(".sql"))
      .sort();

    for (const file of files) {
      console.log(`Running migration: ${file}`);
      const sql = fs.readFileSync(path.join(migrationsDir, file), "utf8");

      // Split the SQL file into individual statements
      const statements = splitSqlStatements(sql);

      // Execute each statement
      for (const statement of statements) {
        if (statement.trim()) {
          try {
            await db.execute(statement);
          } catch (err) {
            if (HANDLED_ERROR_CODES[err.code]) {
              console.log(`Skipping: ${HANDLED_ERROR_CODES[err.code]}`);
              continue;
            }
            throw err;
          }
        }
      }

      console.log(`Completed migration: ${file}`);
    }

    console.log("All migrations completed successfully");
  } catch (error) {
    console.error("Migration failed:", error);
    process.exit(1);
  } finally {
    await disconnect(); // Use the imported disconnect function
  }
}

runMigrations();
