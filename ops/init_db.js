import pool from "../src/db/store";
import async from "async";
import config from "../config.json" with { type: 'json' };

const sqlDropTables = `
DROP TABLE IF EXISTS lumen_admin;
DROP TABLE IF EXISTS lumen_team_revenue_number;
DROP TABLE IF EXISTS lumen_team_metric;
DROP TABLE IF EXISTS lumen_team;
DROP TABLE IF EXISTS lumen_initiative;
DROP TABLE IF EXISTS lumen_challenge;
DROP TABLE IF EXISTS lumen_initiative_scheme;
DROP TABLE IF EXISTS lumen_challenge_scheme;
DROP TABLE IF EXISTS lumen_team_selected_initiative;
DROP TABLE IF EXISTS lumen_team_selected_challenge;
DROP TABLE IF EXISTS lumen_decision_page;
DROP TABLE IF EXISTS lumen_decision_scheme;
DROP TABLE IF EXISTS decision_results;
`;
const sqlCreateTables = `
CREATE TABLE lumen_admin
    (
    admin_id serial NOT NULL,
    admin_email character varying(127) NOT NULL,
    admin_created_at timestamp without time zone,
    admin_password character varying(127),
    CONSTRAINT lumen_admin_pkey PRIMARY KEY (admin_id)
);
CREATE TABLE lumen_team
(
    team_id serial NOT NULL,
    team_initiative_scheme_id int,
    team_challenge_scheme_id int,
    team_disabled boolean DEFAULT false,
    team_name character varying(127),
    team_email character varying(127),
    team_created_at timestamp without time zone,
    team_password character varying(127),
    team_goal1 text,
    team_goal2 text,
    team_goal3 text,
    CONSTRAINT team_id PRIMARY KEY (team_id)
);
CREATE TABLE lumen_team_revenue_number
(
  number_id serial NOT NULL,
  number_fixed BOOLEAN,
  number_name character varying(127),
  number_company_value bigint,
  number_industry_value bigint,
  number_planned_value bigint,
  number_team_id bigint,
  CONSTRAINT number_id PRIMARY KEY (number_id),
  CONSTRAINT number_team_id FOREIGN KEY (number_team_id)
      REFERENCES lumen_team (team_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);
CREATE TABLE lumen_team_metric
(
  metric_id serial NOT NULL,
  metric_fixed BOOLEAN,
  metric_team_id bigint NOT NULL,
  metric_name character varying(127),
  metric_string_value character varying(127),
  metric_company_value character varying(127),
  metric_industry_value character varying(127),
  metric_maximum bigint,
  CONSTRAINT metric_id PRIMARY KEY (metric_id),
  CONSTRAINT metric_team_id FOREIGN KEY (metric_team_id)
      REFERENCES lumen_team (team_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);
CREATE TABLE lumen_initiative_scheme
(
    scheme_id serial NOT NULL,
    scheme_disabled boolean DEFAULT false,
    scheme_name character varying(253) NOT NULL,
    scheme_created_at timestamp without time zone,
    scheme_initiative_number int NOT NULL,
    CONSTRAINT lumen_initive_scheme_pkey PRIMARY KEY (scheme_id)
);
CREATE TABLE lumen_initiative
(
    initiative_id serial NOT NULL,
    initiative_scheme_id int NOT NULL,
    initiative_name character varying(253) NOT NULL,
    initiative_description text,
    CONSTRAINT lumen_initiative_pkey PRIMARY KEY (initiative_id)
);
CREATE TABLE lumen_challenge_scheme
(
    scheme_id serial NOT NULL,
    scheme_disabled boolean DEFAULT false,
    scheme_name character varying(253) NOT NULL,
    scheme_created_at timestamp without time zone,
    CONSTRAINT lumen_challenge_scheme_pkey PRIMARY KEY (scheme_id)
);
CREATE TABLE lumen_challenge
(
    challenge_id serial NOT NULL,
    challenge_scheme_id int NOT NULL,
    challenge_image_url text,
    challenge_option_a text,
    challenge_consequence_a text,
    challenge_option_b text,
    challenge_consequence_b text,
    challenge_option_c text,
    challenge_consequence_c text,
    CONSTRAINT lumen_challenge_pkey PRIMARY KEY (challenge_id)
);
CREATE TABLE lumen_team_selected_initiative
(
  selected_id serial NOT NULL,
  selected_initiative_id integer,
  selected_team_id integer,
  CONSTRAINT selected_id PRIMARY KEY (selected_id)
);
CREATE TABLE lumen_team_selected_challenge
(
  selected_challenge_id integer,
  selected_team_id integer,
  selected_option_a boolean DEFAULT false,
  selected_option_b boolean DEFAULT false,
  selected_option_c boolean DEFAULT false,
  selected_id serial NOT NULL,
  CONSTRAINT selected_challenge_id PRIMARY KEY (selected_id)
);
CREATE TABLE lumen_decision_scheme
(
    scheme_id serial NOT NULL,
    scheme_disabled boolean DEFAULT false,
    scheme_name character varying(253) NOT NULL,
    scheme_created_at timestamp without time zone,
    scheme_updated_at timestamp without time zone,
    CONSTRAINT lumen_decision_scheme_pkey PRIMARY KEY (scheme_id)
);
CREATE TABLE lumen_decision_page
(
    page_id serial NOT NULL,
    scheme_id integer NOT NULL,
    page_number integer NOT NULL,
    sliders jsonb NOT NULL,
    incentives jsonb NOT NULL,
    CONSTRAINT lumen_decision_page_pkey PRIMARY KEY (page_id),
    CONSTRAINT lumen_decision_page_scheme_id_fkey FOREIGN KEY (scheme_id)
        REFERENCES lumen_decision_scheme (scheme_id)
);
CREATE TABLE decision_results
(
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    decision_scheme_id INTEGER NOT NULL REFERENCES lumen_decision_scheme(scheme_id),
    client_id INTEGER NOT NULL REFERENCES clients(id),
    selected_values JSONB NOT NULL,
    total_fte NUMERIC NOT NULL,
    total_investment NUMERIC NOT NULL,
    ai_analysis JSONB,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL
);
`;
const sqlInsert = `
INSERT INTO lumen_admin(admin_email, admin_password, admin_created_at) VALUES('${config.admin.email}', MD5('${config.admin.password}'), current_timestamp);
`;

pool.connect((err, client, release) => {
  if (err) {
    return console.error("Error acquiring client", err.stack);
  } else {
    async.series(
      {
        sqlDropTables: (done) => {
          client.query(sqlDropTables, (err, result) => {
            if (err) {
              return console.error("Error executing query", err.stack);
            } else {
              console.log(result);
              done(err);
            }
          });
        },
        sqlCreateTables: (done) => {
          client.query(sqlCreateTables, (err, result) => {
            if (err) {
              return console.error("Error executing query", err.stack);
            } else {
              console.log(result);
              done(err);
            }
          });
        },
        sqlInsert: (done) => {
          client.query(sqlInsert, (err, result) => {
            if (err) {
              return console.error("Error executing query", err.stack);
            } else {
              console.log(result);
              done(err);
            }
          });
        },
      },
      (err) => {
        if (err) {
          return console.error("Error executing query", err.stack);
        } else {
          release();
          process.exit(0);
        }
      }
    );
  }
});
