<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta name="robots" content="noindex,nofollow" />
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <link rel="manifest" href="/manifest.json" />
    <title>Lumen</title>
    <script>
      // On page load, check localStorage for theme preference
      (function() {
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
          document.documentElement.classList.remove('dark', 'light');
          document.documentElement.classList.add(savedTheme);
        }
      })();
    </script>
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
