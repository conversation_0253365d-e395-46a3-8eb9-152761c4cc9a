# Lumen Simulation App

A comprehensive business simulation platform built with React and Redux, designed to provide immersive learning experiences through interactive challenges, strategic decision-making, and team collaboration.

## 🌟 Features

### Core Simulation Features
- **🎯 Goals Management** - Set and track team objectives
- **🚀 Strategic Initiatives** - Select initiatives with real-time metrics impact
- **⚡ Interactive Challenges** - Sequential challenge system with animations
- **📊 Decision Making** - P&L analysis with advanced charting
- **📋 Self Assessment** - Comprehensive assessment with PDF export

### Analytics & Visualization
- **🏆 Leaderboard** - Team rankings with regional filtering
- **🌳 Organizational Charts** - Interactive tree visualization
- **📈 Real-time Metrics** - Performance tracking across all features
- **📊 Dashboard** - Comprehensive overview and navigation

### User Experience
- **🎨 Modern UI/UX** - Glass morphism design with dark/light themes
- **📱 Responsive Design** - Optimized for all device sizes
- **🔐 Secure Authentication** - Complete auth system with password recovery
- **🏢 Multi-Workshop Support** - Different simulation environments

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn package manager

### Installation

```bash
# Clone the repository
git clone [repository-url]
cd lumen-simulation-app

# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at `http://localhost:5173`

### Production Build

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## 🏗️ Tech Stack

- **Frontend**: React 18, Redux, React Router
- **UI Framework**: Tailwind CSS, Radix UI, Framer Motion
- **Charts**: Recharts, D3
- **Build Tool**: Vite
- **Language**: JavaScript/TypeScript

## 📚 Documentation

Comprehensive feature documentation is available in the [README folder](./README/):

- [📖 Documentation Index](./README/README-INDEX.md) - Complete guide to all documentation
- [🔐 Authentication System](./README/AUTHENTICATION.md)
- [🏢 Workshops](./README/WORKSHOPS.md)
- [📊 Dashboard & Navigation](./README/DASHBOARD.md)
- [🎯 Goals](./README/GOALS.md)
- [🚀 Strategic Initiatives](./README/STRATEGIC-INITIATIVES.md)
- [⚡ Challenges](./README/CHALLENGES.md)
- [📊 Decisions](./README/DECISIONS.md)
- [📋 Self Assessment](./README/SELF-ASSESSMENT.md)
- [🏆 Leaderboard](./README/LEADERBOARD.md)
- [🌳 OrgChart](./README/ORGCHART.md)
- [📈 Metrics](./README/METRICS.md)
- [🎨 UI/UX & Theming](./README/UI-UX-THEMING.md)
- [🏗️ Technical Architecture](./README/TECHNICAL-ARCHITECTURE.md)

## 🚀 Deployment

### Staging Deployment

```bash
# Build the application
npm run build

# Deploy to AWS S3 (staging)
cp ~/.aws/projects/lumen ~/.aws/credentials
aws s3 sync ./build s3://app-staging.simulation.lumenconsultinggroup.com --region us-east-1 --acl public-read --exclude .DS_Store
```

### Production Deployment

The application is deployed using modern CI/CD practices. See [Technical Architecture](./README/TECHNICAL-ARCHITECTURE.md) for detailed deployment information.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software owned by Lumen Consulting Group.

## 🆘 Support

For support and questions:
- Check the [Documentation](./README/README-INDEX.md)
- Contact the development team
- Review the [Technical Architecture](./README/TECHNICAL-ARCHITECTURE.md) for technical details

---

**Built with ❤️ by the Lumen Development Team**
