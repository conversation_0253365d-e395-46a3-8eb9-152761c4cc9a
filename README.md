## Install dependencies

    run "npm install && npm run init"

## Setup

    run "npm run dev" to run the Node live server ( localhost:8080 )

## Install on an ubuntu server

    # Update all packages
    sudo apt-get dist-upgrade -y; sudo apt-get update --fix-missing -y; sudo apt-get upgrade -y; sudo apt autoremove -y

    # Install NVM
    sudo apt-get install libpq-dev libssl-dev libxslt-dev libxml2-dev mailutils postgresql postgresql-contrib
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.37.2/install.sh | bash
    export NVM_DIR="$([ -z "${XDG_CONFIG_HOME-}" ] && printf %s "${HOME}/.nvm" || printf %s "${XDG_CONFIG_HOME}/nvm")"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" # This loads nvm

    # Install Node & Node Dependencies
    nvm install 18
    nvm alias default 18
    npm install -g babel-cli
    npm install -g pm2

    # For the PDF feature, install supporting PDF frameworks:
    sudo apt-get install -y libatk1.0-0 libatk-bridge2.0.0 libcups2 libxkbcommon0 libxcomposite-dev libxdamage1 libxrandr2 libpango-1.0 libcairo2

    # Open the ports
    sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT

    # Clone the repository
    git clone ... app && cd app

    # Install dependencies

    run "npm install && npm run init"

    # Run the live server

    cd /app
    sudo npm run migrate
    ps aux | grep node
    sudo chown -R $USER:0755 .
    npm run build
    pm2 start dist/index.js -i 4 --max-memory-restart 1G
    pm2 save
    pm2 log

