import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';

import AppContainer from './containers/AppContainer';
import IndexContainer from './containers/IndexContainer';
import TeamsContainer from './containers/TeamsContainer';
import AddTeamContainer from './containers/AddTeamContainer';
import ViewTeamContainer from './containers/ViewTeamContainer';
import StrategicInitiativesContainer from './containers/StrategicInitiativesContainer';
import AddInitiativeContainer from './containers/AddInitiativeContainer';
import ViewInitiativeContainer from './containers/ViewInitiativeContainer';
import ChallengesContainer from './containers/ChallengesContainer';
import AddChallengeContainer from './containers/AddChallengeContainer';
import ViewChallengeContainer from './containers/ViewChallengeContainer';
import ClientsContainer from './containers/ClientsContainer';
import ClientDetailsContainer from './containers/ClientDetailsContainer';
import LeaderboardsContainer from './containers/LeaderboardsContainer';
import LeaderboardDetailsContainer from './containers/LeaderboardDetailsContainer';
import OrgChartsContainer from './containers/OrgChartsContainer';
import OrgChartDetailsContainer from './containers/OrgChartDetailsContainer';
import WelcomePagesContainer from './containers/WelcomePagesContainer';
import WelcomePageDetailsContainer from './containers/WelcomePageDetailsContainer';
import NotFound from './components/NotFound/NotFound';
import AddSelfAssessmentContainer from './containers/AddSelfAssessmentContainer';
import SelfAssessmentContainer from './containers/SelfAssessmentContainer';
import PrivateRoute from './components/PrivateRoute';
import DecisionsContainer from './containers/DecisionsContainer';
import DecisionDetailsContainer from './containers/DecisionDetailsContainer';
import AddDecisionContainer from './containers/AddDecisionContainer';

export default (
  <BrowserRouter>
    <Routes>
      {/* Login page - no header needed */}
      <Route path="/" exact element={<IndexContainer />} />

      {/* All authenticated routes - wrapped with PrivateRoute and AppContainer */}
      <Route
        path="/teams"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <TeamsContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/teams/add"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <AddTeamContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/teams/id/:teamId"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <ViewTeamContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/strategic-initiatives"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <StrategicInitiativesContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/strategic-initiatives/id/:initiativeId"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <ViewInitiativeContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/strategic-initiatives/add"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <AddInitiativeContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/challenges"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <ChallengesContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/challenges/id/:challengeId"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <ViewChallengeContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/challenges/add"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <AddChallengeContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/clients"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <ClientsContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/clients/:clientId"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <ClientDetailsContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/welcome-pages"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <WelcomePagesContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/welcome-pages/:welcomePageId"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <WelcomePageDetailsContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/leaderboards"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <LeaderboardsContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/leaderboards/:leaderboardId"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <LeaderboardDetailsContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/org-charts"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <OrgChartsContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/org-charts/:orgChartId"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <OrgChartDetailsContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/self-assessments"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <SelfAssessmentContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/self-assessments/add"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <AddSelfAssessmentContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/self-assessments/:id"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <AddSelfAssessmentContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/decisions"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <DecisionsContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/decisions/add"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <AddDecisionContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="/decisions/:decisionId"
        exact
        element={
          <PrivateRoute>
            <AppContainer>
              <DecisionDetailsContainer />
            </AppContainer>
          </PrivateRoute>
        }
      />
      <Route
        path="*"
        element={
          <PrivateRoute>
            <AppContainer>
              <NotFound />
            </AppContainer>
          </PrivateRoute>
        }
      />
    </Routes>
  </BrowserRouter>
);
