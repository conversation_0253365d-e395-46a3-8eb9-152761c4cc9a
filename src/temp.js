// <div className="container">
//   <PageHeader>Add Challenges</PageHeader>
//   <form>
//     <Tabs defaultActiveKey={1} id="tabs-container">
//       <Tab eventKey={1} title="Challenge #1">
//         <Row>
//           <Col md={12}>
//             <FormControl
//               componentClass="textarea"
//               rows={5}
//               placeholder="Enter description text"
//             />
//             <hr/>
//           </Col>
//           <Col md={12}>
//             <ControlLabel>Option A)</ControlLabel>
//             <FormControl type="text" placeholder="Enter option name"/>
//             <ControlLabel>Consequence A)</ControlLabel>
//             <FormControl componentClass="textarea" placeholder="Enter description text"/>
//             <hr/>
//           </Col>
//           <Col md={12}>
//             <ControlLabel>Option B)</ControlLabel>
//             <FormControl type="text" placeholder="Enter option name"/>
//             <ControlLabel>Consequence B)</ControlLabel>
//             <FormControl componentClass="textarea" placeholder="Enter description text"/>
//             <hr/>
//           </Col>
//           <Col md={12}>
//             <ControlLabel>Option B)</ControlLabel>
//             <FormControl type="text" placeholder="Enter option name"/>
//             <ControlLabel>Consequence B)</ControlLabel>
//             <FormControl componentClass="textarea" placeholder="Enter description text"/>
//             <hr/>
//           </Col>
//           <Col md={12}>
//             <Button className="pull-right">Save</Button>
//           </Col>
//         </Row>
//       </Tab>
//       <Tab eventKey={2} title="Challenge #2"/>
//       <Tab eventKey={3} title={this.addChallengeButton()}/>
//     </Tabs>
//   </form>
// </div>;
