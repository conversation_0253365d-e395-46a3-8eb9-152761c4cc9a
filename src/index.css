body {
  margin: 0;
  padding: 0;
  font-family: sans-serif;
}

* {
  text-shadow: none !important;
}

.root-container {
  backdrop-filter: blur(10px);
}

.root-container .page-header {
  border-bottom: none;
  margin: 0;
}

.page-header {
  border-bottom: none;
  margin: 0;
}

.thead-light {
  background: white;
  border-color: white;
}

.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  border-color: white;
}

.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f5f5f5;
}

/* Ensure buttons with blue background (#0095ff) have white text in light mode */
.light button[class*="bg-[#0095ff]"],
.light a[class*="bg-[#0095ff]"],
.light div[class*="bg-[#0095ff]"],
.light span[class*="bg-[#0095ff]"],
.light [class*="bg-[#0095ff]"] {
  color: white !important;
}

/* Also ensure text elements inside these blue background elements stay white */
.light [class*="bg-[#0095ff]"] *,
.light [class*="bg-[#0095ff]"] span,
.light [class*="bg-[#0095ff]"] p,
.light [class*="bg-[#0095ff]"] div {
  color: white !important;
}
