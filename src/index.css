@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}
@layer base {
  * {
    @apply border-border;
    border-radius: var(--radius);
  }
  body {
    @apply bg-background text-foreground;
  }

  .dark body {
    background-color: black;
  }

  .light body {
    background-color: white;
  }

  /* Fix for text colors in light mode */
  .light .text-white {
    color: #111111 !important;
  }

  /* Ensure buttons with white text in light mode have proper contrast */
  .light button.text-white:not([style*="background"]),
  .light a.text-white:not([style*="background"]) {
    color: #111111 !important;
  }

  /* Make all text in light mode darker for better contrast */
  .light .text-gray-400 {
    color: #444444 !important;
  }

  .light .text-gray-300 {
    color: #333333 !important;
  }

  .light .text-gray-200 {
    color: #222222 !important;
  }

  /* Ensure all text has good contrast in light mode */
  .light {
    --foreground: 240 10% 0%; /* Make default text almost black */
    --muted-foreground: 240 5% 25%; /* Make muted text darker */
    --card-foreground: 240 10% 0%; /* Make card text almost black */
    --popover-foreground: 240 10% 0%; /* Make popover text almost black */
    --secondary-foreground: 240 5.9% 10%; /* Make secondary text darker */
  }

  /* Additional text color overrides for light mode */
  .light .prose p,
  .light .prose li,
  .light .prose h1,
  .light .prose h2,
  .light .prose h3,
  .light .prose h4 {
    color: #111111 !important;
  }

  /* Make all gray text classes darker in light mode */
  .light .text-gray-100,
  .light .text-gray-200,
  .light .text-gray-300,
  .light .text-gray-400,
  .light .text-gray-500,
  .light .text-gray-600 {
    color: #333333 !important;
  }

  /* Catch-all rule for any text elements */
  .light [class*="text-"],
  .light [class*="typography-"],
  .light [class*="font-"],
  .light [class*="prose"] {
    color: #111111 !important;
  }

  /* Ensure all headings have proper contrast in light mode */
  .light h1,
  .light h2,
  .light h3,
  .light h4,
  .light h5,
  .light h6 {
    color: #111111 !important;
  }

  /* Ensure all paragraphs have proper contrast in light mode */
  .light p,
  .light p *,
  .light div > p,
  .light section p,
  .light article p,
  .light main p,
  .light [class*="container"] p {
    color: #111111 !important;
  }

  /* Ensure all list items have proper contrast in light mode */
  .light li {
    color: #333333 !important;
  }

  /* Exception for buttons with colored backgrounds that need white text */
  .light button[class*="bg-blue"],
  .light button[class*="bg-indigo"],
  .light button[class*="bg-purple"],
  .light button[class*="bg-green"],
  .light button[class*="bg-red"],
  .light a[class*="bg-blue"],
  .light a[class*="bg-indigo"],
  .light a[class*="bg-purple"],
  .light a[class*="bg-green"],
  .light a[class*="bg-red"] {
    color: white !important;
  }

  /* Ensure labels and form elements have proper contrast */
  .light label {
    color: #333333 !important;
  }

  /* Ensure inputs have proper text color */
  .light input,
  .light textarea,
  .light select {
    color: #333333 !important;
  }

  /* Ensure links have proper contrast */
  .light a:not([class*="bg-"]) {
    color: #0066cc !important;
  }

  /* Fix for any specific components that might need special handling */
  .light .prose {
    color: #333333 !important;
  }

  /* Ensure tooltips and popovers have proper contrast */
  .light [role="tooltip"],
  .light [role="dialog"] {
    color: #333333 !important;
  }

  /* Ensure all span elements have proper contrast in light mode */
  .light span,
  .light div span,
  .light p span,
  .light li span,
  .light section span,
  .light article span,
  .light [class*="container"] span,
  .light [class*="wrapper"] span,
  .light [class*="content"] span {
    color: #111111 !important;
  }

  /* Exceptions for spans that should remain light colored */
  .light button span,
  .light a[class*="bg-"] span,
  .light [class*="bg-"] span {
    color: inherit !important;
  }

  /* Force dark text for specific components */
  .light .WelcomePage p,
  .light .WelcomePage span,
  .light .Decisions p,
  .light .Decisions span,
  .light .card p,
  .light .card span,
  .light .content p,
  .light .content span,
  .light .container p,
  .light .container span {
    color: #111111 !important;
  }

  /* Override any inline styles that might be setting text color */
  .light *:not(button):not([class*="bg-"]):not(a[class*="bg-"]) {
    color: #111111 !important;
  }

  /* Final catch-all for any text that might be missed */
  .light div,
  .light section,
  .light article,
  .light main,
  .light aside,
  .light header,
  .light footer {
    color: #111111;
  }

  /* Fix background opacity issues for specific components in light mode */
  .light .bg-black\/20,
  .light .bg-black\/40,
  .light .bg-black\/50,
  .light .bg-black\/60,
  .light .bg-black\/70,
  .light .bg-black\/80 {
    background-color: rgba(255, 255, 255, 0.9) !important;
  }

  /* Fix for Challenges component */
  .light .AccordionItem,
  .light [class*="AccordionItem"],
  .light [data-state="open"],
  .light [data-state="closed"] {
    background-color: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(200, 200, 200, 0.3) !important;
  }

  /* Fix for OrgChart nodes */
  .light .OrgChartNode,
  .light [class*="OrgChartNode"] {
    background-color: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(200, 200, 200, 0.3) !important;
  }

  /* Fix for any dark backgrounds in light mode */
  .light .bg-black,
  .light .bg-black\/10,
  .light .bg-black\/20,
  .light .bg-black\/30,
  .light .bg-black\/40,
  .light .bg-black\/50,
  .light .bg-black\/60,
  .light .bg-black\/70,
  .light .bg-black\/80,
  .light .bg-black\/90 {
    background-color: rgba(255, 255, 255, 0.9) !important;
  }

  /* Fix for dark borders in light mode */
  .light .border-white\/5,
  .light .border-white\/10,
  .light .border-white\/20,
  .light .border-white\/30 {
    border-color: rgba(200, 200, 200, 0.3) !important;
  }

  /* Force light backgrounds for any remaining dark elements */
  .light [class*="backdrop-blur"] {
    background-color: rgba(255, 255, 255, 0.9) !important;
  }

  /* Fix for any remaining dark backgrounds */
  .light .Challenges,
  .light .OrgChart,
  .light .AccordionContent,
  .light .AccordionItem,
  .light .Card {
    background-color: rgba(255, 255, 255, 0.9) !important;
  }
}
