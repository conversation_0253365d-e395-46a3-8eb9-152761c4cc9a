import { Provider } from 'react-redux';

import configureStore from './store/configureStore';
import routes from './routes';
import 'bootstrap/dist/css/bootstrap.css';
import 'bootstrap/dist/css/bootstrap-theme.css';
import './index.css';

import { createRoot } from 'react-dom/client';
import { initPdfWorker } from './pdf/initPdfWorker';

initPdfWorker();
const store = configureStore();
const container = document.getElementById('root');
const root = createRoot(container);

root.render(<Provider store={store}>{routes}</Provider>);
