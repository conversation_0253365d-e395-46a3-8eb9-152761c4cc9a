import React, { createContext, useContext, useState } from 'react';

const PageBackgroundContext = createContext();

export const usePageBackground = () => {
  const context = useContext(PageBackgroundContext);
  if (!context) {
    throw new Error('usePageBackground must be used within a PageBackgroundProvider');
  }
  return context;
};

export const PageBackgroundProvider = ({ children }) => {
  const [pageImage, setPageImage] = useState(null);

  return (
    <PageBackgroundContext.Provider value={{ pageImage, setPageImage }}>
      {children}
    </PageBackgroundContext.Provider>
  );
};
