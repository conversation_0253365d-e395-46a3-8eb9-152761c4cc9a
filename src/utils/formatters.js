/**
 * Formats a number as a currency string with $ symbol and commas
 * @param {number|string} value - The number to format
 * @returns {string} Formatted currency string (e.g., "$1,234.56")
 */
export const formatCurrency = (value) => {
  if (!value) return '$0';

  // Convert string to number if needed
  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  // Format with $ symbol and commas
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numValue);
};

/**
 * Formats a number as an abbreviated currency string (e.g., "$100k", "$1m", "$1b")
 * @param {number|string} value - The number to format
 * @returns {string} Abbreviated currency string
 */
export const formatCurrencyAbbreviated = (value) => {
  if (!value) return '$0';

  // Convert string to number if needed
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  const absValue = Math.abs(numValue);
  const sign = numValue < 0 ? '-' : '';

  if (absValue >= 1000000000) {
    // Billions
    const billions = absValue / 1000000000;
    return `${sign}$${billions.toFixed(billions >= 10 ? 0 : 1)}b`;
  } else if (absValue >= 1000000) {
    // Millions
    const millions = absValue / 1000000;
    return `${sign}$${millions.toFixed(millions >= 10 ? 0 : 1)}m`;
  } else if (absValue >= 1000) {
    // Thousands
    const thousands = absValue / 1000;
    return `${sign}$${thousands.toFixed(thousands >= 10 ? 0 : 1)}k`;
  } else {
    // Less than 1000, show full amount
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(numValue);
  }
};
