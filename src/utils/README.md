# Utils

This directory contains utility functions and helper modules used throughout the Lumen simulation platform.

## Overview

Utilities provide reusable functionality that supports the core application logic. These modules handle common operations like prompt generation, formatting, and data processing.

## Modules

### AI & Prompt Management
- **prompts.js** - AI prompt generation and template management
  - Handles P&L analysis prompt construction
  - Manages template variables and substitution
  - Provides system prompt configuration
  - Supports custom template functionality
  - Integrates with OpenAI API for content generation

## Key Features

### Prompt Generation
The `prompts.js` module provides:
- **Template System**: Flexible prompt templates with variable substitution
- **P&L Analysis**: Specialized prompts for financial analysis
- **Custom Templates**: Support for user-defined prompt templates
- **System Prompts**: Centralized system message configuration
- **Response Formatting**: Structured output formatting for AI responses

### JSDoc Types
Comprehensive TypeScript-style documentation for:
- `PromptParams` - Parameters for prompt generation
- Template variable definitions
- User selection data structures

## Dependencies

- **System Prompts**: `../../prompts/system.js`
- **Template Definitions**: `../../prompts/pnl-templates.js`

## Usage

Controllers and services import utility functions:
```javascript
import { generatePrompt, SYSTEM_PROMPTS } from '../utils/prompts.js';
```

The utilities are designed to be stateless and side-effect free, making them easy to test and reuse across different parts of the application.