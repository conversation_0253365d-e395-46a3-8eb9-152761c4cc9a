/**
 * @typedef {Object} PromptParams
 * @property {any} pnlData - Single PnL data object for individual analysis
 * @property {Object} userSelections
 * @property {number} userSelections.totalFTE
 * @property {number} userSelections.totalInvestment
 * @property {string} [customTemplate] - Optional custom prompt template
 * @property {Object} [templateVariables] - Additional variables for template substitution
 */

import { SYSTEM_PROMPTS } from '../../prompts/system.js';
import {
  TEMPLATE_VARIABLES,
  PNL_ANALYSIS_INSTRUCTIONS,
  PNL_RESPONSE_EXAMPLE,
  PNL_FORMAT_NOTE,
  DEFAULT_PNL_TEMPLATE
} from '../../prompts/pnl-templates.js';

export { SYSTEM_PROMPTS, TEMPLATE_VARIABLES };

/**
 * Substitutes template variables with actual values
 * @param {string} template - The template string with variables
 * @param {Object} variables - Object containing variable values
 * @returns {string} - Template with variables substituted
 */
const substituteTemplateVariables = (template, variables) => {
  let result = template;

  // Replace each variable in the template
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `{{${key}}}`;
    result = result.replace(new RegExp(placeholder, 'g'), value);
  });

  return result;
};

/**
 * @param {PromptParams} params
 * @returns {string}
 */
export const generatePNLAnalysisPrompt = ({ pnlData, userSelections, customTemplate, templateVariables = {} }) => {
  // Use custom template if provided, otherwise use default
  const template = customTemplate || DEFAULT_PNL_TEMPLATE;

  // Extract detailed selection information
  const sliderDetails = userSelections.sliderSelections ?
    Object.entries(userSelections.sliderSelections).map(([fieldName, selection]) =>
      `${fieldName}: ${selection.selectedLabel} (FTE: ${selection.fte}, Investment: $${Number(selection.investment).toLocaleString()})`
    ).join('\n  - ') : 'No slider selections';

  const incentiveDetails = userSelections.incentiveSelections ?
    userSelections.incentiveSelections.filter(Boolean).map((incentive, idx) =>
      `Initiative ${idx + 1}: ${incentive.label} (FTE: ${incentive.fte}, Investment: $${Number(incentive.investment).toLocaleString()})`
    ).join('\n  - ') : 'No incentive selections';

  // Format P&L data as a clear table structure for the AI
  const formatPnLForAI = (pnlData) => {
    if (!Array.isArray(pnlData) || pnlData.length === 0) {
      return "No P&L data provided";
    }

    // Convert array of arrays to a readable table format
    const headers = pnlData[0];
    const rows = pnlData.slice(1);

    let tableString = `P&L Table Structure:\nHeaders: [${headers.map(h => `"${h}"`).join(', ')}]\n\nData Rows:\n`;
    rows.forEach((row, index) => {
      tableString += `Row ${index + 1}: [${row.map(cell => `"${cell}"`).join(', ')}]\n`;
    });

    return tableString;
  };

  // Prepare standard template variables
  const standardVariables = {
    PNL_DATA: formatPnLForAI(pnlData),
    TOTAL_FTE: userSelections.totalFTE,
    TOTAL_INVESTMENT: userSelections.totalInvestment,
    FORMATTED_INVESTMENT: `$${userSelections.totalInvestment?.toLocaleString() || '0'}`,
    USER_SELECTIONS: JSON.stringify(userSelections, null, 2),
    SLIDER_DETAILS: sliderDetails,
    INCENTIVE_DETAILS: incentiveDetails,
    PAGE_NAME: templateVariables.pageName || 'Unknown Page',
    SCHEME_NAME: templateVariables.schemeName || 'Unknown Scheme',
    RESPONSE_FORMAT: `{
    "updatedPnL": [
      [...originalHeaderRow, "Adjusted PNL"],
      [...originalDataRow1, calculatedAdjustment1],
      [...originalDataRow2, calculatedAdjustment2]
    ],
    "analysis": "Your detailed analysis here"
  }`,
    ANALYSIS_INSTRUCTIONS: PNL_ANALYSIS_INSTRUCTIONS,
    FORMAT_NOTE: PNL_FORMAT_NOTE,
    RESPONSE_EXAMPLE: PNL_RESPONSE_EXAMPLE,
  };

  // Merge with any additional template variables
  const allVariables = { ...standardVariables, ...templateVariables };

  // Substitute variables in the template
  return substituteTemplateVariables(template, allVariables);
};

/**
 * Gets the appropriate prompt template for a given scheme and page
 * @param {Object} schemeData - Decision scheme object with template fields
 * @param {Array} pagesData - Array of decision page objects with template fields
 * @param {number} pnlIndex - Index of the current PNL being processed (0 = main, 1+ = pages)
 * @param {Array} _pages - Original pages array from frontend (unused but kept for API compatibility)
 * @returns {string|null} - Custom template if available, null for default
 */
export const getPromptTemplate = (schemeData, pagesData, pnlIndex, _pages) => {
  // For main PNL (index 0), check scheme-level template
  if (pnlIndex === 0) {
    if (schemeData?.use_custom_global_template && schemeData?.global_pnl_prompt_template) {
      return schemeData.global_pnl_prompt_template;
    }
    return null;
  }

  // For page PNLs (index 1+), check page-level template
  const pageIndex = pnlIndex - 1;
  if (pagesData && pageIndex < pagesData.length) {
    const pageData = pagesData[pageIndex];
    if (pageData?.use_custom_page_template && pageData?.page_pnl_prompt_template) {
      return pageData.page_pnl_prompt_template;
    }
  }

  // Fallback to scheme-level template if page doesn't have custom template
  if (schemeData?.use_custom_global_template && schemeData?.global_pnl_prompt_template) {
    return schemeData.global_pnl_prompt_template;
  }

  // Return null to use default template
  return null;
};
