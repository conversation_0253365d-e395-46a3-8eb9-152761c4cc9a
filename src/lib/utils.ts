import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import memoize from "lodash.memoize";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Memoize the string conversion for better performance
const snakeToCamelStr = memoize((str) => {
  return str
    .toLowerCase()
    .replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
});

/**
 * Converts all keys in an object from snake_case to camelCase
 * Works with nested objects and arrays
 *
 * @param {Object|Array} input - The input object or array
 * @returns {Object|Array} - A new object/array with converted keys
 */
export const snakeToCamel = (input) => {
  if (Array.isArray(input)) {
    return input.map((item) => snakeToCamel(item));
  }

  if (input !== null && typeof input === "object") {
    return Object.fromEntries(
      Object.entries(input).map(([key, value]) => [
        snakeToCamelStr(key),
        snakeToCamel(value),
      ])
    );
  }

  return input;
};
