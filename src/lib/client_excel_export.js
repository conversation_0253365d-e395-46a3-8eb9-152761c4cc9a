import excel from 'node-excel-export';
import styles from './excel_styles.js';

export default function getExcel(client) {
  const { teams, challenges, initiatives, orgChartUsers, selfAssessments, decisions } =
    client;

  const separator1 = [{ col_h_0: '' }];
  const separator2 = [{ col_h_0: '' }];

  teams.forEach((team, index) => (separator1[0][`col_h_${index + 1}`] = ''));

  let separator1Length = Object.keys(separator1[0]).length;
  const separator2Length = 9;

  if (separator1Length < separator2Length) {
    separator1Length = separator2Length;
  }

  new Array(separator1Length)
    .fill(null)
    .forEach((_, index) => (separator1[0][`col_h_${index + 1}`] = ''));

  new Array(separator2Length)
    .fill(null)
    .forEach((_, index) => (separator2[0][`col_h_${index + 1}`] = ''));

  const selfAssessmentSeparatorKeys = Object.keys(separator2[0]);
  const separatorKeys = Object.keys(separator1[0]);

  // Specification for main sheet
  const specificationClientsData = separatorKeys.reduce((acc, key, index) => {
    let displayName = '';
    if (key === 'col_h_0') {
      acc[key] = {
        displayName: 'Data',
        headerStyle: styles.header,
        width: 200,
      };
      return acc;
    } else {
      const relatedTeam = teams[index - 1];
      displayName = relatedTeam?.name || '';
    }
    acc[key] = { displayName, headerStyle: styles.header, width: 200 };
    return acc;
  }, {});

  // Specification for self assessment sheet data (unchanged)
  const specificationSelfAssessmentData = selfAssessmentSeparatorKeys.reduce(
    (acc, key, index) => {
      let displayName = '';
      if (index === 0) displayName = 'Team user';
      else if (index < 5) displayName = `Normal - [Quadrant #${index}]`;
      else if (index < 9) displayName = `Stress - [Quadrant #${index - 4}]`;
      else displayName = 'Date';
      acc[key] = displayName;
      return acc;
    },
    {}
  );

  // Specification for the Decisions sheet
  const specificationDecisions = {
    col_h_0: { displayName: 'Team', headerStyle: styles.header, width: 150 },
    col_h_1: { displayName: 'Page', headerStyle: styles.header, width: 150 },
    col_h_2: { displayName: 'Decision', headerStyle: styles.header, width: 200 },
    col_h_3: { displayName: 'Selection', headerStyle: styles.header, width: 200 },
    col_h_4: { displayName: 'FTE', headerStyle: styles.header, width: 100 },
    col_h_5: { displayName: 'Investment', headerStyle: styles.header, width: 100 },
  };

  // Build data rows for decisions sheet
  const decisionsRows = [];

  // Add header for decisions section
  decisionsRows.push({
    col_h_0: 'DECISIONS',
  });

  // Add decision scheme name if available
  if (decisions && decisions.name) {
    decisionsRows.push({
      col_h_0: `Decision Scheme: ${decisions.name}`,
    });
  }

  // Add empty row as separator
  decisionsRows.push({
    col_h_0: '',
  });

  // Process each team's decisions
  teams.forEach(team => {
    // Add team header
    decisionsRows.push({
      col_h_0: team.name,
      col_h_1: 'TOTALS',
      col_h_2: '',
      col_h_3: '',
      col_h_4: team.decisions?.totalFTE || 0,
      col_h_5: team.decisions?.totalInvestment || 0,
    });

    // Process each decision page
    if (decisions.pages && decisions.pages.length > 0) {
      decisions.pages.forEach((page, pageIndex) => {
        // Get team's selections for this page
        const pageSelections = team.decisions?.selectedValues?.pages?.[pageIndex] || {};

        // Add page header
        decisionsRows.push({
          col_h_0: '',
          col_h_1: `Page ${pageIndex + 1}`,
          col_h_2: page.page_name || `Page ${pageIndex + 1}`,
          col_h_3: '',
          col_h_4: '',
          col_h_5: '',
        });

        // Process sliders
        if (page.sliders) {
          Object.entries(page.sliders).forEach(([sliderId, sliderConfig]) => {
            const selectedValue = pageSelections.sliders?.[sliderId];
            const selectedLabel = sliderConfig.labels?.[selectedValue];

            decisionsRows.push({
              col_h_0: '',
              col_h_1: '',
              col_h_2: sliderConfig.fieldName || `Slider ${sliderId}`,
              col_h_3: selectedLabel ? selectedLabel.text : 'Not selected',
              col_h_4: selectedLabel ? selectedLabel.fte || 0 : 0,
              col_h_5: selectedLabel ? selectedLabel.investment || 0 : 0,
            });
          });
        }

        // Process incentives - fix the selectedIncentives array handling
        if (page.incentives && page.incentives.list && pageSelections.selectedIncentives) {
          pageSelections.selectedIncentives.forEach((selectedIncentive, incentiveIndex) => {
            if (selectedIncentive) {
              decisionsRows.push({
                col_h_0: '',
                col_h_1: '',
                col_h_2: `Incentive ${incentiveIndex + 1}`,
                col_h_3: selectedIncentive.label || `Selected Incentive ${incentiveIndex + 1}`,
                col_h_4: selectedIncentive.fte || 0,
                col_h_5: selectedIncentive.investment || 0,
              });
            }
          });
        }

        // Add empty row as separator
        decisionsRows.push({
          col_h_0: '',
        });
      });
    } else {
      // No pages found
      decisionsRows.push({
        col_h_0: '',
        col_h_1: '',
        col_h_2: 'No decision pages found',
        col_h_3: '',
        col_h_4: '',
        col_h_5: '',
      });
    }

    // Add empty row as separator between teams
    decisionsRows.push({
      col_h_0: '',
    });
  });

  // Build the main client data rows (example code)
  const goals = [];
  const metrics = [];
  const challengesRows = [];
  const initiativesRows = [];
  const orgChartUsersRows = [];
  const decisionFieldsRows = [];

  for (let i = 1, n = 4; i < n; ++i) {
    const goal = separatorKeys.reduce((acc, key, index) => {
      if (key === 'col_h_0') {
        acc[key] = `Goal ${i}`;
      } else {
        const relatedTeam = teams[index - 1];
        acc[key] = relatedTeam ? relatedTeam[`goal${i}`] : '';
      }
      return acc;
    }, {});
    const metric = separatorKeys.reduce((acc, key, index) => {
      if (key === 'col_h_0') {
        acc[key] = `Metric ${i}`;
      } else {
        const relatedTeam = teams[index - 1];
        acc[key] = relatedTeam ? relatedTeam[`metric${i}`] : '';
      }
      return acc;
    }, {});
    goals.push(goal);
    metrics.push(metric);
  }

  orgChartUsers.forEach((orgChartUser) => {
    const orgChartUserRow = separatorKeys.reduce((acc, key, index) => {
      if (key === 'col_h_0') {
        acc[key] = orgChartUser.name;
      } else {
        const relatedTeam = teams[index - 1];
        const relatedTeamInOrgChartUsers = orgChartUser.teams.find(
          ({ id }) => id === relatedTeam?.id
        );

        if (!relatedTeamInOrgChartUsers) return acc;

        const { value } = relatedTeamInOrgChartUsers;

        if (value) acc[key] = value;
        else acc[key] = '';
      }

      return acc;
    }, {});

    orgChartUsersRows.push(orgChartUserRow);
  });

  const selfAssessmentRows = selfAssessments.data.map((item) =>
    selfAssessmentSeparatorKeys.reduce((acc, key, index) => {
      if (item) {
        acc[key] = Object.values(item)[index];
      }
      return acc;
    }, {})
  );

  initiatives.forEach((initiative) => {
    const initiativeRow = separatorKeys.reduce((acc, key, index) => {
      if (key === 'col_h_0') {
        acc[key] = initiative.name;
      } else {
        const relatedTeam = teams[index - 1];

        if (!relatedTeam) return acc;

        const { initiatives } = relatedTeam;
        const relatedInitiative = initiatives.find(
          ({ id }) => id === initiative?.id
        );

        if (relatedInitiative) acc[key] = relatedInitiative.option;
      }

      return acc;
    }, {});

    initiativesRows.push(initiativeRow);
  });

  challenges.forEach((challenge) => {
    const challengeRow = separatorKeys.reduce((acc, key, index) => {
      if (key === 'col_h_0') {
        acc[key] = challenge.name;
      } else {
        const relatedTeam = teams[index - 1];

        if (!relatedTeam) return acc;

        const { challenges } = relatedTeam;
        const relatedChallenge = challenges.find(
          ({ id }) => id === challenge?.id
        );

        if (relatedChallenge) acc[key] = relatedChallenge.option;
      }

      return acc;
    }, {});

    challengesRows.push(challengeRow);
  });

  // Build decision field rows - show decision field names in left column with team selections
  if (decisions.pages && decisions.pages.length > 0) {
    decisions.pages.forEach((page, pageIndex) => {
      // Process sliders for this page
      if (page.sliders) {
        Object.entries(page.sliders).forEach(([sliderId, sliderConfig]) => {
          const decisionFieldRow = separatorKeys.reduce((acc, key, index) => {
            if (key === 'col_h_0') {
              acc[key] = sliderConfig.fieldName || `${page.page_name || `Page ${pageIndex + 1}`} - ${sliderId}`;
            } else {
              const relatedTeam = teams[index - 1];

              if (!relatedTeam || !relatedTeam.decisions?.selectedValues?.pages?.[pageIndex]) {
                acc[key] = 'Not selected';
                return acc;
              }

              const pageSelections = relatedTeam.decisions.selectedValues.pages[pageIndex];
              const selectedValue = pageSelections.sliders?.[sliderId];

              if (selectedValue !== undefined) {
                const selectedLabel = sliderConfig.labels?.[selectedValue];
                acc[key] = selectedLabel ? `${selectedLabel.text} (Option ${selectedValue + 1})` : `Option ${selectedValue + 1}`;
              } else {
                acc[key] = 'Not selected';
              }
            }

            return acc;
          }, {});

          decisionFieldsRows.push(decisionFieldRow);
        });
      }

      // Process incentives for this page
      if (page.incentives && page.incentives.list) {
        const incentiveFieldRow = separatorKeys.reduce((acc, key, index) => {
          if (key === 'col_h_0') {
            acc[key] = `${page.page_name || `Page ${pageIndex + 1}`} - Incentives`;
          } else {
            const relatedTeam = teams[index - 1];

            if (!relatedTeam || !relatedTeam.decisions?.selectedValues?.pages?.[pageIndex]) {
              acc[key] = 'None selected';
              return acc;
            }

            const pageSelections = relatedTeam.decisions.selectedValues.pages[pageIndex];
            const selectedIncentives = pageSelections.selectedIncentives || [];

            if (selectedIncentives.length > 0) {
              const incentiveLabels = selectedIncentives
                .filter(incentive => incentive)
                .map((incentive, idx) => `${incentive.label || `Incentive ${idx + 1}`}`)
                .join(', ');
              acc[key] = incentiveLabels || 'None selected';
            } else {
              acc[key] = 'None selected';
            }
          }

          return acc;
        }, {});

        decisionFieldsRows.push(incentiveFieldRow);
      }
    });
  }

  const clientsData = [
    ...separator1,
    ...metrics,
    ...separator1,
    ...goals,
    ...separator1,
    ...initiativesRows,
    ...separator1,
    ...challengesRows,
    ...separator1,
    // Add decision fields section if there are any decision fields
    ...(decisionFieldsRows.length > 0 ? [
      {
        col_h_0: `${decisions.name || 'Decisions'} - Decision Field Names & Selected Options`,
      },
      ...separator1,
      ...decisionFieldsRows,
      ...separator1,
    ] : []),
    {
      col_h_0: `${decisions.name || 'Decisions'} - Detailed Decision Data for Teams`,
    },
    ...decisionsRows,
    ...separator2,
    {
      col_h_0: `${selfAssessments.name} - Self Assessment Data for Team Users`,
    },
    specificationSelfAssessmentData,
    ...selfAssessments.data,
  ];

  // Build export with an extra Decisions sheet
  const report = excel.buildExport([
    {
      name: 'Client Data',
      specification: specificationClientsData,
      data: clientsData,
      merges: [
        { start: { row: 25, column: 1 }, end: { row: 25, column: 8 } },
      ],
    },
    {
      name: 'Org Charts',
      specification: specificationClientsData,
      data: [...separator1, ...orgChartUsersRows],
    },
    {
      name: 'Decisions',
      specification: specificationDecisions,
      data: decisionsRows,
    },
  ]);

  return report;
}
