# Library (lib)

This directory contains shared library functions and utilities for the Lumen simulation platform.

## Overview

The lib directory provides common functionality and integrations used across multiple parts of the application. These modules handle cross-cutting concerns like authentication, API responses, file processing, and external service integrations.

## Core Libraries

### API & Communication
- **api_client.js** - Centralized API client for external service communication
- **index.js** - Main library exports and module aggregation

### Authentication & Security
- **jwt_middleware.js** - JSON Web Token authentication middleware
  - Token validation and verification
  - User authentication from JWT claims
  - Authorization header processing
  - Integration with application security model

### Data Export & Reporting
- **excel_styles.js** - Excel formatting and styling utilities
- **client_excel_export.js** - Client-specific Excel report generation
- **team_excel_export.js** - Team performance Excel exports
- **all_teams_export_excel.js** - Comprehensive team data exports

### PDF Generation
The `pdf/` subdirectory contains:
- **export_self_assessment_pdf.js** - Self-assessment report PDF generation
- **fonts.js** - Font management and loading for PDFs
- **getSvg.js** - SVG processing and conversion utilities
- **get_pdf_page_1.js** - First page template for PDF reports
- **get_pdf_page_2.js** - Second page template for PDF reports
- **Font files**: Helvetica_Neue_Bold.ttf, Helvetica_Neue_Regular.otf

## Key Features

### Authentication Middleware
JWT middleware provides:
- Bearer token extraction from headers
- Token verification against application secret
- User context injection into requests
- Unauthorized access handling

### Export Functionality
Excel export libraries offer:
- Styled spreadsheet generation
- Client and team data formatting
- Performance metrics visualization
- Multi-sheet workbook creation

### PDF Reports
PDF generation system includes:
- Self-assessment report creation
- Custom font support
- Multi-page document templates
- SVG graphics integration

## Dependencies

- **jsonwebtoken** - JWT token handling
- **Application config** - Configuration and secrets management
- **External APIs** - Integration with third-party services

## Usage

Library functions are imported throughout the application:
```javascript
import { apiClient } from '../lib/index.js';
import jwtMiddleware from '../lib/jwt_middleware.js';
```

These libraries provide foundational functionality that supports the core business logic of the simulation platform.