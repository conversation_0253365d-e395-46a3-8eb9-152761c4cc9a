import { fonts } from './fonts.js';

export const getPdfPage1 = ({
  logo,
  clientName,
  userName,
  date,
  dominantProfile,
  coverImage,
}) => {
  // TODO: Remove hardcoded display title

  const copyrightYear = new Date().getFullYear();

  const document =
    '' +
    `<!DOCTYPE html>
		<html lang="en">
			<head>
				<meta charset="UTF-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">

				<title>Document</title>

				<style>
					${fonts}

					* {
						font-family: "Cal<PERSON>ri", Calibri, work-Sans, sans-serif;
						box-sizing: border-box;
					}

					body {
						height: 99vh;
						max-height: 99vh;
						margin: 0;
						overflow: hidden;
					}

					.container {
						padding: 0;
						height: 100%;
						display: grid;
						grid-template-rows: 20% 1fr 20%;
					}

					.center {
						align-self: center;
						display: flex;
						justify-content: center;
						align-items: center;
						flex-direction: column;
					}

					.logo {
						max-width: 100%;
						max-height: 120px;
						position: absolute;
						right: 15px;
						top: 15px;
						border-radius: 10px;
						overflow: none;
					}

					.cover-image {
						min-height: 550px;
						max-height: 550px;
						max-width: 100%;
					}

					.cover-image-wrapper {
						justify-self: center;
					}

					.title {
						font-weight: 500;
						padding: 10px;
						padding-left: 30px;
					}

					.sub-title {
						font-style: normal;
						font-size: 18px;
						line-height: 18px;
						color: #333333;
						margin: 0;
						margin-bottom: 20px;
						text-align: center;
					}
					.sub-title span { line-height: 30px; }
					.sub-title-1 { font-size: 22px; font-weight: 900; }
					.sub-title-2 { font-size: 24px; }
					.sub-title small {
						font-size: 14px;
						color: #A0A0A0;
					}

					.infotext {
						font-style: normal;
						font-weight: 400;
						font-size: 12px;
						line-height: 14px;
						color: #333333;
						margin:0;
						margin-bottom: 12px;
					}

					.infotext_bigger {
						font-size: 16px;
					}

					.infotext:last-of-type {
						margin-bottom: 0;
					}

					.text {
						font-style: normal;
						font-weight: 700;
						line-height: 15px;
						color: #333333;
					}

					.first-block {
						align-self: start;
						justify-items: space-between;
						align-items: space-between;
						background-color: black;
						color: white;
						font-size: 16px;
						border-bottom: 3px solid black;
						min-height: 500px;
						overflow: hidden;
						text-align: center;
					}
					.first-block h2 {
						align-self: start;
						text-align: left;
					}
					.first-block img {
						align-self: center;
					}

					.bottom-left-text-block {
						align-self: end;
						padding: 0 0 10px 20px;
					}
					.bottom-right-text-block {
						position: absolute;
						bottom: 20px;
						right: 20px;
						border-left: 2px solid black;
						padding: 10px 10px 10px 20px;
						text-align: center;
					}

					.document-title {
						position: relative;
						top: 550px;
						height: 150px;
						overflow: hidden;
					}
				</style>
			</head>

			<body>
				<div class="container">
					<div class="center first-block">
					<!-- Harcoded for now -->
					<h2 class="title">ARNG Communication Report</h2>

					<img src="data:image;base64, ${logo}" alt="" class="logo" />

					<img src="data:image;base64, ${coverImage}" alt="" class="cover-image" />
				</div>

				<div class="document-title">
					<p class="sub-title">
						<b>${userName}</b><br/><br/>
						<span class="sub-title-1">Your ARNG Communication Styles Report</span><br/>
						<!-- span class="sub-title-2">ARNG Communication Self-Assessment Report</span><br/-->
						<small>Assessment Date: ${date}</small>
					</p>
				</div>

				<div class="bottom-left-text-block">
					<p class="infotext infotext_bigger">Dominant Profile: <br/><span class="text">${dominantProfile}</span></p>
					<p class="infotext">Copyright &copy; Advantage Performance Group ${copyrightYear}. All Rights Reserved</span></p>
				</div>

				<div class="bottom-right-text-block">
					<p class="infotext"><b>ASSESSMENT</b></p>
					<p class="infotext">R E P O R T</span></p>
				</div>
			</body>
		</html>
	`;

  return document;
};
