import AWS from 'aws-sdk';
import { PDFDocument } from 'pdf-lib';
import puppeteer from 'puppeteer';
import config from '../../../config.json' with { type: 'json' };
import { getPdfPage1 } from './get_pdf_page_1.js';
import { getPdfPage2 } from './get_pdf_page_2.js';
import { format } from 'date-fns';
// import { jsPDF } from 'jspdf';
// import htmlPdf from 'html-pdf';

const QUADRANTS_COUNT = 4;

function generatePairs(current = 1, result = []) {
  if (current > QUADRANTS_COUNT) return result;
  for (let i = 1; i <= QUADRANTS_COUNT; i++) {
    if (i !== current) {
      result.push(`${current}_${i}`);
    }
  }
  return generatePairs(current + 1, result);
}

const numberPairs = generatePairs();

const credentials = new AWS.Credentials(
  config.aws.accessKeyId,
  config.aws.secretAccessKey
);
const s3 = new AWS.S3({ credentials });

const params = {
  Bucket: config.aws.bucketName,
};

function findParagraphIndexDependingOnDominants(firstKey, secondKey) {
  return numberPairs.findIndex((item) => item === `${firstKey}_${secondKey}`);
}

export const exportSelfAssessmentPdf = async ({
  answers,
  pdfConfig,
  paragraphsConfig,
  client,
  team,
  quadrantsConfig,
}) => {
  const {
    normal_quadrant_1_questions,
    normal_quadrant_2_questions,
    normal_quadrant_3_questions,
    normal_quadrant_4_questions,
    stress_quadrant_1_questions,
    stress_quadrant_2_questions,
    stress_quadrant_3_questions,
    stress_quadrant_4_questions,
  } = answers;

  const sortedNormalAnswers = sortQuadrantsAnswers({
    normal_quadrant_1_questions,
    normal_quadrant_2_questions,
    normal_quadrant_3_questions,
    normal_quadrant_4_questions,
  });

  const sortedStressAnswers = sortQuadrantsAnswers({
    stress_quadrant_1_questions,
    stress_quadrant_2_questions,
    stress_quadrant_3_questions,
    stress_quadrant_4_questions,
  });
  const sortedNormProfilesKeys = [
    getQuadrantNumber(sortedNormalAnswers[0][0]),
    getQuadrantNumber(sortedNormalAnswers[1][0]),
    getQuadrantNumber(sortedNormalAnswers[2][0]),
    getQuadrantNumber(sortedNormalAnswers[3][0]),
  ];

  const sortedStressProfilesKeys = [
    getQuadrantNumber(sortedStressAnswers[0][0]),
    getQuadrantNumber(sortedStressAnswers[1][0]),
    getQuadrantNumber(sortedStressAnswers[2][0]),
    getQuadrantNumber(sortedStressAnswers[3][0]),
  ];

  const normalQuadrantsNames = sortedNormProfilesKeys.map(
    (num) => quadrantsConfig[`quadrant_${num}_name`]
  );
  const stressQuadrantsNames = sortedStressProfilesKeys.map(
    (num) => quadrantsConfig[`quadrant_${num}_name`]
  );

  const normalParagraphIndex = findParagraphIndexDependingOnDominants(
    sortedNormProfilesKeys[0],
    sortedNormProfilesKeys[1]
  );

  const stressParagraphIndex = findParagraphIndexDependingOnDominants(
    sortedStressProfilesKeys[0],
    sortedStressProfilesKeys[1]
  );

  if (!client.logo_image) {
    throw new Error('No logo image');
  }

  if (!pdfConfig.cover_image) {
    throw new Error('No PDF cover image');
  }

  if (!pdfConfig[`page_2_quad_${sortedNormProfilesKeys[0]}_pdf`]) {
    throw new Error('Second uploaded PDF page not found');
  }

  if (!pdfConfig[`page_3_quad_${sortedNormProfilesKeys[0]}_pdf`]) {
    throw new Error('Third uploaded PDF page not found');
  }

  const [logoBuffer, coverImageBuffer, secondPageBuffer, thirdPageBuffer] =
    await Promise.all([
      getS3FileViaUrl(client.logo_image),
      getS3FileViaUrl(pdfConfig.cover_image),
      getS3FileViaUrl(
        pdfConfig[`page_2_quad_${sortedNormProfilesKeys[0]}_pdf`]
      ),
      getS3FileViaUrl(
        pdfConfig[`page_3_quad_${sortedNormProfilesKeys[0]}_pdf`]
      ),
    ]);
  const date = format(new Date(answers.created_at), 'yyyy-L-dd H:mm:ss'); // 'EEEE, MMMM do, u'

  const firstPageHtml = getPdfPage1({
    userName: team.team_name,
    date,
    dominantProfile:
      quadrantsConfig[`quadrant_${sortedNormProfilesKeys[0]}_name`],
    clientName: client.name,
    coverImage: coverImageBuffer.toString('base64'),
    logo: logoBuffer.toString('base64'),
  });

  const scorePageHtml = getPdfPage2({
    logo: logoBuffer.toString('base64'),
    userName: team.team_name,
    normalParagraph: paragraphsConfig.normal_paragraphs[normalParagraphIndex],
    stressParagraph: paragraphsConfig.stress_paragraphs[stressParagraphIndex],
    sortedNormalAnswers: sortedNormalAnswers.map(
      (item) => { item[1] = parseFloat(item[1]).toFixed(1); return item; }
    ),
    sortedStressAnswers: sortedStressAnswers.map(
      (item) => { item[1] = parseFloat(item[1]).toFixed(1); return item; }
    ),
    normalQuadrantsNames,
    stressQuadrantsNames,
    quadrantsConfig,
  });

  const [firstPageBuffer, scorePageBuffer] = await Promise.all([
    createPdfFromTemplateUsingPuppeteer(firstPageHtml),
    createPdfFromTemplateUsingPuppeteer(scorePageHtml),
  ]);

  const file = mergePdfBuffersIntoOneFile([
    firstPageBuffer,
    scorePageBuffer,
    secondPageBuffer,
    thirdPageBuffer,
  ]);

  return file;
};

async function mergePdfBuffersIntoOneFile(pdfBuffers) {
  const pdfDoc = await PDFDocument.create();
  const pdfDonors = await Promise.all(
    pdfBuffers.map((buffer) => PDFDocument.load(buffer))
  );
  const allPages = [];

  for (const donor of pdfDonors) {
    const pages = await pdfDoc.copyPages(donor, donor.getPageIndices());

    allPages.push(...pages);
  }

  for (const page of allPages) {
    pdfDoc.addPage(page);
  }

  const file = await pdfDoc.save();

  return file;
}

// async function createPdfFromTemplateUsingHtmlPdf(htmlTemplate) {
//   htmlPdf.create(htmlTemplate).toFile('test.pdf', (err, res) => {
//     if (err) return console.log(err);
//   });

//   return htmlPdf.create(htmlTemplate).toBuffer();
// }

async function createPdfFromTemplateUsingPuppeteer(htmlTemplate) {
  // Launch a headless browser
  const browser = await puppeteer.launch();

  // Create a new page
  const page = await browser.newPage();

  // Set the content of the page to your HTML content

  await page.setContent(htmlTemplate);

  // Generate a PDF with good image quality
  const pdfBuffer = await page.pdf({
    format: 'Letter',
    printBackground: true, // Include background graphics
  });

  // Close the browser
  await browser.close();

  return pdfBuffer;
}
// async function createPdfFromTemplateUsingJsPdf(htmlTemplate) {
//   const doc = new jsPDF({
//     orientation: 'portrait',
//     unit: 'in',
//     format: [8.5, 11],
//   });

//   doc.text(htmlTemplate, 10, 10);

//   doc.save('a4.pdf');
//   // return pdfBuffer;
// }

async function getS3FileViaUrl(url) {
  const Key = url.replace('https://', '').split('/').splice(2).join('/');

  const response = await s3.getObject({ ...params, Key }).promise();

  return response.Body;
}

function sortQuadrantsAnswers(points) {
  const poinstArray = Object.entries(points);
  const sorted = [...poinstArray].sort((a, b) => {
    if ((a[1] === b) === 1) return a[0].localeCompare(b[0]);

    return Number(b[1]) - Number(a[1]);
  });

  return sorted;
}

export function getQuadrantNumber(key) {
  return Number(key.split('_')[2]);
}
