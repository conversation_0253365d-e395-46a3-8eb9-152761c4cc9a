import D3Node, { d3 } from 'd3-node';
import { getQuadrantNumber } from './export_self_assessment_pdf.js';
const d3n = new D3Node();

const axes = ['y', 'x'];

function offsetIfDataPointsAreSimilar(dataPoints) {
  const dataPointsAreSimilar =
    dataPoints[0][axes[0]] === dataPoints[1][axes[0]] &&
    dataPoints[0][axes[1]] === dataPoints[1][axes[1]];

  if (!dataPointsAreSimilar) return dataPoints;

  const newDataPoints = [...dataPoints];

  const newFirstDataPoint = dataPoints[0];

  if (newFirstDataPoint[axes[0]] > 0) {
    newFirstDataPoint[axes[0]] = newFirstDataPoint[axes[0]] - 0.25;
  } else {
    newFirstDataPoint[axes[1]] = newFirstDataPoint[axes[1]] - 0.25;
  }

  newDataPoints[0] = newFirstDataPoint;

  return newDataPoints;
}

export const getSvg = ({ values, quadrantsOrder }) => {
  // Define the chart dimensions
  const radiusPointAdjustment = 60;
  const heightPointAdjustment = 28;
  const WIDTH = 500;
  const HEIGHT = 500;
  const CIRCLE_RADIUS = WIDTH / 2.5;

  /* leave it in case we return to swapping logic
  const QUADRANTS_MAP = new Map();
	
  quadrantsOrder.forEach((num, index) => {
		QUADRANTS_MAP.set(num, {
			axis: index % 2 === 0 ? 'y' : 'x',
			multiplier: index <= 1 ? 1 : -1,
		});
	});
	*/
  const QUADRANTS_MAP = new Map();
  quadrantsOrder.forEach((num, index) => {
    let axis;
    const multiplier = index <= 1 ? 1 : -1;

    if (index === 0 || index === 3) {
      axis = axes[0];
    } else {
      axis = axes[1];
    }
    QUADRANTS_MAP.set(num, {
      axis,
      multiplier,
    });
  });

  let dataPoints = [{}, {}];

  values.forEach((val, index) => {
    const key = getQuadrantNumber(val[0]);
    const { axis, multiplier } = QUADRANTS_MAP.get(key);
    let value = 0;

    if (val[1] > 0) {
      value = val[1] * multiplier;
    }

    const dataPointIndex = index > 1 ? 1 : 0;

    if (dataPoints[dataPointIndex].hasOwnProperty(axis)) {
      const missingAxis = axes.filter((ax) => ax !== axis);
      dataPoints[dataPointIndex][missingAxis] = 0;
    } else {
      dataPoints[dataPointIndex][axis] = value;
    }
  });

  dataPoints = offsetIfDataPointsAreSimilar(dataPoints);

  // Create SVG element
  const svg = d3n.createSVG(WIDTH, HEIGHT);

  svg.attr('transform', `rotate(-45)`);

  // Create a yellow circle
  svg
    .append('circle')
    .attr('cx', WIDTH / 2)
    .attr('cy', HEIGHT / 2)
    .attr('cx', WIDTH / 2)
    .attr('cy', HEIGHT / 2)
    .attr('r', CIRCLE_RADIUS)
    .style('fill', '#E3B500');

  const DOMAIN_SINGLE_VAL = 5;

  // Define scales for x and y axes
  const xScale = d3
    .scaleLinear()
    .domain([-DOMAIN_SINGLE_VAL, DOMAIN_SINGLE_VAL])
    .range([-CIRCLE_RADIUS, CIRCLE_RADIUS]);

  const yScale = d3
    .scaleLinear()
    .domain([-DOMAIN_SINGLE_VAL, DOMAIN_SINGLE_VAL])
    .range([-CIRCLE_RADIUS, CIRCLE_RADIUS]);

  // Custom star path data

  // Add a vertical line along the x-axis with arrows on both sides
  svg
    .append('line')
    .attr(
      'x1',
      WIDTH / 2 + CIRCLE_RADIUS - (CIRCLE_RADIUS / DOMAIN_SINGLE_VAL) * 1.5
    )
    .attr(
      'y1',
      HEIGHT / 2 + CIRCLE_RADIUS - (CIRCLE_RADIUS / DOMAIN_SINGLE_VAL) * 1.5
    ) // Adjust the vertical position as needed
    .attr(
      'x2',
      WIDTH / 2 - CIRCLE_RADIUS + (CIRCLE_RADIUS / DOMAIN_SINGLE_VAL) * 1.5
    )
    .attr(
      'y2',
      HEIGHT / 2 - CIRCLE_RADIUS + (CIRCLE_RADIUS / DOMAIN_SINGLE_VAL) * 1.5
    ) // Adjust the vertical position as needed
    .style('stroke', 'black')
    .style('stroke-width', 2)
    .attr('marker-start', 'url(#arrowhead-y-start)') // Attach the arrowhead marker at the start
    .attr('marker-end', 'url(#arrowhead-y-end)'); // Attach the arrowhead marker at the end

  // Add arrowhead markers for the y-axis line (both start and end)
  svg
    .append('defs')
    .append('marker')
    .attr('id', 'arrowhead-y-start')
    .attr('refX', 3) // Adjust the position as needed
    .attr('refY', 2)
    .attr('markerWidth', 12)
    .attr('markerHeight', 12)
    .attr('orient', 'auto-start-reverse')
    .append('path')
    .attr('d', 'M0,0 L4,2 L0,4')
    .style('fill', 'black');

  svg
    .append('defs')
    .append('marker')
    .attr('id', 'arrowhead-y-end')
    .attr('refX', 3)
    .attr('refY', 2)
    .attr('markerWidth', 12)
    .attr('markerHeight', 12)
    .attr('orient', 'auto')
    .append('path')
    .attr('d', 'M0,0 L4,2 L0,4')
    .style('fill', 'black');

  // Add a horizontal line along the x-axis with arrows on both sides
  svg
    .append('line')
    .attr('x1', WIDTH / 2 - CIRCLE_RADIUS + radiusPointAdjustment) // Adjust the horizontal position as needed
    .attr('y1', HEIGHT / 2 + CIRCLE_RADIUS - radiusPointAdjustment)
    .attr('x2', WIDTH / 2 + CIRCLE_RADIUS - radiusPointAdjustment) // Adjust the horizontal position as needed
    .attr('y2', HEIGHT / 2 - CIRCLE_RADIUS + radiusPointAdjustment)
    .style('stroke', 'black')
    .style('stroke-WIDTH', 2)
    .attr('marker-start', 'url(#arrowhead-x-start)') // Attach the arrowhead marker at the start
    .attr('marker-end', 'url(#arrowhead-x-end)'); // Attach the arrowhead marker at the end

  // Add arrowhead markers for the x-axis line (both start and end)
  svg
    .append('defs')
    .append('marker')
    .attr('id', 'arrowhead-x-start')
    .attr('refX', 3)
    .attr('refY', 2) // Adjust the position as needed
    .attr('markerWidth', 12)
    .attr('markerHeight', 12)
    .attr('orient', 'auto-start-reverse')
    .append('path')
    .attr('d', 'M0,0 L4,2 L0,4')
    .style('fill', 'black');

  svg
    .append('defs')
    .append('marker')
    .attr('id', 'arrowhead-x-end')
    .attr('refX', 3)
    .attr('refY', 2)
    .attr('markerWidth', 12)
    .attr('markerHeight', 12)
    .attr('orient', 'auto')
    .append('path')
    .attr('d', 'M0,0 L4,2 L0,4')
    .style('fill', 'black');

  const starPathData =
    'M17.5646 2.73067L14.0403 14.3333H3.1665C2.51431 14.3333 1.93685 14.7547 1.73796 15.3759C1.53906 15.997 1.76432 16.6755 ' +
    '2.29521 17.0543L11.1981 23.4073L7.79778 34.3896C7.60495 35.0125 7.83651 35.6885 8.37069 36.0623C8.90488 36.4361 9.61935 ' +
    '36.422 10.1384 36.0275L19.0006 29.2911L27.878 36.0282C28.3972 36.4222 29.1114 36.4358 29.6453 36.062C30.1792 35.6881 ' +
    '30.4105 35.0122 30.2177 34.3896L26.8171 23.4065L35.7054 17.0536C36.2358 16.6745 36.4606 15.9962 36.2615 15.3753C36.0625 ' +
    '14.7545 35.4852 14.3333 34.8332 14.3333H23.9594L20.4351 2.73067C20.2431 2.09867 19.6603 1.66663 18.9998 1.66663C18.3393 ' +
    '1.66663 17.7566 2.09867 17.5646 2.73067Z';

  const colors = [
    { fill: '#4472C4', stroke: '#073B58' },
    { fill: '#E02727', stroke: '#7B0606' },
  ];

  console.log('dataPoints', dataPoints);

  dataPoints.forEach((d, index) => {
    svg
      .append('path')
      .attr('d', starPathData) // Set the custom star path
      .attr(
        'transform',
        `translate( ${xScale(d.x / 2) + WIDTH / 2 + 1} , ${
          HEIGHT / 2 - yScale(d.y / 2) - heightPointAdjustment
        } ) rotate(45)`
      ) // Position the star

      .style('fill', colors[index].fill)
      .style('stroke', colors[index].stroke)
      .style('stroke-width', 4);
  });

  // Export the SVG as a string
  const svgString = d3n.svgString();

  return svgString;
};
