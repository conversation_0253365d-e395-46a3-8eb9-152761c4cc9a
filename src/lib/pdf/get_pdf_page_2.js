import { fonts } from './fonts.js';
import { getQuadrantNumber } from './export_self_assessment_pdf.js';
import { getSvg } from './getSvg.js';

const quadrantsOrder = [1, 2, 3, 4];

let swappedEarlier = false;
const firstPairIndexes = [];

function swapQuadrantsIfNeeded(sortedAnswers) {
  const firstQuadNumber = getQuadrantNumber(sortedAnswers[0][0]);
  const secondQuadNumber = getQuadrantNumber(sortedAnswers[1][0]);

  const firstQuadIndex = quadrantsOrder.findIndex(
    (num) => num === firstQuadNumber
  );
  const secondQuadIndex = quadrantsOrder.findIndex(
    (num) => num === secondQuadNumber
  );

  const minIndex = Math.min(firstQuadIndex, secondQuadIndex);
  const maxIndex = Math.max(firstQuadIndex, secondQuadIndex);
  const difference = firstQuadIndex - secondQuadIndex;
  const needsSwapping = Math.abs(difference) === 2;

  if (needsSwapping) {
    let prevIndex = minIndex + 1;

    if (swappedEarlier || prevIndex === firstPairIndexes[1]) {
      prevIndex = quadrantsOrder?.[minIndex - 1]
        ? minIndex - 1
        : quadrantsOrder.length - 1;
    } else {
    }

    const prev = quadrantsOrder[prevIndex];

    quadrantsOrder[prevIndex] = quadrantsOrder[maxIndex];
    quadrantsOrder[maxIndex] = prev;

    swappedEarlier = true;
    firstPairIndexes.push(minIndex, prevIndex);

    return;
  }

  firstPairIndexes.push(minIndex, maxIndex);

  return;
}

export const getPdfPage2 = ({
  logo,
  userName,
  sortedNormalAnswers,
  sortedStressAnswers,
  stressParagraph,
  normalParagraph,
  normalQuadrantsNames,
  stressQuadrantsNames,
  quadrantsConfig,
}) => {
  /* Do not swap quadrants; left the function definition above in case we need it in the future
  swapQuadrantsIfNeeded(sortedNormalAnswers);
  swapQuadrantsIfNeeded(sortedStressAnswers);
	*/

  const quadrantsNames = quadrantsOrder.map((num) =>
    quadrantsConfig[`quadrant_${num}_name`]?.toUpperCase()
  );

  const quadrantsAbbreviation = quadrantsNames.reduce(
    (acc, current) => `${acc}${current[0]}`,
    ''
  );

  const valuesForSvg = [
    sortedNormalAnswers[0],
    // sortedNormalAnswers[1],
    // explicitly set second value to zero
    [sortedNormalAnswers[1][0], 0],
    sortedStressAnswers[0],
    // sortedStressAnswers[1],
    // explicitly set second value to zero
    [sortedStressAnswers[1][0], 0],
  ];

  const svgString = getSvg({ values: valuesForSvg, quadrantsOrder });
  const svgStyles = `
		.svg-container {
			position: relative;
			margin-bottom: 30px;
			zoom: 0.55;
		}

		.svg-quadrant-title {
			color: #000000;
			position: absolute;
			// font-family: 'Helvetica Neue';
			font-size: 24px;
			text-transform: uppercase;
		}

		.svg-quadrant-title_1 {
			top: 30px;
			left: -40px;
		}

		.svg-quadrant-title_2 {
			top: 30px;
			right: -40px;
		}

		.svg-quadrant-title_3 {
			bottom: 30px;
			left: -40px;
		}
		
		.svg-quadrant-title_4 {
			bottom: 30px;
			right: -60px;
		}

		.svg-quadrant-subtitle {
			color: #000000;
			position: absolute;
			// font-family: 'Helvetica Neue';
			font-size: 18px;
		}

		.svg-quadrant-subtitle_1 {
			top: -10px;
			left: 200px;
		}

		.svg-quadrant-subtitle_2 {
			bottom: -10px;
			left: 200px;
		}

		.svg-quadrant-subtitle_3 {
			top: 220px;
			left: -25px;
			transform: rotate(-90deg);
		}

		.svg-quadrant-subtitle_4 {
			top: 225px;
			right: 0px;
			transform: rotate(90deg);
		}

		.stars-description {
			color: #333333;
			// font-family: 'Helvetica Neue';
			font-style: normal;
			font-weight: 700;
			font-size: 12px;
			line-height: 130%;
		}

		.blue-star {
			color: #0085FF;
		}

		.red-star {
			color: #D93333;
		}

		.chart-description-item {
			margin-bottom: 5px;
			// font-family: 'Helvetica Neue';
			font-style: normal;
			font-weight: 700;
			font-size: 10px;
			line-height: 130%;
			color: #333333;
			padding-right: 27px;
		}
	`;

  const document =
    '' +
    `<!DOCTYPE html>
	<html lang="en">
		<head>
			<meta charset="UTF-8">
			<meta name="viewport" content="width=device-width, initial-scale=1.0">

			<title>Document</title>

			<style>
				${fonts}

				* {
					font-family: "Calibri", Calibri, work-Sans, sans-serif;
					/* box-sizing: border-box; */
				}

				body {
					width: 100vw;
					height: 99vh;
					max-height: 99vh;
					overflow: hidden;
				}

				.logo {
					position: absolute;
					top: 50px;
					right: 50px;
					max-width: 100%;
					max-height: 75px;
				}

				.container {
					padding: 20px 76px 0px;
					width: 100%;
					max-width: 100%;
					box-sizing: border-box;
				}

				.center {
					display: flex;
					justify-content: center;
					align-items: center;
					flex-direction: column;
					width: 100%;
				}

				.user-name-title {
					// font-family: 'Helvetica Neue';
					font-style: normal;
					font-weight: 900;
					font-size: 24px;
					line-height: 22px;
					display: flex;
					align-items: center;
					text-align: center;
					color: #333333;
					margin-bottom: 20px;
				}

				.title {
					// font-family: 'Helvetica Neue';
					font-style: normal;
					font-weight: 700;
					font-size: 18px;
					line-height: 22px;
					display: flex;
					align-items: center;
					text-align: center;
					text-transform: capitalize;
					color: #333333;
					margin-bottom: 20px;
				}

				.paragraph {
					// font-family: 'Helvetica Neue';
					font-style: normal;
					font-weight: 400;
					font-size: 12px;
					line-height: 130%;
					color: #333333;
					margin-bottom: 20px;
					text-align: left;
					max-width: 100%;
				}

				.diagrams {
					// margin-bottom: 45px;
					display: flex;
					justify-content: stretch;
					align-items: center;
					flex-direction: column;

					width: 100%;
				}

				.diagram {
					margin-bottom: 20px;
					display: flex;
					justify-content: stretch;
					flex-grow: 1;
					width: 100%;
				}

				.diagram-title {
					border-radius: 50px 0px 0px 50px;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					padding: 16.5px 33px 16.5px 28px;
					gap: 10px;
					background: #000000;
					width: 63px;
					height: 42px;
					// font-family: 'Helvetica Neue';
					font-style: normal;
					font-weight: 400;
					font-size: 12px;
					line-height: 14px;
					color: #FFFFFF;
				}

				.diagram-item {
					width: 100%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					padding: 0px;
					gap: 10px;
					isolation: isolate;

					// font-family: 'Helvetica Neue';
					font-style: normal;
					font-weight: 700;
					font-size: 16px;
					line-height: 100%;
					text-align: center;
					color: #FFFFFF;
					max-width: 100%;
				}

				.normal-diagram-item-1 {
					background-color: #303B76;
				}

				.normal-diagram-item-2 {
					background-color: #4D5387;
				}

				.normal-diagram-item-3 {
					background-color: #6D6E9A;
				}

				.normal-diagram-item-4 {
					background-color: #9393B5;
				}

				.stress-diagram-item-1 {
					background-color: #A2292E;
				}

				.stress-diagram-item-2 {
					background-color: #AC5444;
				}

				.stress-diagram-item-3 {
					background-color: #BC7865;
				}

				.stress-diagram-item-4 {
					background-color: #CD9E88;
				}

				.diagram-item:last-of-type {
					border-radius: 0px 50px 50px 0px;
				}

				${svgStyles}

				.page-number {
					margin-top: auto;
					position: absolute;
					bottom: 50px;
					right: 50px;
					text-align: right;
					font-size: 12px;
					color: #000;
				}

				.graph-title {
					font-style: normal;
					font-weight: 700;
					font-size: 20px;
					line-height: 130%;
					color: #333333;
					margin-bottom: 36px;
				}
			</style>
		</head>

		<body>
			<div class="container">
				<div class="center">
					<h1 class="user-name-title">${userName}</h1>
					<h2 class="title">How you scored and who you are</h2>
				</div>

				<img src="data:image;base64, ${logo}" alt="" class="logo" />

				<p class="paragraph">${normalParagraph}</p>

				<div class="center">
					<div class="diagrams">
						<div class="diagram">
							<div class="diagram-title">Under Normal Conditions</div>
							<div class="diagram-item normal-diagram-item-1">
								<span>${normalQuadrantsNames[0][0].toUpperCase()}</span><span>${
      sortedNormalAnswers[0][1]
    }</span>
							</div>
							<div class="diagram-item normal-diagram-item-2">
								<span>${normalQuadrantsNames[1][0].toUpperCase()}</span><span>${
      sortedNormalAnswers[1][1]
    }</span>
							</div>
							<div class="diagram-item normal-diagram-item-3">
								<span>${normalQuadrantsNames[2][0].toUpperCase()}</span><span>${
      sortedNormalAnswers[2][1]
    }</span>
							</div>
							<div class="diagram-item normal-diagram-item-4">
								<span>${normalQuadrantsNames[3][0].toUpperCase()}</span><span>${
      sortedNormalAnswers[3][1]
    }</span>
							</div>
						</div>

						<div class="diagram">
							<div class="diagram-title">Under Stressful Conditions</div>
							<div class="diagram-item stress-diagram-item-1">
								<span>${stressQuadrantsNames[0][0].toUpperCase()}</span><span>${
      sortedStressAnswers[0][1]
    }</span>
							</div>
							<div class="diagram-item stress-diagram-item-2">
								<span>${stressQuadrantsNames[1][0].toUpperCase()}</span><span>${
      sortedStressAnswers[1][1]
    }</span>
							</div>
							<div class="diagram-item stress-diagram-item-3">
								<span>${stressQuadrantsNames[2][0].toUpperCase()}</span><span>${
      sortedStressAnswers[2][1]
    }</span>
							</div>
							<div class="diagram-item stress-diagram-item-4">
								<span>${stressQuadrantsNames[3][0].toUpperCase()}</span><span>${
      sortedStressAnswers[3][1]
    }</span>
							</div>
						</div>
					</div>
				</div>

				<p class="paragraph">
					${stressParagraph}
				</p>

				<div class="center">
				<h1 class="graph-title">The ${quadrantsAbbreviation} Four-Quadrant Model</h1>
			</div>

				<div class="center">
					<div class="svg-container">
						<h2 class="svg-quadrant-title svg-quadrant-title_1">${quadrantsNames[0]}</h2>
						<h2 class="svg-quadrant-title svg-quadrant-title_2">${quadrantsNames[1]}</h2>
						<h2 class="svg-quadrant-title svg-quadrant-title_3">${quadrantsNames[2]}</h2>
						<h2 class="svg-quadrant-title svg-quadrant-title_4">${quadrantsNames[3]}</h2>

						<p class="svg-quadrant-subtitle svg-quadrant-subtitle_1">Tasks/Results</p>
						<p class="svg-quadrant-subtitle svg-quadrant-subtitle_2">People/Ideas</p>
						<p class="svg-quadrant-subtitle svg-quadrant-subtitle_3">Deliberate</p>
						<p class="svg-quadrant-subtitle svg-quadrant-subtitle_4">Fast</p>

						${svgString}
					</div>
				</div>

				<p class="stars-description">
					<span class="blue-star">Blue Star</span>
					- Under normal conditions
				</p>
				<p class="stars-description">
					<span class="red-star">Red Star</span>
					- Under stress conditions
				</p>

				<ul class="chart-description">
					<li class="chart-description-item">
						Axes: The horizontal axis represents speed – Deliberate or Fast. The vertical axis represents an orientation
						toward
						People/Ideas or Tasks/Results.
					</li>
					<li class="chart-description-item">
						Quadrants: Each quadrant represents a specific behavioral style or preference characterized by different traits,
						tendencies, and communication patterns.
					</li>
				</ul>
			</div>

			<div class="page-number">2</div>
		</body>
	</html>`;

  return document;
};
