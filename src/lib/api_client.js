class ApiClient {
  _send(status, req, res, next, data) {
    return res.status(status).json(data || []);
  }

  success(req, res, next, data) {
    this._send(200, req, res, next, data);
  }

  noContent(req, res, next) {
    this._send(204, req, res, next);
  }

  invalidRequest(req, res, next, msg) {
    if (!msg) {
      msg = 'Missing or invalid parameters.';
    }

    console.error('400 client', msg, req.body, req.params, req.query);

    const data = { message: msg };

    this._send(400, req, res, next, data);
  }

  notFound(req, res, next, msg) {
    if (!msg) {
      msg = 'Object does not exist.';
    }

    console.error('404 client', msg, req.body, req.params, req.query);
    
    const data = { message: msg };
    
    this._send(404, req, res, next, data);
  }

  notAuthorized(req, res, next, msg) {
    if (!msg) {
      msg = 'Not authorized to perform this action.';
    }

    console.error('401 client', msg, req.body, req.params, req.query);

    const data = { message: msg };

    this._send(401, req, res, next, data);
  }

  serverError(req, res, next, msg) {
    if (!msg) {
      msg = 'Server reported an error processing the operation.';
    }

    console.error('500 client', msg, req.body, req.params, req.query);
    
    const data = { message: msg };
    
    this._send(500, req, res, next, data);
  }
}

export default new ApiClient();
