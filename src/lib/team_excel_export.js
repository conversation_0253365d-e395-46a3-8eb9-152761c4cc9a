import excel from 'node-excel-export';
import styles from './excel_styles.js';

export default function getExcel(team) {
  let separators_1 = [{ col_h_1: '', col_h_2: '', col_h_3: '', col_h_4: '' }];
  let separators_2 = [
    { col_h_1: '', col_h_2: '', col_h_3: '', col_h_4: '' },
    { col_h_1: '', col_h_2: '', col_h_3: '', col_h_4: '' },
  ];

  let userMetrics = [];
  if (team.userMetrics) {
    team.userMetrics.map((metric) => {
      userMetrics.push({
        col_h_1: metric.name,
        col_h_2: metric.value,
        col_h_3: '',
        col_h_4: '',
      });
    });
  }

  let userInSeasonMetrics = [];
  if (team.userInSeasonMetrics) {
    team.userInSeasonMetrics.map((metric) => {
      userInSeasonMetrics.push({
        col_h_1: metric.name,
        col_h_2: metric.value,
        col_h_3: '',
        col_h_4: '',
      });
    });
  }

  let initiatives = [];
  if (team.initiatives) {
    team.initiatives.map((initiative) => {
      initiatives.push({
        col_h_1: `#${initiative.index}`,
        col_h_2: initiative.selected ? 'yes' : 'no',
      });
    });
  }

  let challenges = [];
  if (team.challenges) {
    team.challenges.map((challenge) => {
      challenges.push({
        col_h_1: challenge.name,
        col_h_2: challenge.selectedA ? 'yes' : 'no',
      });
      challenges.push({
        col_h_1: challenge.name,
        col_h_2: challenge.selectedB ? 'yes' : 'no',
      });
      challenges.push({
        col_h_1: challenge.name,
        col_h_2: challenge.selectedC ? 'yes' : 'no',
      });
    });
  }

  // Process decision data
  let decisions = [];
  if (team.decisions && team.decisions.pages && team.decisions.results) {
    const { pages, results } = team.decisions;
    const selectedValues = results.selected_values;

    if (selectedValues && selectedValues.pages) {
      pages.forEach((page, pageIndex) => {
        const pageData = selectedValues.pages[pageIndex];

        if (pageData && pageData.sliders) {
          // Process sliders for this page
          Object.entries(page.sliders).forEach(([sliderId, sliderConfig]) => {
            const selectedValue = pageData.sliders[sliderId];
            const selectedLabel = sliderConfig.labels && sliderConfig.labels[selectedValue];

            decisions.push({
              col_h_1: sliderConfig.fieldName || `${page.page_name || `Page ${pageIndex + 1}`} - ${sliderId}`,
              col_h_2: selectedLabel ? selectedLabel.text : 'No selection',
              col_h_3: selectedValue !== undefined ? `Option ${selectedValue + 1}` : 'N/A',
              col_h_4: selectedLabel ? `FTE: ${selectedLabel.fte || 0}, Investment: ${selectedLabel.investment || 0}` : '',
            });
          });
        }

        // Process incentives for this page
        if (pageData && pageData.selectedIncentives && page.incentives && page.incentives.list) {
          pageData.selectedIncentives.forEach((selectedIncentive, incentiveIndex) => {
            if (selectedIncentive) {
              decisions.push({
                col_h_1: `${page.page_name || `Page ${pageIndex + 1}`} - Incentive ${incentiveIndex + 1}`,
                col_h_2: selectedIncentive.label || 'Selected Incentive',
                col_h_3: `Incentive ${incentiveIndex + 1}`,
                col_h_4: `FTE: ${selectedIncentive.fte || 0}, Investment: ${selectedIncentive.investment || 0}`,
              });
            }
          });
        }
      });
    }
  }

  const specificationTeamData = {
    col_h_1: { displayName: '', headerStyle: styles.headerDark, width: 200 },
    col_h_2: { displayName: '', headerStyle: styles.headerDark, width: 200 },
    col_h_3: { displayName: '', headerStyle: styles.headerDark, width: 400 },
    col_h_4: { displayName: '', headerStyle: styles.headerDark, width: 200 },
  };

  const datasetTeamData = [
    // Team
    {
      col_h_1: 'Team Name'.toUpperCase(),
      col_h_2: 'Team Email'.toUpperCase(),
      col_h_3: '',
      col_h_4: '',
    },
    ...separators_1,
    { col_h_1: team.name, col_h_2: team.email, col_h_3: '', col_h_4: '' },
    ...separators_2,

    // Client
    {
      col_h_1: 'Client Name'.toUpperCase(),
      col_h_2: 'Challenge Tab Name'.toUpperCase(),
      col_h_3: 'Goal Tab Name'.toUpperCase(),
      col_h_4: 'Strategic Initiative Tab Name'.toUpperCase(),
    },
    ...separators_1,
    {
      col_h_1: team.clientName || team.client?.name || '',
      col_h_2: team.challengesTabName || team.client?.challengesTabName || '',
      col_h_3: team.goalsTabName || team.client?.goalsTabName || '',
      col_h_4: team.strategicTabName || team.client?.strategicInitiativesTabName || '',
    },
    ...separators_2,

    // UserMetrics
    {
      col_h_1: 'User Metric Name'.toUpperCase(),
      col_h_2: 'User Metric Value'.toUpperCase(),
      col_h_3: '',
      col_h_4: '',
    },
    ...separators_1,
    ...userMetrics,
    ...separators_2,

    // Initiatives
    {
      col_h_1: 'Initiative #'.toUpperCase(),
      col_h_2: 'Initiative Selected'.toUpperCase(),
      col_h_3: '',
      col_h_4: '',
    },
    ...separators_1,
    ...initiatives,
    ...separators_2,

    // Challenges
    {
      col_h_1: 'Challenge #'.toUpperCase(),
      col_h_2: 'Challenge Selected'.toUpperCase(),
      col_h_3: '',
      col_h_4: '',
    },
    ...separators_1,
    ...challenges,
    ...separators_2,

    // Decisions (only add if decision data exists)
    ...(decisions.length > 0 ? [
      {
        col_h_1: 'Decision Field Name'.toUpperCase(),
        col_h_2: 'Selected Option'.toUpperCase(),
        col_h_3: 'Option Order'.toUpperCase(),
        col_h_4: 'Values'.toUpperCase(),
      },
      ...separators_1,
      ...decisions,
      ...separators_2,
    ] : []),
  ];

  const report = excel.buildExport([
    {
      name: 'Team Data',
      specification: specificationTeamData,
      data: datasetTeamData,
    },
    {
      name: 'InSeason Metrics',
      specification: specificationTeamData,
      data: [
        {
          col_h_1: 'Name'.toUpperCase(),
          col_h_2: 'Value'.toUpperCase(),
          col_h_3: '',
          col_h_4: '',
        },
        ...separators_1,
        ...userInSeasonMetrics,
        ...separators_2,
      ],
    },
  ]);
  return report;
}
