import apiClient from './api_client.js';
import jwt from 'jsonwebtoken';
import config from '../../config.json' with { type: 'json' };

export default function jwtMiddleware(req, res, next) {
  if (req.headers.authorization) {
    let token = req.headers.authorization.split('bearer ')[1];
    console.log('checking token:', token);
    jwt.verify(token, config.jwtPassword, (err, decodedToken) => {
      if (decodedToken) {
        req.user = {
          id: decodedToken.id,
          clientId: decodedToken.clientId,
        };
        return next();
      } else {
        return apiClient.notAuthorized(req, res, next);
      }
    });
  } else {
    return apiClient.notAuthorized(req, res, next);
  }
}
