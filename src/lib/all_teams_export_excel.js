import excel from 'node-excel-export';
import styles from './excel_styles.js';

export default function getExcelAll(teams) {
  const specificationTeamData = {
    name: {
      displayName: 'Name',
      headerStyle: styles.headerDark,
      width: 120,
    },
    email: {
      displayName: 'Email',
      headerStyle: styles.headerDark,
      width: 220,
    },
  };

  const datasetTeamData = [{ name: '', email: '' }];

  const specificationClientData = {
    name: {
      displayName: 'Name',
      headerStyle: styles.headerDark,
      width: 120,
    },
    challengesTabName: {
      displayName: 'Challenge Tab Name',
      headerStyle: styles.headerDark,
      width: 120,
    },
    goalsTabName: {
      displayName: 'Goal Tab Name',
      headerStyle: styles.headerDark,
      width: 120,
    },
    strategicInitiativesTabName: {
      displayName: 'Strategic Initiative Tab Name',
      headerStyle: styles.headerDark,
      width: 120,
    },
  };

  const datasetClientData = [
    {
      name: '',
      challengesTabName: '',
      goalsTabName: '',
      strategicInitiativesTabName: '',
    },
  ];

  const specificationMetrics = {
    team: {
      displayName: 'Team',
      headerStyle: styles.headerDark,
      width: 120,
    },
    name: {
      displayName: 'Name',
      headerStyle: styles.headerDark,
      width: 120,
    },
    companyValue: {
      displayName: 'Your Company',
      headerStyle: styles.headerDark,
      width: 220,
    },
    industryValue: {
      displayName: 'Industry Value',
      headerStyle: styles.headerDark,
      width: 220,
    },
    plannedValue: {
      displayName: 'Planned Value',
      headerStyle: styles.headerDark,
      width: 220,
    },
  };

  const datasetMetrics = [
    {
      team: '',
      name: '',
      companyValue: '',
      industryValue: '',
      plannedValue: '',
    },
  ];

  const specificationUserInSeasonMetrics = {
    name: {
      displayName: 'Name',
      headerStyle: styles.headerDark,
      width: 120,
    },
    value: {
      displayName: 'Value',
      headerStyle: styles.headerDark,
      width: 220,
    },
  };

  const datasetUserInSeasonMetrics = [{ name: '', value: '' }];

  const specificationUserMetrics = {
    team: {
      displayName: 'Team',
      headerStyle: styles.headerDark,
      width: 120,
    },
    name: {
      displayName: 'Name',
      headerStyle: styles.headerDark,
      width: 120,
    },
    value: {
      displayName: 'Value',
      headerStyle: styles.headerDark,
      width: 220,
    },
  };

  const datasetUserMetrics = [{ team: '', name: '', value: '' }];

  const specificationInitiatives = {
    team: {
      displayName: 'Team',
      headerStyle: styles.headerDark,
      width: 120,
    },
    index: {
      displayName: '#',
      headerStyle: styles.headerDark,
      width: 50,
    },
    name: {
      displayName: 'Name',
      headerStyle: styles.headerDark,
      width: 400,
    },
    description: {
      displayName: 'Description',
      headerStyle: styles.headerDark,
      width: 400,
    },
    selected: {
      displayName: 'Selected',
      headerStyle: styles.headerDark,
      width: 50,
    },
  };

  const datasetInitiatives = [
    { team: '', index: '', name: '', description: '', selected: '' },
  ];

  const specificationChallenges = {
    team: {
      displayName: 'Team',
      headerStyle: styles.headerDark,
      width: 120,
    },
    description: {
      displayName: 'Challenge',
      headerStyle: styles.headerDark,
      width: 400,
    },
    index: {
      displayName: '#',
      headerStyle: styles.headerDark,
      width: 50,
    },
    option: {
      displayName: 'Options',
      headerStyle: styles.headerDark,
      width: 400,
    },
    selected: {
      displayName: 'selected',
      headerStyle: styles.headerDark,
      width: 50,
    },
  };

  let datasetChallenges = [
    { team: '', description: '', index: '', options: '', selected: '' },
  ];

  teams.map((team) => {
    datasetTeamData.push({
      name: team.name,
      email: team.email,
    });

    (team.clients || []).map((row) => {
      datasetMetrics.push({
        name: row.name,
        challengesTabName: row.challengesTabName,
        goalsTabName: row.goalsTabName,
        strategicInitiativesTabName: row.strategicInitiativesTabName,
      });
    });

    (team.revenueNumbers || []).map((number) => {
      datasetMetrics.push({
        team: team.name,
        name: number.name,
        companyValue: number.companyValue,
        industryValue: number.industryValue,
        plannedValue: number.plannedValue,
      });
    });

    (team.metrics || []).map((number) => {
      datasetMetrics.push({
        team: team.name,
        name: number.name,
        companyValue: number.companyValue,
        industryValue: number.industryValue,
      });
    });

    (team.userMetrics || []).map((number) => {
      datasetUserMetrics.push({
        team: team.name,
        name: number.name,
        value: number.value,
      });
    });

    (team.userInSeasonMetrics || []).map((number) => {
      datasetUserInSeasonMetrics.push({
        team: team.name,
        name: number.name,
        value: number.value,
      });
    });

    (team.initiatives || []).map((initiative) => {
      datasetInitiatives.push({
        team: team.name,
        index: `#${initiative.index}`,
        name: initiative.name,
        description: initiative.description,
        selected: initiative.selected ? 'yes' : 'no',
      });
    });

    let ch = [];

    (team.challenges || []).map((challenge) => {
      ch.push({
        team: team.name,
        description: challenge.description,
        option: challenge.optionA,
        index: 'A',
        selected: challenge.selectedA ? 'yes' : 'no',
      });
      ch.push({
        team: '',
        description: challenge.description,
        option: challenge.optionB,
        index: 'B',
        selected: challenge.selectedB ? 'yes' : 'no',
      });
      ch.push({
        team: '',
        description: challenge.description,
        option: challenge.optionC,
        index: 'C',
        selected: challenge.selectedC ? 'yes' : 'no',
      });
    });
    datasetChallenges = datasetChallenges.concat(ch);
  });

  const report = excel.buildExport([
    {
      name: 'Team data',
      specification: specificationTeamData,
      data: datasetTeamData,
    },
    {
      name: 'Metrics',
      specification: specificationMetrics,
      data: datasetMetrics,
    },
    {
      name: 'Team Metrics',
      specification: specificationUserMetrics,
      data: datasetUserMetrics,
    },
    {
      name: 'InSeason Metrics',
      specification: specificationUserInSeasonMetrics,
      data: datasetUserInSeasonMetrics,
    },
    {
      name: 'Strategic Initiatives',
      specification: specificationInitiatives,
      data: datasetInitiatives,
    },
    {
      name: 'Challenges',
      specification: specificationChallenges,
      data: datasetChallenges,
    },
  ]);
  return report;
}
