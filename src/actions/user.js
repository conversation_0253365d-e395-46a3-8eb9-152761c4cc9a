import axios from 'axios';

import <PERSON><PERSON><PERSON> from '../config';

// sign in user
export const SIGNIN_USER = 'SIGNIN_USER';
export const SIGNIN_USER_SUCCESS = 'SIGNIN_USER_SUCCESS';
export const SIGNIN_USER_FAILURE = 'SIGNIN_USER_FAILURE';

// sign out user
export const SIGNOUT_USER = 'SIGNOUT_USER';
export const RESET_TOKEN = 'RESET_TOKEN';

// token refresh
export const REFRESH_TOKEN = 'REFRESH_TOKEN';
export const TOKEN_REFRESH_SUCCESS = 'TOKEN_REFRESH_SUCCESS';
export const TOKEN_REFRESH_FAILURE = 'TOKEN_REFRESH_FAILURE';

export function signIn(formValues) {
  const request = axios.post(`${CONFIG.API_URL}/admin/signin`, formValues);
  return {
    type: SIGNIN_USER,
    payload: request,
  };
}

export function signInUserSuccess(user) {
  return {
    type: SIGNIN_USER_SUCCESS,
    payload: user,
  };
}

export function signOut() {
  sessionStorage.removeItem('jwtToken');
  sessionStorage.removeItem('client');

  return {
    type: SIGNOUT_USER,
    payload: {},
  };
}

export function refreshToken(token) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/admin/token/refresh`,
    data: {
      token,
    },
  });

  return {
    type: REFRESH_TOKEN,
    payload: request,
  };
}

export function refreshTokenSuccess(currentUser) {
  return {
    type: TOKEN_REFRESH_SUCCESS,
    payload: currentUser,
  };
}

export function refreshTokenFailure(error) {
  return {
    type: TOKEN_REFRESH_FAILURE,
    payload: error,
  };
}

export function resetToken() {
  return {
    type: RESET_TOKEN,
  };
}
