import axios from 'axios';

import <PERSON>NF<PERSON> from '../config';

export const ADD_INITIATIVE = 'ADD_INITIATIVE';
export const GET_INITIATIVE = 'GET_INITIATIVE';
export const UPDATE_INITIATIVE = 'UPDATE_INITIATIVE';
export const TOGGLE_INITIATIVE_DISABLED = 'TOGGLE_INITIATIVE_DISABLED';
export const LIST_INITIATIVES = 'LIST_INITIATIVES';
export const LIST_ALL_INITIATIVES = 'LIST_ALL_INITIATIVES';

export function getInitiative(id) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/initiative/id/${id}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_INITIATIVE,
    payload: request,
  };
}

export function addInitiative(formValues) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/admin/initiative`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...formValues,
    },
  });
  return {
    type: ADD_INITIATIVE,
    payload: request,
  };
}

export function updateInitiative(formValues) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/admin/initiative`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...formValues,
    },
  });
  return {
    type: UPDATE_INITIATIVE,
    payload: request,
  };
}

export function listInitiatives(query) {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/initiative/list`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    params: {
      ...query,
    },
  });
  return {
    type: LIST_INITIATIVES,
    payload: request,
  };
}

export function listAllInitiatives() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/initiative/all`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: LIST_ALL_INITIATIVES,
    payload: request,
  };
}

export function toggleDisabled(initiativeId) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/admin/initiative/disabled/${initiativeId}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: TOGGLE_INITIATIVE_DISABLED,
    payload: request,
  };
}
