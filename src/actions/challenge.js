import axios from 'axios';

import <PERSON>NF<PERSON> from '../config';

export const ADD_CHALLENGE = 'ADD_CHALLENGE';
export const GET_CHALLENGE = 'GET_CHALLENGE';
export const UPDATE_CHALLENGE = 'UPDATE_CHALLENGE';
export const TOGGLE_CHALLENGE_DISABLED = 'TOGGLE_CHALLENGE_DISABLED';
export const LIST_CHALLENGES = 'LIST_CHALLENGES';
export const LIST_ALL_CHALLENGES = 'LIST_ALL_CHALLENGES';

export function getChallenge(id) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/challenge/id/${id}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_CHALLENGE,
    payload: request,
  };
}

export function addChallenge(formValues) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/admin/challenge`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...formValues,
    },
  });
  return {
    type: ADD_CHALLENGE,
    payload: request,
  };
}

export function updateChallenge(formValues) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/admin/challenge`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...formValues,
    },
  });
  return {
    type: UPDATE_CHALLENGE,
    payload: request,
  };
}

export function listChallenges(query) {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/challenge/list`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    params: {
      ...query,
    },
  });
  return {
    type: LIST_CHALLENGES,
    payload: request,
  };
}

export function listAllChallenges() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/challenge/all`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: LIST_ALL_CHALLENGES,
    payload: request,
  };
}

export function toggleDisabled(challengeId) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/admin/challenge/disabled/${challengeId}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: TOGGLE_CHALLENGE_DISABLED,
    payload: request,
  };
}
