import axios from 'axios';

import CONFIG from '../config';

export const CREATE_LEADERBOARD = 'CREATE_LEADERBOARD';
export const UPDATE_LEADERBOARD = 'UPDATE_LEADERBOARD';
export const GET_LEADERBOARD = 'GET_LEADERBOARD';
export const TOGGLE_LEADERBOARDS_DISABLED = 'TOGGLE_LEADERBOARDS_DISABLED';
export const GET_LEADERBOARDS = 'GET_LEADERBOARDS';

export function createLeaderboard(payload) {
  const request = axios({
    method: 'POST',
    url: `${CONFIG.API_URL}/admin/leaderboard`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...payload,
    },
  });

  return {
    type: CREATE_LEADERBOARD,
    payload: request,
  };
}

export function updateLeaderboard(payload) {
  const request = axios({
    method: 'PUT',
    url: `${CONFIG.API_URL}/admin/leaderboard`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...payload,
    },
  });

  return {
    type: UPDATE_LEADERBOARD,
    payload: request,
  };
}

export function getLeaderboard(id) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/leaderboard/id/${id}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_LEADERBOARD,
    payload: request,
  };
}

export function getLeaderboards(query) {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/leaderboard/list`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    params: { ...query },
  });
  return {
    type: GET_LEADERBOARDS,
    payload: request,
  };
}

export function getAllLeaderboards() {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/leaderboard/all`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_LEADERBOARDS,
    payload: request,
  };
}

export function toggleDisabled(leaderboardId) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/admin/leaderboard/disabled/${leaderboardId}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: TOGGLE_LEADERBOARDS_DISABLED,
    payload: request,
  };
}
