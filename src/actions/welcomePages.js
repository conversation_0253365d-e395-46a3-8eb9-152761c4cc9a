import axios from 'axios';
import { nanoid } from 'nanoid';

import <PERSON>NF<PERSON> from '../config';

export const CREATE_WELCOME_PAGE = 'CREATE_WELCOME_PAGE';
export const UPDATE_WELCOME_PAGE = 'UPDATE_WELCOME_PAGE';
export const GET_WELCOME_PAGE = 'GET_WELCOME_PAGE';
export const TOGGLE_WELCOME_PAGE_DISABLED = 'TOGGLE_WELCOME_PAGE_DISABLED';
export const GET_WELCOME_PAGES = 'GET_WELCOME_PAGES';

export function createWelcomePage(payload) {
  const request = axios({
    method: 'POST',
    url: `${CONFIG.API_URL}/admin/welcome-page`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...payload,
    },
  });

  return {
    type: CREATE_WELCOME_PAGE,
    payload: request,
  };
}

export function updateWelcomePage(payload) {
  const request = axios({
    method: 'PUT',
    url: `${CONFIG.API_URL}/admin/welcome-page`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...payload,
    },
  });

  return {
    type: UPDATE_WELCOME_PAGE,
    payload: request,
  };
}

export function getWelcomePage(id) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/welcome-page/id/${id}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_WELCOME_PAGE,
    payload: request,
  };
}

export function getWelcomePages(query) {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/welcome-page/list`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    params: { ...query },
  });
  return {
    type: GET_WELCOME_PAGES,
    payload: request,
  };
}

export function getAllWelcomePages() {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/welcome-page/all`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_WELCOME_PAGES,
    payload: request,
  };
}

export function toggleDisabled(welcomePageId) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/admin/welcome-page/disabled/${welcomePageId}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: TOGGLE_WELCOME_PAGE_DISABLED,
    payload: request,
  };
}

/**
 * Upload image to AWS S3
 * @param file
 */
export function uploadImage(file) {
  const formData = new FormData();

  formData.append('file', file, nanoid(14));

  return axios.post(`${CONFIG.API_URL}/upload-file`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
}
