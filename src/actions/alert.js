import Guid from 'guid';

/* Action Types */
export const CREATE_NOTIFICATION = 'CREATE_NOTIFICATION';
export const REMOVE_NOTIFICATION = 'REMOVE_NOTIFICATION';

/* Actions */
export function createNotification(type, title, message, timeout = 3000) {
  const notification = {
    type,
    headline: title,
    message,
    timeout,
  };
  return {
    type: CREATE_NOTIFICATION,
    payload: { ...notification, id: Guid.raw() },
  };
}

export function removeNotification(notification) {
  return {
    type: REMOVE_NOTIFICATION,
    payload: notification,
  };
}
