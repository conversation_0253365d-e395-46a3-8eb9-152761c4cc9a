import axios from 'axios';

import <PERSON>NF<PERSON> from '../config';

export const CREATE_ORG_CHART = 'CREATE_ORG_CHART';
export const UPDATE_ORG_CHART = 'UPDATE_ORG_CHART';
export const GET_ORG_CHART = 'GET_ORG_CHART';
export const TOGGLE_ORG_CHARTS_DISABLED = 'TOGGLE_ORG_CHARTS_DISABLED';
export const GET_ORG_CHARTS = 'GET_ORG_CHARTS';

export function createOrgChart(payload) {
  const request = axios({
    method: 'POST',
    url: `${CONFIG.API_URL}/admin/org-chart`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...payload,
    },
  });

  return {
    type: CREATE_ORG_CHART,
    payload: request,
  };
}

export function updateOrgChart(payload) {
  const request = axios({
    method: 'PUT',
    url: `${CONFIG.API_URL}/admin/org-chart`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...payload,
    },
  });

  return {
    type: UPDATE_ORG_CHART,
    payload: request,
  };
}

export function getOrgChart(id) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/org-chart/id/${id}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_ORG_CHART,
    payload: request,
  };
}

export function getOrgCharts(query) {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/org-chart/list`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    params: { ...query },
  });
  return {
    type: GET_ORG_CHARTS,
    payload: request,
  };
}

export function getAllOrgCharts() {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/org-chart/all`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_ORG_CHARTS,
    payload: request,
  };
}

export function toggleDisabled(orgChartId) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/admin/org-chart/disabled/${orgChartId}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: TOGGLE_ORG_CHARTS_DISABLED,
    payload: request,
  };
}
