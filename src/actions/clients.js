import axios from 'axios';
import { nanoid } from 'nanoid';

import CONFIG from '../config';

export const CREATE_CLIENT = 'CREATE_CLIENT';
export const UPDATE_CLIENT = 'UPDATE_CLIENT';
export const GET_CLIENT = 'GET_CLIENT';
export const TOGGLE_CLIENTS_DISABLED = 'TOGGLE_CLIENTS_DISABLED';
export const GET_CLIENTS = 'GET_CLIENTS';
export const EXPORT_EXCEL = 'EXPORT_EXCEL';
export const EXPORT_EXCEL_ALL = 'EXPORT_EXCEL_ALL';

export function createClient(payload) {
  const request = axios({
    method: 'POST',
    url: `${CONFIG.API_URL}/admin/client`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...payload,
    },
  });

  return {
    type: CREATE_CLIENT,
    payload: request,
  };
}

export function updateClient(payload) {
  const request = axios({
    method: 'PUT',
    url: `${CONFIG.API_URL}/admin/client`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...payload,
    },
  });

  return {
    type: UPDATE_CLIENT,
    payload: request,
  };
}

export function getClient(id) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/client/id/${id}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_CLIENT,
    payload: request,
  };
}

export function getClients(query) {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/client/list`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    params: { ...query },
  });
  return {
    type: GET_CLIENTS,
    payload: request,
  };
}

export function getAllClients() {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/client/all`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_CLIENTS,
    payload: request,
  };
}

export function toggleDisabled(clientId) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/admin/client/disabled/${clientId}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: TOGGLE_CLIENTS_DISABLED,
    payload: request,
  };
}

/**
 * Upload image to AWS S3
 * @param file
 */
export function uploadImage(file) {
  const formData = new FormData();

  formData.append('file', file, nanoid(14));

  return axios.post(`${CONFIG.API_URL}/upload-file`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
}

export function exportExcel(clientId) {
  return fetch(`${CONFIG.API_URL}/admin/client/${clientId}/export`, {
    method: 'get',
    responseType: 'arraybuffer',
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  }).then((fetchResponse) => {
    if (fetchResponse.status === 204) return {};

    if (fetchResponse.ok) return fetchResponse.blob();

    return fetchResponse.json().then((err) => {
      throw err;
    });
  });
}
export function exportJson(clientId) {
  return fetch(`${CONFIG.API_URL}/admin/client/${clientId}/export-json`, {
    method: 'get',
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
      'Content-Type': 'application/json',
    },
  }).then((response) => {
    if (response.status === 204) return {};
    if (!response.ok) {
      return response.json().then((err) => {
        throw err;
      });
    }
    return response.json();
  });
}
export function exportExcelAll() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/client/export`,
    responseType: 'arraybuffer',
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: EXPORT_EXCEL_ALL,
    payload: request,
  };
}
