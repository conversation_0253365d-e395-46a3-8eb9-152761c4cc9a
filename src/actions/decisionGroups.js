import axios from 'axios';
import CONFIG from '../config';

export function getDecisionGroups() {
  return axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/decision-groups`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
}

export function createDecisionGroup(name) {
  return axios({
    method: 'POST',
    url: `${CONFIG.API_URL}/admin/decision-groups`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
      'Content-Type': 'application/json',
    },
    data: { name },
  });
}

export function deleteDecisionGroup(id) {
  return axios({
    method: 'DELETE',
    url: `${CONFIG.API_URL}/admin/decision-groups/id/${id}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
}
