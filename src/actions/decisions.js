import axios from 'axios';

import <PERSON><PERSON><PERSON> from '../config';

export const CREATE_DECISION = 'CREATE_DECISION';
export const UPDATE_DECISION = 'UPDATE_DECISION';
export const GET_DECISION = 'GET_DECISION';
export const TOGGLE_DECISION_DISABLED = 'TOGGLE_DECISION_DISABLED';
export const GET_DECISIONS = 'GET_DECISIONS';

export function createDecision(payload) {
  const request = axios({
    method: 'POST',
    url: `${CONFIG.API_URL}/admin/decision`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
      'Content-Type': 'application/json',
    },
    data: payload,
  });

  return {
    type: CREATE_DECISION,
    payload: request,
  };
}

export function updateDecision(payload) {
  const request = axios({
    method: 'PUT',
    url: `${CONFIG.API_URL}/admin/decision`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...payload,
    },
  });

  return {
    type: UPDATE_DECISION,
    payload: request,
  };
}

export function getDecision(id) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/decision/id/${id}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_DECISION,
    payload: request,
  };
}

export function getDecisions(query) {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/decision/list`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    params: { ...query },
  });
  return {
    type: GET_DECISIONS,
    payload: request,
  };
}
