import CONFIG from '../config';
import axios from 'axios';
import { nanoid } from 'nanoid';

export const GET_SELF_ASSESSMENT = 'GET_SELF_ASSESSMENT';
export const GET_SELF_ASSESSMENTS = 'GET_SELF_ASSESSMENTS';
export const CREATE_SELF_ASSESSMENT = 'CREATE_SELF_ASSESSMENT';
export const LIST_SELF_ASSESSMENTS = 'LIST_SELF_ASSESSMENTS';
export const LIST_ALL_SELF_ASSESSMENTS = 'LIST_ALL_SELF_ASSESSMENTS';
export const TOGGLE_SELF_ASSESSMENTS_DISABLED = 'TOGGLE_SELF_ASSESSMENTS_DISABLED';
export const UPDATE_SELF_ASSESSMENT = 'UPDATE_SELF_ASSESSMENT';
export const IMPORT = 'IMPORT';

export function createSelfAssessment(formValues) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/admin/self-assessments`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...formValues,
    },
  });
  return {
    type: CREATE_SELF_ASSESSMENT,
    payload: request,
  };
}

export function updateSelfAssessment(formValues) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/admin/self-assessments`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...formValues,
    },
  });
  return {
    type: UPDATE_SELF_ASSESSMENT,
    payload: request,
  };
}

export function getSelfAssessment(id) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/self-assessments/id/${id}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_SELF_ASSESSMENT,
    payload: request,
  };
}

export function getAllSelfAssessments(query) {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/self-assessments/all`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    params: { ...query },
  });
  return {
    type: GET_SELF_ASSESSMENTS,
    payload: request,
  };
}

export function listSelfAssessments(query) {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/self-assessments/list`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    params: {
      ...query,
    },
  });
  return {
    type: LIST_SELF_ASSESSMENTS,
    payload: request,
  };
}

export function listAllSelfAssessments(query) {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/self-assessments/all`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    params: {
      ...query,
    },
  });
  return {
    type: LIST_ALL_SELF_ASSESSMENTS,
    payload: request,
  };
}

export function toggleDisabled(selfAssessmentId) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/admin/self-assessments/disabled/${selfAssessmentId}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: TOGGLE_SELF_ASSESSMENTS_DISABLED,
    payload: request,
  };
}

export function uploadFile(file) {
  const formData = new FormData();

  formData.append('file', file, nanoid(14));

  return axios.post(`${CONFIG.API_URL}/upload-file`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
}
