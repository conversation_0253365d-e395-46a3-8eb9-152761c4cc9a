import axios from 'axios';

import { nanoid } from 'nanoid';

import CONFIG from '../config';

export const ADD_TEAM = 'ADD_TEAM';
export const GET_TEAM = 'GET_TEAM';
export const RESET_TEAM_HISTORY = 'RESET_TEAM_HISTORY';
export const UPDATE_TEAM = 'UPDATE_TEAM';
export const TOGGLE_TEAM_DISABLED = 'TOGGLE_TEAM_DISABLED';
export const LIST_TEAMS = 'LIST_TEAMS';
export const LIST_ALL_TEAMS = 'LIST_ALL_TEAMS';
export const EXPORT_EXCEL = 'EXPORT_EXCELL';
export const EXPORT_EXCEL_ALL = 'EXPORT_EXCELL_ALL';
export const IMPORT = 'IMPORT';
export const GET_SELF_ASSESSMENT_PDF = 'GET_SELF_ASSESSMENTPDFS';

export function getTeam(id) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/team/id/${id}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: GET_TEAM,
    payload: request,
  };
}

export function addTeam(formValues) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/admin/team`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...formValues,
    },
  });
  return {
    type: ADD_TEAM,
    payload: request,
  };
}

export function resetTeamHistory(teamId) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/admin/team/reset-history`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      teamId,
      source: 'admin',
    },
  });
  return {
    type: RESET_TEAM_HISTORY,
    payload: request,
  };
}

export function updateTeam(formValues) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/admin/team`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...formValues,
    },
  });
  return {
    type: UPDATE_TEAM,
    payload: request,
  };
}

export function listTeams(query) {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/team/list`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    params: {
      ...query,
    },
  });
  return {
    type: LIST_TEAMS,
    payload: request,
  };
}

export function listAllTeams() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/team/all`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: LIST_ALL_TEAMS,
    payload: request,
  };
}

export function toggleDisabled(teamId) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/admin/team/disabled/${teamId}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: TOGGLE_TEAM_DISABLED,
    payload: request,
  };
}

export function exportExcel(teamId) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/team/id/${teamId}/export`,
    responseType: 'arraybuffer',
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: EXPORT_EXCEL,
    payload: request,
  };
}

export function exportExcelAll() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/team/export`,
    responseType: 'arraybuffer',
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: EXPORT_EXCEL_ALL,
    payload: request,
  };
}

export function importExcel(file) {
  const formData = new FormData();

  formData.append('file', file, nanoid(14));

  const request = axios.post(`${CONFIG.API_URL}/admin/team/import`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });

  return {
    type: IMPORT,
    payload: request,
  };
}

export function exportSelfAssessmentPdf({ teamId, clientId }) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/admin/self-assessment-pdf?clientId=${clientId}&teamId=${teamId}`,
    responseType: 'arraybuffer',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/pdf',
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });

  return {
    type: GET_SELF_ASSESSMENT_PDF,
    payload: request,
  };
}

export function getTeamDecisionResults(teamId) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/decision-results`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    params: {
      userId: teamId,
    },
  });

  return {
    type: 'GET_TEAM_DECISION_RESULTS',
    payload: request,
  };
}
