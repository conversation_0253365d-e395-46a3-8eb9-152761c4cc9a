import { Readable } from 'stream';
import csv from 'csv-parser';
import readXlsxFile from 'read-excel-file/node';

export async function parseCsv(buffer) {
  const results = [];
  const stringValue = buffer.toString();
  const stream = Readable.from(stringValue);
  const separator = stringValue.includes(';') ? ';' : ',';

  return new Promise((resolve) => {
    return stream
      .pipe(
        csv({
          separator,
          mapHeaders: ({ header, index }) => {
            switch (header.toLowerCase()) {
              case 'team name':
                return 'name';
              case 'client':
                return 'client';
              case 'email':
                return 'email';
              case 'password':
                return 'password';
              default:
                return header;
            }
          },
        })
      )
      .on('data', (data) => results.push(data))
      .on('end', () => {
        resolve(results);
      });
  });
}

export async function parseXlsx(buffer) {
  // const stream = Readable.from(buffer);
  //
  // readXlsxFile(stream)
  // stream
  //   .pipe(readXlsxFile())
  //   .on('end', () => {
  //     console.log('END')
  //   })
  // return readXlsxFile(buffer).then(r => console.log(r)).catch(e => console.log(e));
}
