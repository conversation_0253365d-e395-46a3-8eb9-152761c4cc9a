# Services

This directory contains service layer modules that handle business logic and data processing for the Lumen simulation platform.

## Overview

Services provide reusable business logic that can be used across multiple controllers. They encapsulate complex operations, external integrations, and data transformations.

## Service Modules

### Data Processing
- **parser.js** - File parsing and data import services
  - CSV file parsing with flexible separators (comma and semicolon support)
  - Excel file parsing and processing
  - Header mapping and normalization
  - Team and client data import functionality
  - Stream-based processing for large files

## Key Features

### File Parsing Service
The parser service provides:
- **Multi-format Support**: Handles both CSV and Excel (.xlsx) files
- **Flexible CSV Parsing**: Automatically detects comma or semicolon separators
- **Header Mapping**: Converts various header formats to standardized field names
- **Stream Processing**: Efficient handling of large data files
- **Promise-based API**: Modern async/await compatible interface

### Data Transformation
- Standardizes team and client data from various input formats
- Maps different column headers to consistent internal field names
- Handles data validation during import process

## Dependencies

- **csv-parser** - CSV file parsing functionality
- **read-excel-file** - Excel file processing
- **Node.js Streams** - Efficient data processing

## Usage

Services are imported and used by controllers:
```javascript
import { parseCsv, parseExcel } from '../services/parser.js';
```

The service layer separates business logic from request handling, making the code more maintainable and testable.

## Architecture

Services follow these patterns:
- Export named functions for specific operations
- Use async/await for asynchronous operations
- Return consistent data structures
- Handle errors gracefully
- Provide streaming capabilities for large data sets