import express from 'express';
import DECISION_CONTROLLER from '../controllers/decision.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Apply authentication middleware
router.use(authenticateToken);

// Routes
router.get('/list', DECISION_CONTROLLER.list);
router.get('/all', DECISION_CONTROLLER.listAll);
router.post('/', DECISION_CONTROLLER.create);
router.get('/id/:id', DECISION_CONTROLLER.get);
router.put('/', DECISION_CONTROLLER.update);
router.put('/disabled/:id', DECISION_CONTROLLER.toggleDisabled);

export default router;
