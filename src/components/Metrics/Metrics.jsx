import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Label } from "recharts";
import stickybits from "stickybits";
import { getTeam } from "../../actions/user";

const Metrics = ({
  metrics: globalTeamMetrics = [],
  setGlobalMetrics = () => {},
  client,
  location,
}) => {
  const [metrics, setMetrics] = useState([]);
  const metricsRef = useRef(null);

  // Default colors if client is undefined
  const darkHighlightColor = client?.darkHighlightColor || "#004864";
  const lightHighlightColor = client?.lightHighlightColor || "#00A3E3";

  useEffect(() => {
    let stickyElement;

    if (!metricsRef.current || location?.pathname === "/") return;

    try {
      requestAnimationFrame(() => {
        stickyElement = stickybits(metricsRef.current, {
          stickyBitStickyOffset: 60,
        });
      });
    } catch (e) {
      console.error("Sticky initialization error:", e);
    }

    return () => {
      if (stickyElement && metricsRef.current) {
        stickyElement.cleanup();
      }
    };
  }, [location?.pathname]);

  // useEffect(() => {
  //   fetchTeamMetrics();
  // }, []);

  useEffect(() => {
    if (!globalTeamMetrics?.length) return;

    requestAnimationFrame(() => {
      setMetrics(globalTeamMetrics);
    });
  }, [globalTeamMetrics]);

  // const fetchTeamMetrics = async () => {
  //   try {
  //     const result = await getTeam().payload;

  //     setGlobalMetrics(result.data.globalTeamMetrics?.map((metric) => ({
  //       ...metric,
  //       value: 0,
  //     })));

  //   } catch (error) {
  //     console.error("Error fetching team metrics:", error);
  //   }
  // };

  if (!metrics.length) 
    return <></>;
    // return <p>No metrics data available.</p>;

  return (
    <div
      ref={metricsRef}
      className="grid grid-cols-1 sm:grid-cols-1 gap-4 p-4"
    >
      {metrics.map((metric, index) => {
        const currentValue =
          parseInt(metric.value || 0, 10) +
          parseInt(metric.defaultValue || 0, 10);
        const remainingValue = Math.max(0, 100 - currentValue);

        const data = [
          { name: "Current", value: currentValue, color: lightHighlightColor },
          {
            name: "Remaining",
            value: remainingValue,
            color: darkHighlightColor,
          },
        ];

        return (
          <div
            key={index}
            className="flex flex-col items-center p-4 rounded-lg bg-transparent border border-white/10"
          >
            <h2 className="text-sm font-medium text-gray-300 mb-2 text-center">
              {metric.name}
            </h2>
            <div className="relative w-32 h-32">
              <PieChart width={128} height={128}>
                <Pie
                  data={data}
                  cx={64}
                  cy={64}
                  innerRadius={40}
                  outerRadius={60}
                  startAngle={90}
                  endAngle={-270}
                  dataKey="value"
                  stroke="none"
                >
                  {data.map((entry, idx) => (
                    <Cell key={idx} fill={entry.color} />
                  ))}
                  <Label
                    value={`${currentValue}%`}
                    position="center"
                    className="text-base font-semibold fill-white"
                  />
                </Pie>
              </PieChart>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default Metrics;
