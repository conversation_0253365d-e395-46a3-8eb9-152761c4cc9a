import React from 'react';
import { FormControl } from 'react-bootstrap';

export default function ResultLimit({ onChange }) {
  return (
    <FormControl componentClass="select" placeholder="select" onChange={onChange}>
      <option value="20">Show 20 rows</option>
      <option value="50">Show 50 rows</option>
      <option value="100">Show 100 rows</option>
      <option value="150">Show 150 rows</option>
      <option value="200">Show 200 rows</option>
    </FormControl>
  );
}
