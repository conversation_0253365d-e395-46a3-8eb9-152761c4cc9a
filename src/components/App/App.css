body {
  background-image: url(../../img/background_new.jpg);
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.body-container {
  margin-top: 62px; /* Increased to prevent content from being hidden under navbar */
  padding: 15px;
}

.root-container {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 5px;
  box-shadow: 0 5px 15px 0 hsla(0, 0%, 0%, 0.2);
  padding: 0 15px 15px 15px;
}
.root-container .page-header {
  margin: 20px 0;
}
.root-container .panel,
.root-container .panel form .form-group:last-child {
  margin-bottom: 0;
}

nav.navbar.navbar-fixed-top {
  background-color: white;
  border-radius: 5px;
  -webkit-box-shadow: 0 5px 15px 0 hsla(0, 0%, 0%, 0.2);
  box-shadow: 0 5px 15px 0 hsla(0, 0%, 0%, 0.2);
  padding: 0;
  top: 0px;
  left: 0px;
  width: 90%;
  margin: auto;
  max-width: 1170px;
}

nav .navbar-btn .btn a {
  padding: 0;
}

.App {
  text-align: center;
}

.App-header {
  background-color: #222;
  height: 150px;
  padding: 20px;
  color: white;
}

.App-title {
  font-size: 1.5em;
}

.App-intro {
  font-size: large;
}

.navbar-brand {
  padding: 0 0 0 0 !important;
}
