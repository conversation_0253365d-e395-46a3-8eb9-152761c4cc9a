import React from 'react';

const renderField = ({ input, label, type, meta: { touched, error, invalid, warning } }) => {
  return (
    <div className={`form-group ${touched && invalid ? 'has-error' : ''}`}>
      <div>
        <input {...input} className="form-control" placeholder={label} type={type} />
        <div className="help-block">
          {touched && ((error && <span>{error}</span>) || (warning && <span>{warning}</span>))}
        </div>
      </div>
    </div>
  );
};

const renderFieldWithoutMessage = ({ input, label, type, meta: { touched, invalid } }) => (
  <div className={`form-group ${touched && invalid ? 'has-error' : ''}`}>
    <div>
      <input {...input} className="form-control" placeholder={label} type={type} />
    </div>
  </div>
);

export { renderField, renderFieldWithoutMessage };
