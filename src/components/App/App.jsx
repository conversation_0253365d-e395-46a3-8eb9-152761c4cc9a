import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

import NavbarContainer from '../../containers/NavbarContainer';
import AlertsContainer from '../../containers/AlertsContainer';
import './App.css';
import 'react-block-ui/style.css';

export default function App(props) {
  const { children, loadUserFromToken } = props;
  const location = useLocation();

  useEffect(() => {
    const token = sessionStorage.getItem('jwtToken');
    if (token) {
      loadUserFromToken();
    }
  }, []); // Empty dependency array means it only runs once on mount

  function renderNavbar() {
    const route = location.pathname;
    if (route !== '/') return <NavbarContainer />;
  }

  return (
    <div className="app">
      <AlertsContainer />
      {renderNavbar()}
      <div className="body-container"> {children} </div>
      <br />
      <div className="footer">
        <strong>Copyright &copy;&nbsp;2021 1st90</strong>
      </div>
    </div>
  );
}
