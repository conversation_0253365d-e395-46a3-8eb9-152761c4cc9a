import React, { useEffect, useCallback, useState } from "react";
import { useSelector } from "react-redux";
import to from "await-to-js";
import { Toaster, toast } from "sonner";
import Loader from "../common/Loader";
import { getTeam, SET_CLIENT, signOut } from "../../actions/user";
import { ROUTES } from "@/lib/constants";
import { useTheme } from "../ThemeProvider/ThemeProvider";
import { usePageBackground } from "../../contexts/PageBackgroundContext";
import bgImage from "../../img/background.jpg"; // Added import for background image

const currentYear = new Date().getFullYear();

export default function App({
  children,
  location,
  loadUserFromToken,
  dispatch,
  navigate,
}) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const client = useSelector((state) => state.user.client);
  const { theme } = useTheme();
  const { pageImage } = usePageBackground();

  const isUnauthorizedPage = useCallback(() => {
    const unauthorizedPages = [
      "/",
      "/sign-up",
      "/forgot-password",
      "/workshops",
    ];

    if (location.pathname.includes("recover-password")) return true;

    return unauthorizedPages.some((p) => p === location.pathname);
  }, [location.pathname]);

  const isKnownRoute = useCallback(() => {
    const knownRoutes = ROUTES;

    // Check if it's a known static route or a known dynamic route
    return (
      knownRoutes.includes(location.pathname) ||
      location.pathname.includes("recover-password")
    );
  }, [location.pathname]);

  useEffect(() => {
    const initializeApp = async () => {
      // Only load user data if it's a known route
      if (isKnownRoute()) {
        setIsLoading(true);
        await loadUserFromToken();
        setIsLoading(false);
      } else {
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  useEffect(() => {
    if (isKnownRoute() && !isUnauthorizedPage()) {
      proceedTeam();
    }
  }, [location.pathname]);

  const proceedTeam = async () => {
    setError("");
    const [err, res] = await to(getTeam().payload);

    if (err?.response?.status === 401) {
      sessionStorage.removeItem("jwtToken");
      localStorage.removeItem("clientId");

      navigate("/");

      dispatch(signOut());

      return;
    }

    if (err) {
      setError("Unable to load team data. Please try again later.");

      console.error("Something went wrong with getting the team.");

      toast.error("Something went wrong with getting the team.");

      return;
    }

    const { client } = res.data;
    dispatch({ type: SET_CLIENT, payload: client });
  };

  if (isLoading) return <Loader fullScreen />;

  // Use page image if available, otherwise fall back to client background or default
  const backgroundImage = pageImage || client?.backgroundImage || bgImage;

  return (
    // Removed [background:...] class and added inline style for background image and gradient
    <main
      className="min-h-screen flex flex-col justify-center items-center overflow-auto scrollbar-hide"
      style={{
        background: theme === 'dark'
          ? `radial-gradient(125% 125% at 50% 10%, hsla(0, 0%, 0%, 0.4) 40%, hsla(255, 84%, 57%, 0.4) 100%), url(${backgroundImage}) center/cover no-repeat fixed, black`
          : `radial-gradient(125% 125% at 50% 10%, hsla(0, 0%, 100%, 0.4) 40%, hsla(255, 84%, 90%, 0.4) 100%), url(${backgroundImage}) center/cover no-repeat fixed, white`
      }}
    >
      <Toaster richColors position="top-right" closeButton />
      {/* content */}
      <section className="mt-auto relative z-0">{children}</section>
      {/* footer */}
      <footer className={`text-center w-full font-light text-sm relative z-0 mt-auto py-4 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
        <strong>Copyright &copy;&nbsp;{new Date().getFullYear()} 1st90</strong>
      </footer>
    </main>
  );
}
