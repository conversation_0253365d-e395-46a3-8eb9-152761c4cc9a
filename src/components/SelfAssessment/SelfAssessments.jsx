import React, { useEffect, useState } from 'react';
import BlockUi from 'react-block-ui';
import { Button, Col, FormControl, FormGroup, InputGroup, PageHeader, Pagination, Row, Table } from 'react-bootstrap';
import { getAllClients } from '../../actions/clients';
import { listSelfAssessments, toggleDisabled } from '../../actions/selfAssessments';
import ResultLimit from '../App/ResultLimit';

function SelfAssessments(props) {
  const [state, setState] = useState({
    query: '',
    showDisabled: false,
    currentPage: 1,
    totalItems: 0,
    limit: 20,
    offset: 0,
    sort: 'name',
    clientId: -1,
    ascending: true,
    selfAssessments: [],
    clients: [],
    blocking: false,
  });

  useEffect(() => {
    list();
  }, [state.limit, state.showDisabled, state.offset, state.currentPage, state.clientId, state.ascending, state.sort]);

  const handleLimitChange = (e) => {
    setState((prevState) => ({
      ...prevState,
      limit: +e.target.value,
      offset: 0,
      currentPage: 1,
    }));
  };

  const handleClientChange = (e) => {
    setState((prevState) => ({
      ...prevState,
      currentPage: 1,
      offset: 0,
      clientId: e.target.value,
    }));
  };

  const updateQuery = (key, value) => {
    setState((prevState) => ({
      ...prevState,
      [key]: value,
      currentPage: 1,
      offset: 0,
    }));
  };

  const sort = (column) => {
    setState((prevState) => ({
      ...prevState,
      ascending: !prevState.ascending,
      sort: column,
      currentPage: 1,
      offset: 0,
    }));
  };

  const pageEvent = (e) => {
    const { limit } = state;
    setState((state) => ({
      ...state,
      currentPage: e,
      offset: (e - 1) * limit,
    }));
  };

  const list = () => {
    const { query, limit, offset, sort, ascending, showDisabled, clientId } = state;
    const { alert } = props;

    const q = {
      query,
      limit,
      offset,
      sort,
      ascending,
      showDisabled,
    };

    blockUi();

    listSelfAssessments(q)
      .payload.then((results) => {
        setState((state) => ({
          ...state,
          selfAssessments: [...results.data],
          totalItems: results.data.length ? Math.ceil(results.data[0].total / limit) : 0,
        }));
        unBlockUi();
      })
      .catch((error) => {
        console.log(error);
        alert('danger', 'Error', `Error listing self-assessments: ${error.toString()}`);
        unBlockUi();
      });

    getAllClients().payload.then((res) => {
      setState((state) => ({ ...state, clients: [...res.data] }));
    });
  };

  const viewSelfAssessment = (id) => {
    const { navigate } = props;
    navigate(`/self-assessments/${id}`);
  };

  const blockUi = () => {
    setState((state) => ({ ...state, blocking: true }));
  };

  const unBlockUi = () => {
    setState((state) => ({ ...state, blocking: false }));
  };

  const toggleSelfAssessmentDisable = (selfAssessment) => {
    blockUi();
    toggleDisabled(selfAssessment.id).payload.then(() => {
      list();
    });
  };

  const toggleShowDisabled = () => {
    setState((state) => ({
      ...state,
      showDisabled: !state.showDisabled,
    }));
  };

  const renderButtons = (team) => {
    let disable;
    if (team.disabled)
      disable = (
        <Button bsSize="xsmall" bsStyle="info" onClick={() => toggleSelfAssessmentDisable(team)}>
          Enable
        </Button>
      );
    else
      disable = (
        <Button bsSize="xsmall" onClick={() => toggleSelfAssessmentDisable(team)}>
          Disable
        </Button>
      );

    return (
      <td className="text-right">
        <Button bsSize="xsmall" onClick={() => viewSelfAssessment(team.id)} style={{ marginRight: '10px' }}>
          View
        </Button>
        {disable}
      </td>
    );
  };

  const renderSelfAssessmentsList = () => {
    const { selfAssessments } = state;
    return (
      <tbody>
        {selfAssessments.map((selfAssessment, i) => (
          // eslint-disable-next-line react/no-array-index-key
          <tr key={i}>
            <td>{selfAssessment.name}</td>
            <td className="text-right">{selfAssessment.created_at}</td>
            {renderButtons(selfAssessment)}
          </tr>
        ))}
      </tbody>
    );
  };

  const renderClientsSelect = () => {
    const { clients, clientId } = state;
    return (
      <FormGroup>
        <FormControl value={clientId} componentClass="select" placeholder="Select Client" onChange={handleClientChange}>
          <option value={0}>Select client...</option>
          {clients.map((client) => (
            <option key={client.id} value={client.id}>
              {client.name}
            </option>
          ))}
        </FormControl>
      </FormGroup>
    );
  };

  const { showDisabled, blocking, totalItems, currentPage } = state;

  const { navigate } = props;

  return (
    <div className="container root-container">
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Col md={10}>
          <PageHeader>Self Assessments</PageHeader>
        </Col>
      </div>
      <Row>
        <Col lg={2} md={2} xs={6}>
          <ResultLimit onChange={(e) => handleLimitChange(e)} />
        </Col>
        <Col lg={3} md={3} xs={6}>
          <InputGroup>
            <FormControl
              type="text"
              placeholder="Search"
              name="query"
              onChange={(e) => updateQuery('query', e.target.value)}
            />
            <InputGroup.Button>
              <Button type="submit" className="pull-right" name="list" onClick={() => list()}>
                Go
              </Button>
            </InputGroup.Button>
          </InputGroup>
        </Col>
        <Col lg={3} md={3} xs={12}>
          {renderClientsSelect()}
        </Col>
        <Col lg={4} md={4} xs={12}>
          <Button
            className="pull-right"
            bsStyle="primary"
            onClick={() => navigate('/self-assessments/add')}
            style={{ marginRight: '10px' }}
          >
            Add Self-assessment
          </Button>
          <Button
            className="pull-right"
            bsStyle={showDisabled ? 'success' : 'default'}
            onClick={() => toggleShowDisabled()}
            style={{ marginRight: '10px' }}
          >
            Show Disabled
          </Button>
        </Col>
      </Row>
      <br />
      <Row>
        <Col md={12}>
          <BlockUi tag="div" blocking={blocking}>
            <Table striped bordered hover>
              <thead className="thead-light">
                <tr>
                  <th role="button" onClick={() => sort('name')}>
                    Name
                    <i className="glyphicon glyphicon-sort" style={{ marginLeft: '5px' }} />
                  </th>
                  <th className="text-right" role="button" onClick={() => sort('created_at')}>
                    Created At
                    <i className="glyphicon glyphicon-sort" style={{ marginLeft: '5px' }} />
                  </th>
                  <th className="text-right">Actions</th>
                </tr>
              </thead>
              {renderSelfAssessmentsList()}
            </Table>
          </BlockUi>
        </Col>
      </Row>
      <Row>
        <Col md={12}>
          <Pagination
            className="pull-right"
            prev
            next
            first
            last
            boundaryLinks
            items={totalItems}
            maxButtons={5}
            activePage={currentPage}
            onSelect={pageEvent}
          />
        </Col>
      </Row>
    </div>
  );
}

export default SelfAssessments;
