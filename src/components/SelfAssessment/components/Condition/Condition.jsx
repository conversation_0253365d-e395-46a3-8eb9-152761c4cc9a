import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Check, Zap } from "lucide-react";

function Condition({
  imgSource,
  conditionType,
  setActiveQuiz,
  isActive,
  canBeLaunched,
  isCompleted,
  currentProgress,
}) {
  const casedConditionType =
    conditionType.charAt(0).toUpperCase() +
    conditionType.slice(1).toLowerCase();
  const title = `Under ${casedConditionType} Conditions`;
  const description = `Launch the assessment to answer questions on how you feel you respond under ${conditionType?.toLowerCase()} conditions.`;

  const launchHandler = () => {
    canBeLaunched && setActiveQuiz(conditionType);
  };

  const progressValue = currentProgress
    ? (parseInt(currentProgress.split("/")[0]) /
        parseInt(currentProgress.split("/")[1])) *
      100
    : 0;

  return (
    <div
      className={`backdrop-blur-xl bg-transparent rounded-xl border ${
        canBeLaunched || isCompleted ? "border-gray-800" : "border-gray-800/50"
      } p-6 shadow-lg transition-all duration-200 ${
        !canBeLaunched && !isCompleted ? "opacity-50" : ""
      }`}
    >
      <div className="flex flex-col items-center text-center space-y-4">
        <div className="w-16 h-16 flex items-center justify-center rounded-full bg-blue-200/10">
          <Zap className="w-8 h-8 text-amber-200" />
        </div>

        <h3 className="text-xl font-semibold text-white">{title}</h3>

        <p className="text-gray-400 mb-4">{description}</p>

        {!isActive && !isCompleted && (
          <Button
            disabled={!canBeLaunched}
            onClick={launchHandler}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            Launch Assessment
          </Button>
        )}

        {isActive && (
          <div className="w-full space-y-2">
            <div className="flex justify-between text-sm text-gray-400">
              <span>Assessment in Progress</span>
              <span>{currentProgress}</span>
            </div>
            <Progress value={progressValue} className="h-2" />
          </div>
        )}

        {isCompleted && (
          <Button className="w-full bg-green-600 hover:bg-green-700 text-white cursor-default">
            Assessment Completed
            <Check className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}

export default Condition;
