function getAvailableQuestions({ questions = [], conditionType }) {
  const availableQuestions = [];
  let quadrantNumber = 1;

  function getNextQuestion(key = getQuadrant({ conditionType, quadrantNumber }), questionIndex = 0) {
    if (quadrantNumber === 5) {
      return;
    }
    availableQuestions.push({ key, questionIndex });

    if (questions[key]?.[questionIndex + 1]) {
      return getNextQuestion(key, questionIndex + 1);
    }

    quadrantNumber++;

    return getNextQuestion(getQuadrant({ conditionType, quadrantNumber }), 0);
  }

  getNextQuestion();

  return availableQuestions;
}

export function getLocalStorageAnswersKey({ conditionType }) {
  return `${conditionType}_answers`;
}

export function getLocalStorageAvailableQuestionsKey({ conditionType }) {
  return `${conditionType}_AvailableQuestionsKeys`;
}

export function getLocalStorageCurrentQuestionPropertiesKey({ conditionType }) {
  return `${conditionType}CurrentQuestionProperties`;
}

export function getCurrentQuestionProperties({ conditionType }) {
  const storageCurrentQuestionProperties = localStorage.getItem(
    getLocalStorageCurrentQuestionPropertiesKey({ conditionType })
  );

  if (storageCurrentQuestionProperties) {
    return JSON.parse(storageCurrentQuestionProperties);
  }

  return { key: getQuadrant({ conditionType, quadrantNumber: 1 }), questionIndex: 0 };
}

export function getAvailableQuestionsKeys({ conditionType, questions }) {
  const storageAvailableQuestionsKeys = localStorage.getItem(getLocalStorageAvailableQuestionsKey({ conditionType }));

  if (storageAvailableQuestionsKeys) {
    return JSON.parse(storageAvailableQuestionsKeys);
  }

  const result = getAvailableQuestions({ questions, conditionType });

  return result;
}

export function getAnswers({ conditionType }) {
  const storageAnswers = localStorage.getItem(getLocalStorageAnswersKey({ conditionType }));

  if (storageAnswers) {
    return JSON.parse(storageAnswers);
  }

  return {};
}

export function getQuadrant({ quadrantNumber, conditionType }) {
  return `${conditionType}_quadrant_${quadrantNumber}_questions`;
}
