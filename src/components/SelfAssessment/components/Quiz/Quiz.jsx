import React, { useEffect, useState } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  getAnswers,
  getAvailableQuestionsKeys,
  getCurrentQuestionProperties,
  getLocalStorageAnswersKey,
  getLocalStorageAvailableQuestionsKey,
  getLocalStorageCurrentQuestionPropertiesKey,
} from "./helpers";

export function Quiz({
  quizCompletedHandler,
  setCurrentQuestion,
  quizQuestions: questions,
  conditionType,
  setRawAnswers,
}) {
  const [answers, setAnswers] = useState(getAnswers({ conditionType }));
  const [currentQ, setCurrentQ] = useState(
    getCurrentQuestionProperties({ conditionType })
  );
  const [selectedValue, setSelectedValue] = useState(null);
  const [availableQuestionsKeys, setAvailableQuestionsKeys] = useState(
    getAvailableQuestionsKeys({ conditionType, questions })
  );
  const [isTransitioning, setIsTransitioning] = useState(false);

  const currentQuestionNumber =
    Object.values(questions).flat()?.length -
    availableQuestionsKeys?.length +
    1;

  function getRandomQuestion() {
    const questions = availableQuestionsKeys.filter(
      (el) =>
        el.key !== currentQ?.key || el.questionIndex !== currentQ?.questionIndex
    );
    const randomIndex = Math.floor(Math.random() * questions.length);
    const availablePair = questions[randomIndex];
    return availablePair;
  }

  function onChangeHandler(value) {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setSelectedValue(value);

    const key = currentQ?.key;
    const num = Number(value);

    setAnswers((prevAnswers) => {
      const newAnswers = {
        ...prevAnswers,
        [key]: prevAnswers?.[key] ? prevAnswers?.[key] + num : num,
      };
      localStorage.setItem(
        getLocalStorageAnswersKey({ conditionType }),
        JSON.stringify(newAnswers)
      );
      return newAnswers;
    });

    setRawAnswers((rawAnswers) => ({
      ...rawAnswers,
      [`${currentQ?.key}_${currentQuestionNumber}`]: {
        presentedIndex: `Question #${currentQuestionNumber}`,
        question: currentQ,
        description: questions[currentQ?.key]?.[currentQ?.questionIndex],
        answerGiven: num,
      },
    }));

    // Wait for 500ms before moving to next question
    setTimeout(() => {
      setSelectedValue(null);
      setNextQuestion();
      setIsTransitioning(false);
    }, 500);
  }

  function setNextQuestion() {
    const randomQuestion = getRandomQuestion();

    setAvailableQuestionsKeys((prevKeys) => {
      const newKeys = prevKeys.filter((el) => {
        if (el.key !== currentQ?.key) return true;
        if (el.questionIndex !== currentQ?.questionIndex) return true;
        return false;
      });

      localStorage.setItem(
        getLocalStorageAvailableQuestionsKey({ conditionType }),
        JSON.stringify(newKeys)
      );
      return newKeys;
    });

    setCurrentQ(randomQuestion);

    if (randomQuestion) {
      localStorage.setItem(
        getLocalStorageCurrentQuestionPropertiesKey({ conditionType }),
        JSON.stringify(randomQuestion)
      );
    }
  }

  useEffect(() => {
    const handleKeyPress = (event) => {
      const key = event.key;
      if (isTransitioning) return;
      if (['1', '2', '3', '4', '5'].includes(key)) {
        onChangeHandler(key);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isTransitioning]);

  useEffect(() => {
    setCurrentQuestion(currentQuestionNumber);
    if (availableQuestionsKeys?.length === 0) {
      quizCompletedHandler(answers);
    }
  }, [currentQ]);

  const totalQuestionIndex = `Question #${currentQuestionNumber}`;
  const description = questions[currentQ?.key]?.[currentQ?.questionIndex];

  const options = [
    { value: "1", label: "Never" },
    { value: "2", label: "Seldom" },
    { value: "3", label: "Sometimes" },
    { value: "4", label: "Often" },
    { value: "5", label: "Almost Always" },
  ];

  return (
    <div className="backdrop-blur-xl bg-white/5 rounded-xl border border-gray-800 p-6 shadow-lg">
      <div className="mb-6 space-y-4">
        <p className="text-gray-400">
          Complete the assessment by answering each question
        </p>
        <h4 className="text-xl font-semibold text-white">
          {totalQuestionIndex}
        </h4>
        <h5 className="text-gray-300">{description}</h5>
      </div>

      <RadioGroup
        className="flex justify-between gap-2"
        value={selectedValue}
        onValueChange={onChangeHandler}
        disabled={isTransitioning}
      >
        {options.map((option) => (
          <div key={option.value} className="flex flex-col items-center gap-2">
            <RadioGroupItem
              value={option.value}
              id={`option-${option.value}`}
              className="text-white border-white data-[state=checked]:bg-blue-600 data-[state=checked]:text-blue-600 data-[state=checked]:border-blue-600"
            />
            <Label
              htmlFor={`option-${option.value}`}
              className="text-sm text-white"
            >
              {option.label}
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
}
