import { Field } from 'formik';
import React from 'react';
import { Col, Row } from 'react-bootstrap';
import { CustomUploadButton } from '../../../../CustomUploadButton/CustomUploadButton';

const page2 = new Array(4).fill(null);
const page3 = new Array(4).fill(null);

function DetailPages() {
  function getDetails(array, pageKey) {
    return array.map((_, index) => {
      return (
        <div key={`${pageKey}_${index}`} style={{ display: 'flex', alignItems: 'center' }}>
          <span
            style={{
              marginRight: '8px',
              fontFamily: 'sans-serif',
              fontWeight: '400',
              fontSize: '16px',
              lineHeight: '18.11px',
            }}
          >
            Upload Quadrant #{index + 1}
          </span>
          <Field
            accept=".pdf"
            type="file"
            name={`page_${pageKey}_quad_${index + 1}_pdf`}
            label="Upload PDF Page"
            fileName="PDF Page"
            component={CustomUploadButton}
          />
        </div>
      );
    });
  }

  return (
    <div>
      <Row>
        <Col md={12}>
          <h1 style={{ fontFamily: 'sans-serif', fontWeight: '700', fontSize: '20px', lineHeight: '24.42px' }}>
            Detail Page #1
          </h1>
          <div style={{ display: 'flex' }}>
            <Field
              type="file"
              name="cover_image"
              label="Upload Cover Image"
              fileName="Cover Image"
              component={CustomUploadButton}
              accept="image/*"
            />
          </div>
        </Col>
      </Row>

      <Col md={12}>
        <Row style={{ marginTop: '30px', marginBottom: '30px', borderBottom: '2px solid white' }} />
      </Col>

      <Row>
        <Col md={6}>
          <h1 style={{ fontFamily: 'sans-serif', fontWeight: '700', fontSize: '20px', lineHeight: '24.42px' }}>
            Detail Page #2
          </h1>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>{getDetails(page2, 2)}</div>
        </Col>

        <Col md={6}>
          <h1 style={{ fontFamily: 'sans-serif', fontWeight: '700', fontSize: '20px', lineHeight: '24.42px' }}>
            Detail Page #3
          </h1>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>{getDetails(page3, 3)}</div>
        </Col>
      </Row>
    </div>
  );
}

export default DetailPages;
