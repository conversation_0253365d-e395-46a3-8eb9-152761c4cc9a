import React from 'react';
import QuadrantPair from './QuadrantPair';
import { FieldArray } from 'formik';

const QUADRANTS_COUNT = 4;

function generatePairs(current = 1, result = []) {
  if (current > QUADRANTS_COUNT) return result;
  for (let i = 1; i <= QUADRANTS_COUNT; i++) {
    if (i !== current) {
      result.push([current, i]);
    }
  }
  return generatePairs(current + 1, result);
}

const numberPairs = generatePairs();

function ParagraphsList(props) {
  const { fieldName, name, condition } = props;

  return (
    <div style={{ width: '50%' }}>
      <h1 style={{ fontFamily: 'sans-serif', fontWeight: '700', fontSize: '20px', lineHeight: '24.42px', margin: '0' }}>
        {name} Paragraphs
      </h1>
      <div>
        <FieldArray
          name={fieldName}
          render={() =>
            numberPairs.map(([top1Quadrant, top2Quadrant], index) => (
              <QuadrantPair
                fieldName={`${fieldName}[${index}]`}
                condition={condition}
                key={`${top1Quadrant}${top2Quadrant}`}
                top1Quadrant={top1Quadrant}
                top2Quadrant={top2Quadrant}
              />
            ))
          }
        />
      </div>
    </div>
  );
}

export default ParagraphsList;
