import { Field } from 'formik';
import React from 'react';
function QuadrantPair(props) {
  const { top1Quadrant, top2Quadrant, fieldName } = props;

  return (
    <div>
      <h2 style={{ fontFamily: 'sans-serif', fontWeight: '700', fontSize: '16px', lineHeight: '19.54px' }}>
        Primary Quadrants #{top1Quadrant} & #{top2Quadrant}:
      </h2>
      <Field
        className="form-control"
        style={{
          fontFamily: 'sans-serif',
          fontWeight: '400',
          fontSize: '16px',
          lineHeight: '18.11px',
          resize: 'vertical',
        }}
        as="textarea"
        name={fieldName}
      />
    </div>
  );
}

export default QuadrantPair;
