import classNames from 'classnames';
import React, { useMemo } from 'react';
import { Row } from 'react-bootstrap';
import styles from './quadranttabs.module.scss';

const tabs = [
  {
    id: 1,
    name: 'Quadrant #1',
  },
  {
    id: 2,
    name: 'Quadrant #2',
  },
  {
    id: 3,
    name: 'Quadrant #3',
  },
  {
    id: 4,
    name: 'Quadrant #4',
  },
];

function QuadrantTabs(props) {
  const { setActiveQuadrant, quadrantKey } = props;

  function handleTab(id) {
    setActiveQuadrant(id);
  }

  function getTabClassName(tab) {
    return classNames(tab.id === quadrantKey ? styles.activeQuadTab : styles.quadTab);
  }

  const tabList = useMemo(
    () =>
      tabs.map((tab) => {
        return (
          <button
            key={tab.id}
            className={getTabClassName(tab)}
            onClick={() => {
              handleTab(tab.id);
            }}
          >
            {tab.name}
          </button>
        );
      }),
    [quadrantKey]
  );

  return (
    <Row style={{ borderBottom: '2px solid white', marginTop: '30px', display: 'flex' }}>
      <div className={styles.quadTabs}>{tabList}</div>
    </Row>
  );
}

export default QuadrantTabs;
