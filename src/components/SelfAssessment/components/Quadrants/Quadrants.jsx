import React, { useState } from 'react';
import { Tab } from 'react-bootstrap';
import Quadrant from './Quadrant/Quadrant';
import QuadrantTabs from './QuadrantTabs/QuadrantTabs';
import styles from './quadrants.module.scss';

function Quadrants() {
  const [quadrantKey, setQuadrantKey] = useState(1);

  return (
    <div className={styles.fontStyles}>
      <Tab.Container activeKey={quadrantKey}>
        <>
          <QuadrantTabs quadrantKey={quadrantKey} setActiveQuadrant={setQuadrantKey} />

          <Tab.Content animation>
            <Tab.Pane eventKey={1}>
              <Quadrant quadrantKey={1} />
            </Tab.Pane>
            <Tab.Pane eventKey={2}>
              <Quadrant quadrantKey={2} />
            </Tab.Pane>
            <Tab.Pane eventKey={3}>
              <Quadrant quadrantKey={3} />
            </Tab.Pane>
            <Tab.Pane eventKey={4}>
              <Quadrant quadrantKey={4} />
            </Tab.Pane>
          </Tab.Content>
        </>
      </Tab.Container>
    </div>
  );
}

export default Quadrants;
