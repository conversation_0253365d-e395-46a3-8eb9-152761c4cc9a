import { Field, FieldArray, useFormikContext } from 'formik';
import React from 'react';
import { Col, Row } from 'react-bootstrap';
import Question from './Question/Question';
import { CustomFormControl } from '../../../CustomFormControl';

const borderStyles = {
  borderColor: '#c3c3c3',
  borderStyle: 'solid',
  borderWidth: 0,
};

function Quadrant(props) {
  const { quadrantKey } = props;
  const { values } = useFormikContext();

  const currentQuadrantName = `quadrant_${quadrantKey}_name`;
  const normalQuestionsLength = values[getQuadrantQuestionsName({ condition: 'normal' })].length;
  const stressQuestionsLength = values[getQuadrantQuestionsName({ condition: 'stress' })].length;

  function getQuadrantQuestionsName({ condition }) {
    return `${condition}_quadrant_${quadrantKey}_questions`;
  }

  const questionList = ({ remove, condition }) =>
    values[getQuadrantQuestionsName({ condition })]?.map((_, index) => (
      <Question
        key={index}
        currentQuadrantQuestionsName={getQuadrantQuestionsName({ condition })}
        index={index}
        remove={remove}
      />
    ));

  return (
    <div>
      <Row style={{ marginTop: '15px', marginBottom: '15px' }}>
        <Col md={4}>
          <div style={{ marginBottom: '5px' }}>{`Quadrant #${quadrantKey} Name`}</div>
          <Field type="text" name={currentQuadrantName} component={CustomFormControl} />
        </Col>
      </Row>
      <Row>
        <Col md={6} style={{ height: '100%' }}>
          <h3>Normal Conditions</h3>
          <FieldArray
            name={getQuadrantQuestionsName({ condition: 'normal' })}
            render={({ push, remove }) => (
              <>
                <Row
                  style={{
                    marginTop: '30px',
                    gap: '10px',
                    paddingRight: '30px',
                    ...borderStyles,
                    borderRightWidth: normalQuestionsLength >= stressQuestionsLength ? '1px' : 0,
                  }}
                >
                  <Col md={12}>
                    <div>{questionList({ remove, condition: 'normal' })}</div>
                  </Col>
                </Row>
                <Row>
                  <Col md={2}>
                    <button type="button" className="btn btn-info" onClick={() => push('')}>
                      Add Question
                    </button>
                  </Col>
                </Row>
              </>
            )}
          />
        </Col>
        <Col md={6}>
          <h3 style={{ paddingLeft: '30px' }}>Stress Conditions</h3>
          <FieldArray
            name={getQuadrantQuestionsName({ condition: 'stress' })}
            render={({ push, remove }) => (
              <>
                <Row
                  style={{
                    marginTop: '30px',
                    gap: '10px',
                    paddingLeft: '30px',
                    ...borderStyles,
                    borderLeftWidth: normalQuestionsLength < stressQuestionsLength ? '1px' : 0,
                  }}
                >
                  <Col md={12}>
                    <div>{questionList({ remove, condition: 'stress' })}</div>
                  </Col>
                </Row>
                <Row style={{ paddingLeft: '30px' }}>
                  <Col md={2}>
                    <button type="button" className="btn btn-info" onClick={() => push('')}>
                      Add Question
                    </button>
                  </Col>
                </Row>
              </>
            )}
          />
        </Col>
      </Row>
    </div>
  );
}

export default Quadrant;
