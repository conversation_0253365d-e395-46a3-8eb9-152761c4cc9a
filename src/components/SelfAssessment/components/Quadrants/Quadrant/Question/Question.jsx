import React from 'react';

import { Field } from 'formik';
import { CustomFormControl } from '../../../../CustomFormControl';
import TrashIcon from '../../../../img/trash.svg';

function Question(props) {
  const { index, currentQuadrantQuestionsName, remove } = props;

  return (
    <div style={{ marginBottom: '20px' }} key={index}>
      <div style={{ marginBottom: '5px' }}>Question {index + 1}</div>
      <div style={{ display: 'flex', gap: '20px' }}>
        <Field
          type="text"
          name={`${currentQuadrantQuestionsName}[${index}]`}
          style={{ color: 'black' }}
          component={CustomFormControl}
        />
        <button
          type="button"
          className="btn btn-danger"
          style={{ display: 'flex', gap: '5px', height: '34px', alignItems: 'center' }}
          onClick={() => {
            remove(index);
          }}
        >
          <img src={TrashIcon} alt="" />
          <p style={{ margin: '0' }}>Remove</p>
        </button>
      </div>
    </div>
  );
}

export default Question;
