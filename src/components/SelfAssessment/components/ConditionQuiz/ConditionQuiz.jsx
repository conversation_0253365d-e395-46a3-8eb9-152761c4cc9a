import React, { useMemo, useState, useEffect } from "react";
import Condition from "../Condition/Condition";
import { Quiz } from "../Quiz/Quiz";

export const progressState = {
  IN_PROGRESS: 1,
  COMPLETED: 2,
};

function ConditionQuiz({
  imgSource,
  conditionType,
  isActive,
  quizCompletedHandler,
  setActiveQuiz,
  canBeLaunched,
  isCompleted,
  quizQuestions,
  setRawAnswers,
}) {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [currentProgress, setCurrentProgress] = useState(null);

  const questionCount = useMemo(
    () =>
      Object.values(quizQuestions).reduce(
        (acc, current) => acc + current.length,
        0
      ),
    [quizQuestions]
  );

  useEffect(() => {
    setCurrentProgress(`${currentQuestion}/${questionCount}`);
  }, [currentQuestion, quizQuestions]);

  return (
    <div className="flex flex-col gap-6">
      <Condition
        setActiveQuiz={setActiveQuiz}
        canBeLaunched={canBeLaunched}
        imgSource={imgSource}
        conditionType={conditionType}
        isActive={isActive}
        isCompleted={isCompleted}
        currentProgress={currentProgress}
      />
      {isActive && (
        <Quiz
          conditionType={conditionType}
          quizQuestions={quizQuestions}
          quizCompletedHandler={quizCompletedHandler}
          setCurrentQuestion={setCurrentQuestion}
          setRawAnswers={setRawAnswers}
        />
      )}
    </div>
  );
}

export default ConditionQuiz;
