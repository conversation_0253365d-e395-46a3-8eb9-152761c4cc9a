import React from "react";
import to from "await-to-js";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Download, CheckCircle2 } from "lucide-react";
import { exportSelfAssessmentPdf } from "../../../../actions/user";

function AssessmentCompleted({
  normalQuizResult,
  stressQuizResult,
  teamId,
  setLoading,
}) {
  const downloadReportHandler = async () => {
    setLoading(true);

    const [err, res] = await to(exportSelfAssessmentPdf({ teamId }).payload);

    setLoading(false);

    if (err) {
      const textDecoder = new TextDecoder("utf-8");
      const jsonString = textDecoder.decode(err.response.data);
      const errorObject = JSON.parse(jsonString);
      return toast.error(errorObject.message);
    }

    const blob = new Blob([res.data], { type: "application/pdf" });
    const url = window.URL.createObjectURL(blob);

    const anchor = document.createElement("a");
    anchor.href = url;
    anchor.download = "Your Self Assessment.pdf";
    anchor.click();

    toast.success("Client was successfully exported");
  };

  return (
    <div className="flex justify-center py-8">
      <div className="max-w-xl w-full">
        <div className="flex flex-col items-center text-center space-y-6">
          <div className="flex flex-col items-center gap-2">
            <div className="w-16 h-16 flex items-center justify-center">
              <CheckCircle2 className="w-16 h-16 text-green-500" />
            </div>
            <h3 className="text-3xl font-semibold text-white">
              Assessment Completed
            </h3>
          </div>

          <div>
            <p className="font-medium text-lg text-white/90">
              Download your report below.
            </p>
            <p className="max-w-[40ch] font-light text-sm mt-px tracking-wide leading-snug text-gray-400">
              Click on the Download Report button to access your report now.
              Your report was also sent to your email.
            </p>
          </div>

          <Button
            onClick={downloadReportHandler}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8"
          >
            <Download className="w-4 h-4 mr-2" />
            Download Report
          </Button>
        </div>
      </div>
    </div>
  );
}

export default AssessmentCompleted;
