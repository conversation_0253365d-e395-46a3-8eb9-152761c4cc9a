import to from 'await-to-js';
import classNames from 'classnames';
import { Field, FormikProvider, useFormik } from 'formik';
import React, { useEffect, useState } from 'react';
import BlockUi from 'react-block-ui';
import { Col, Nav, PageHeader, Row, Tab } from 'react-bootstrap';
import { getSelfAssessment, updateSelfAssessment, createSelfAssessment } from '../../actions/selfAssessments';
import { CustomFormControl } from './CustomFormControl';
import Quadrants from './components/Quadrants/Quadrants';
import Reports from './components/Reports/Reports';
import styles from './selfassessment.module.scss';
import * as Yup from 'yup';

const initialValues = {
  name: '',
  quadrant_1_name: '',
  quadrant_2_name: '',
  quadrant_3_name: '',
  quadrant_4_name: '',
  normal_quadrant_1_questions: new Array(5).fill(''),
  normal_quadrant_2_questions: new Array(5).fill(''),
  normal_quadrant_3_questions: new Array(5).fill(''),
  normal_quadrant_4_questions: new Array(5).fill(''),
  stress_quadrant_1_questions: new Array(5).fill(''),
  stress_quadrant_2_questions: new Array(5).fill(''),
  stress_quadrant_3_questions: new Array(5).fill(''),
  stress_quadrant_4_questions: new Array(5).fill(''),
  cover_image: '',
  page_2_quad_1_pdf: '',
  page_2_quad_2_pdf: '',
  page_2_quad_3_pdf: '',
  page_2_quad_4_pdf: '',
  page_3_quad_1_pdf: '',
  page_3_quad_2_pdf: '',
  page_3_quad_3_pdf: '',
  page_3_quad_4_pdf: '',
  normal_paragraphs: new Array(12).fill(''),
  stress_paragraphs: new Array(12).fill(''),
};

const validationSchema = Yup.object().shape({
  name: Yup.string().min(1, 'Too Short!').required('Required'),
  quadrant_1_name: Yup.string().min(1, 'Too Short!').required('Required'),
  quadrant_2_name: Yup.string().min(1, 'Too Short!').required('Required'),
  quadrant_3_name: Yup.string().min(1, 'Too Short!').required('Required'),
  quadrant_4_name: Yup.string().min(1, 'Too Short!').required('Required'),

  normal_quadrant_1_questions: Yup.array()
    .of(Yup.string().min(1, 'Too Short!').required('Required'))
    .required('Required')
    .min(1, 'Must have at least 1 question'),

  normal_quadrant_2_questions: Yup.array()
    .of(Yup.string().min(1, 'Too Short!').required('Required'))
    .required('Required')
    .min(1, 'Must have at least 1 question'),

  normal_quadrant_3_questions: Yup.array()
    .of(Yup.string().min(1, 'Too Short!').required('Required'))
    .required('Required')
    .min(1, 'Must have at least 1 question'),

  normal_quadrant_4_questions: Yup.array()
    .of(Yup.string().min(1, 'Too Short!').required('Required'))
    .required('Required')
    .min(1, 'Must have at least 1 question'),
  stress_quadrant_1_questions: Yup.array()
    .of(Yup.string().min(1, 'Too Short!').required('Required'))
    .required('Required')
    .min(1, 'Must have at least 1 question'),

  stress_quadrant_2_questions: Yup.array()
    .of(Yup.string().min(1, 'Too Short!').required('Required'))
    .required('Required')
    .min(1, 'Must have at least 1 question'),

  stress_quadrant_3_questions: Yup.array()
    .of(Yup.string().min(1, 'Too Short!').required('Required'))
    .required('Required')
    .min(1, 'Must have at least 1 question'),

  stress_quadrant_4_questions: Yup.array()
    .of(Yup.string().min(1, 'Too Short!').required('Required'))
    .required('Required')
    .min(1, 'Must have at least 1 question'),
});

const TAB_KEYS = {
  QUADRANT: 'QUADRANT',
  REPORT: 'REPORT',
};

function AddSelfAssessment(props) {
  const [tabKey, setTabKey] = useState(TAB_KEYS.QUADRANT);
  const [blocking, setBlocking] = useState(false);

  const {
    navigate,
    alert,
    params: { id: assessmentPageId },
  } = props;

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: (values) => {
      handleFormSubmit(values);
    },
  });

  function onSaveHandler(errors) {
    const { alert } = props;
    if (Object.keys(errors).length > 0) {
      return alert('danger', 'Error', 'Missing fields. Changes not saved! Please check your input.');
    }
  }

  useEffect(() => {
    if (assessmentPageId) {
      blockUi();

      getSelfAssessment(assessmentPageId).payload.then((result) => {
        const { data } = result;

        formik.setValues(data);
      });
      unBlockUi();
    }
  }, []);

  async function handleFormSubmit(values) {
    blockUi();

    const { created_at, id, updated_at, ...otherValues } = values;

    const body = {
      ...otherValues,
      normal_quadrant_1_questions: JSON.stringify(values.normal_quadrant_1_questions),
      normal_quadrant_2_questions: JSON.stringify(values.normal_quadrant_2_questions),
      normal_quadrant_3_questions: JSON.stringify(values.normal_quadrant_3_questions),
      normal_quadrant_4_questions: JSON.stringify(values.normal_quadrant_4_questions),
      stress_quadrant_1_questions: JSON.stringify(values.stress_quadrant_1_questions),
      stress_quadrant_2_questions: JSON.stringify(values.stress_quadrant_2_questions),
      stress_quadrant_3_questions: JSON.stringify(values.stress_quadrant_3_questions),
      stress_quadrant_4_questions: JSON.stringify(values.stress_quadrant_4_questions),
      normal_paragraphs: JSON.stringify(values.normal_paragraphs),
      stress_paragraphs: JSON.stringify(values.stress_paragraphs),
    };

    const [err, res] = await (assessmentPageId
      ? to(updateSelfAssessment(body).payload)
      : to(createSelfAssessment(body).payload));

    unBlockUi();

    if (err) {
      if (err?.response?.data?.message)
        alert(
          'danger',
          'Error',
          `Self-assessment not ${assessmentPageId ? 'updated' : 'created'}: ${err.response.data.message.toString()}`
        );
      else
        alert('danger', 'Error', `Self-assessment not ${assessmentPageId ? 'updated' : 'created'}: ${err.toString()}`);
    }

    if (res?.data?.id) {
      alert('success', 'Success', `Self-assessment ${assessmentPageId ? 'updated' : 'created'} successfully`);
    }

    if (!assessmentPageId) {
      navigate(`/self-assessments/${res.data.id}`);
    }
  }

  function blockUi() {
    setBlocking(true);
  }

  function unBlockUi() {
    setBlocking(false);
  }

  return (
    <div className="container root-container">
      <Tab.Container activeKey={tabKey}>
        <FormikProvider value={formik}>
          <BlockUi tag="div" blocking={blocking}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <PageHeader>Self Assessment</PageHeader>
              <div>
                <button
                  onClick={() => {
                    onSaveHandler(formik.errors);
                    formik.submitForm();
                  }}
                  className="btn btn-info pull-right"
                >
                  Save
                </button>
              </div>
            </div>
            <Row style={{ display: 'flex', alignItems: 'center', margin: '0px' }}>
              <Nav bsStyle="tabs" className={styles.tab}>
                <button
                  className={classNames(styles.quadButton, tabKey === TAB_KEYS.QUADRANT ? styles.activeButton : null)}
                  onClick={() => setTabKey(TAB_KEYS.QUADRANT)}
                >
                  Quadrants
                </button>
                <button
                  className={classNames(styles.repButton, tabKey === TAB_KEYS.REPORT ? styles.activeButton : null)}
                  onClick={() => setTabKey(TAB_KEYS.REPORT)}
                >
                  Reports
                </button>
              </Nav>

              <Col md={3}>
                <Field name="name" type="text" placeholder="Enter a Scheme Name" component={CustomFormControl}></Field>
              </Col>
            </Row>

            <Tab.Content animation>
              <Tab.Pane eventKey={TAB_KEYS.QUADRANT}>
                <Quadrants />
              </Tab.Pane>
              <Tab.Pane eventKey={TAB_KEYS.REPORT}>
                <Reports />
              </Tab.Pane>
            </Tab.Content>
          </BlockUi>
        </FormikProvider>
      </Tab.Container>
    </div>
  );
}

export default AddSelfAssessment;
