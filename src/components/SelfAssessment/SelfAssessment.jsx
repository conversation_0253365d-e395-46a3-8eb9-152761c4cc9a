import React, { useEffect, useState } from "react";
import to from "await-to-js";
import { toast } from "sonner";
import {
  createSelfAssessmentAnswers,
  getSelfAssessment,
} from "../../actions/user";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Loader from "../common/Loader";
import ConditionQuiz from "./components/ConditionQuiz/ConditionQuiz";
import AssessmentCompleted from "./components/AssessmentCompleted/AssessmentCompleted";
import {
  getAnswers,
  getAvailableQuestionsKeys,
  getLocalStorageAnswersKey,
  getLocalStorageAvailableQuestionsKey,
  getLocalStorageCurrentQuestionPropertiesKey,
} from "./components/Quiz/helpers";

export const assessmentTypeActive = {
  NORMAL: "normal",
  STRESS: "stress",
};

function SelfAssessment({ alert, user }) {
  const [isLoading, setLoading] = useState(false);
  const [activeType, setActiveType] = useState(null);
  const [normalQuizResult, setNormalQuizResult] = useState({});
  const [stressQuizResult, setStressQuizResult] = useState({});
  const [rawAnswers, setRawAnswers] = useState({});
  const [quizQuestions, setQuizQuestions] = useState({});
  const [error, setError] = useState("");

  const isNormalCompleted = !!Object.keys(normalQuizResult).length;
  const isStressCompleted = !!Object.keys(stressQuizResult).length;

  const {
    client: { selfAssessmentTabName, selfAssessmentAnswersId },
    user: currentUser,
  } = user;

  const teamId = currentUser?.id;

  useEffect(() => {
    proceedSelfAssessment();
  }, []);

  const setActiveQuiz = (type) => {
    setActiveType(type);
  };

  const handleQuizAnswers = (answers, type) => {
    const result = {};
    for (const key in answers) {
      result[key] = Number(
        (answers[key] / quizQuestions[type][key].length).toFixed(2)
      );
    }
    return result;
  };

  const normalQuizCompletedHandler = (answers) => {
    setActiveType(null);
    setNormalQuizResult(handleQuizAnswers(answers, "normal"));
  };

  const stressQuizCompletedHandler = (answers) => {
    setActiveType(null);
    setStressQuizResult(handleQuizAnswers(answers, "stress"));
  };

  const allAssessmentsCompletedHandler = async () => {
    const data = {
      ...normalQuizResult,
      ...stressQuizResult,
      ...{ raw_answers: rawAnswers },
    };

    const [err] = await to(createSelfAssessmentAnswers(data).payload);

    if (err) {
      toast.error("Something went wrong while saving assessment results");
      return;
    }

    // Clear local storage
    [assessmentTypeActive.NORMAL, assessmentTypeActive.STRESS].forEach(
      (type) => {
        localStorage.removeItem(
          getLocalStorageAnswersKey({ conditionType: type })
        );
        localStorage.removeItem(
          getLocalStorageAvailableQuestionsKey({ conditionType: type })
        );
        localStorage.removeItem(
          getLocalStorageCurrentQuestionPropertiesKey({ conditionType: type })
        );
      }
    );
  };

  useEffect(() => {
    if (isNormalCompleted && isStressCompleted && !selfAssessmentAnswersId) {
      setTimeout(() => {
        allAssessmentsCompletedHandler();
      }, 0);
    }
  }, [normalQuizResult, stressQuizResult]);

  const proceedSelfAssessment = async () => {
    setLoading(true);
    setError("");

    const [err, res] = await to(getSelfAssessment().payload);

    if (err) {
      setError("Something went wrong while loading the assessment.");
      setLoading(false);
      toast.error("Something went wrong.");
      return;
    }

    const {
      normal_quadrant_1_questions,
      normal_quadrant_2_questions,
      normal_quadrant_3_questions,
      normal_quadrant_4_questions,
      stress_quadrant_1_questions,
      stress_quadrant_2_questions,
      stress_quadrant_3_questions,
      stress_quadrant_4_questions,
    } = res.data;

    const normal = {
      normal_quadrant_1_questions,
      normal_quadrant_2_questions,
      normal_quadrant_3_questions,
      normal_quadrant_4_questions,
    };

    const stress = {
      stress_quadrant_1_questions,
      stress_quadrant_2_questions,
      stress_quadrant_3_questions,
      stress_quadrant_4_questions,
    };

    setQuizQuestions({ normal, stress });
    setLoading(false);

    if (
      getAvailableQuestionsKeys({
        conditionType: assessmentTypeActive.NORMAL,
        questions: normal,
      }).length === 0
    ) {
      setNormalQuizResult(
        getAnswers({ conditionType: assessmentTypeActive.NORMAL })
      );
    }

    if (
      getAvailableQuestionsKeys({
        conditionType: assessmentTypeActive.STRESS,
        questions: stress,
      }).length === 0
    ) {
      setStressQuizResult(
        getAnswers({ conditionType: assessmentTypeActive.STRESS })
      );
    }
  };

  if (isLoading) return <Loader />;

  return (
    <div className="p-8">
      {error && (
        <Alert
          variant="destructive"
          className="mb-6 bg-red-500/10 text-red-300 border-red-500/20"
        >
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="mb-8">
        <h1 className="text-2xl font-semibold text-white">
          {selfAssessmentTabName || "Self Assessments"}
        </h1>
      </div>

      {!selfAssessmentAnswersId &&
        (!isNormalCompleted || !isStressCompleted) && (
          <>
            <div className="mb-8">
              <div className="text-gray-300 space-y-4">
                <p>
                  Using the Launch Assessment buttons, you can take your two
                  self-assessments. The first represents how you feel you behave
                  under normal conditions.

                  In our real world, we operate in both environments and it's
                  important to understand <u>both</u> to best optimize our
                  impacts and interactions.

                  After you finish both assessments, a report will be generated
                  for you to review and use as you engage others in your role.
                  
                  We intentionally do not allow you to go back and review or
                  redo your responses, as our experience indicates your initial
                  response is the most reliable and accurate one.
                </p>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <ConditionQuiz
                quizQuestions={quizQuestions?.normal ?? {}}
                isActive={activeType === assessmentTypeActive.NORMAL}
                setActiveQuiz={setActiveQuiz}
                imgSource={"/images/wb_sunny.svg"}
                conditionType={assessmentTypeActive.NORMAL}
                canBeLaunched={
                  true // !activeType || activeType !== assessmentTypeActive.STRESS
                }
                quizCompletedHandler={normalQuizCompletedHandler}
                isCompleted={isNormalCompleted}
                setRawAnswers={setRawAnswers}
              />
              <ConditionQuiz
                quizQuestions={quizQuestions?.stress ?? {}}
                isActive={activeType === assessmentTypeActive.STRESS}
                setActiveQuiz={setActiveQuiz}
                imgSource={"/images/wb_zipper.svg"}
                conditionType={assessmentTypeActive.STRESS}
                canBeLaunched={
                  true // !activeType || activeType !== assessmentTypeActive.NORMAL
                }
                quizCompletedHandler={stressQuizCompletedHandler}
                isCompleted={isStressCompleted}
                setRawAnswers={setRawAnswers}
              />
            </div>
          </>
        )}

      {(!!selfAssessmentAnswersId ||
        (isNormalCompleted && isStressCompleted)) && (
        <AssessmentCompleted
          setLoading={setLoading}
          teamId={teamId}
          normalQuizResult={normalQuizResult}
          stressQuizResult={stressQuizResult}
        />
      )}
    </div>
  );
}

export default SelfAssessment;
