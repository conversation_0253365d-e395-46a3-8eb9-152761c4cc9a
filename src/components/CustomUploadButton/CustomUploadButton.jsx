import { useCreateNotification } from '../../hooks';
import { useState } from 'react';
import * as pdfjs from 'pdfjs-dist/build/pdf';
import { uploadFile } from '../../actions/selfAssessments';
import to from 'await-to-js';

async function isUSLetterSize(pdfFile) {
  const isPdf = pdfFile.type === 'application/pdf';

  if (!isPdf) {
    return false;
  }
  // Check if the file is a PDF
  const arrayBuffer = await pdfFile.arrayBuffer();
  const data = new Uint8Array(arrayBuffer);
  const pdf = await pdfjs.getDocument({ data }).promise;

  // Get the first page
  const page = await pdf.getPage(1);

  // Get the page size in points (1 inch = 72 points)
  const pageSize = page.getViewport({ scale: 1 });

  // Check if the page size is approximately US Letter (8.5 x 11 inches)
  const isLetterSize = Math.abs(pageSize.width - 8.5 * 72) < 10 && Math.abs(pageSize.height - 11 * 72) < 10;

  return isLetterSize;
}

export const CustomUploadButton = (props) => {
  const { field, form, label, fileName, ...otherProps } = props;
  const { alert } = useCreateNotification();
  const [loading, setLoading] = useState(false);

  function onClick(e) {
    if (field.value) {
      e.preventDefault();

      const a = document.createElement('a');
      a.href = field.value;
      a.click();
      a.remove();
    }
  }

  async function onChange(e) {
    const file = e.target?.files?.[0];

    if (file) {
      const isPdf = file.type === 'application/pdf';

      if (isPdf) {
        if (file.size > 10_485_760) {
          return alert('danger', 'Error', `PDF File is more than 10 MB.`);
        }

        const isValidPdf = await isUSLetterSize(file);

        if (!isValidPdf) {
          return alert('danger', 'Error', `Pdf is not US letter size.`);
        }
      }

      setLoading(true);
      const [err, res] = await to(uploadFile(file));

      if (err) {
        setLoading(false);
        return alert('danger', 'Error', `File was not uploaded.`);
      }

      const { url } = res.data;

      form.setFieldValue(field.name, url);
      setLoading(false);
      alert('success', 'Success', 'File was successfully uploaded');
    }
  }

  function onRemove() {
    setLoading(true);
    form.setFieldValue(field.name, '');
    setLoading(false);
  }

  return (
    <>
      <label disabled={loading} className="btn btn-info" htmlFor={field.name} onClick={onClick}>
        {loading && 'Uploading...'}
        {field.value && `${fileName} uploaded`}
        {!loading && !field.value && label}
      </label>
      {!field.value && <input {...otherProps} onChange={onChange} id={field.name} className="hidden" />}

      {field.value && (
        <button
          className="glyphicon glyphicon-remove"
          style={{
            marginLeft: '10px',
            border: '0px',
            backgroundColor: 'transparent',
            fontSize: '24px',
          }}
          onClick={onRemove}
        />
      )}
    </>
  );
};
