import React from "react";
import { useTheme } from "@/components/ThemeProvider/ThemeProvider";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { HomeIcon, ArrowLeft } from "lucide-react";

export default function NotFound() {
  const { theme } = useTheme();
  return (
    <div className="flex items-center justify-center">
      <div className={`backdrop-blur-xl bg-transparent rounded-2xl p-24 shadow-xl max-w-2xl w-full ${theme === 'dark' ? 'border border-gray-800' : 'border border-gray-300/30'}`}>
        <div className="flex flex-col items-center text-center space-y-6">
          {/* 404 Text */}
          <div className="relative">
            <h1 className="text-[10rem] font-bold bg-clip-text text-transparent bg-gradient-to-b from-blue-400 to-blue-600 leading-none">
              404
            </h1>
            <div className="absolute inset-0 animate-pulse opacity-50 blur-3xl bg-blue-500/20 rounded-full" />
          </div>

          {/* Message */}
          <div className="space-y-2">
            <h2 className={`text-2xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
              Page Not Found
            </h2>
            <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              Oops! The page you're looking for doesn't exist or has been moved.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 w-full max-w-xs">
            <Button
              variant="outline"
              className={`w-full ${theme === 'dark' ? 'border-gray-700 text-gray-600 hover:text-white hover:bg-gray-800' : 'border-gray-300 text-gray-700 hover:text-gray-900 hover:bg-gray-100'}`}
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>

            <Button
              className={`w-full text-white ${theme === 'dark' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'}`}
              asChild
            >
              <Link to="/">
                <HomeIcon className="mr-2 h-4 w-4" />
                Home
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
