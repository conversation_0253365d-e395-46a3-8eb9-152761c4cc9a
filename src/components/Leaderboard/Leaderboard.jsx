import React, { useEffect, useState, useMemo } from "react";
import to from "await-to-js";

import FiltersGroup from "../FiltersGroup/FiltersGroup";
import DefaultCandidate from "./components/DefaultCandidate/DefaultCandidate";
import TopCandidate from "./components/TopCandidate/TopCandidate";
import Loader from "../common/Loader";
import { getLeaderboard } from "../../actions/user";

import { toast } from "sonner";

const Leaderboard = ({ user, navigate }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [currentLeaderboardRegionId, setCurrentLeaderboardRegionId] =
    useState(-1);
  const [leaderboard, setLeaderboard] = useState({});

  const client = user?.client || {};
  const { leaderboardTabName, leaderboardTabVisibility, lightHighlightColor } =
    client;

  useEffect(() => {
    if (
      client?.hasOwnProperty("leaderboardTabVisibility") &&
      !leaderboardTabVisibility
    ) {
      navigate("/org-charts");
      return;
    }
    fetchLeaderboard();
  }, [client, leaderboardTabVisibility, navigate]);

  const fetchLeaderboard = async () => {
    setIsLoading(true);
    const [err, res] = await to(getLeaderboard().payload);

    if (err) {
      toast.error("Failed to load leaderboard");
      setIsLoading(false);
      return;
    }

    setLeaderboard(res.data);
    if (res.data.regions?.length) {
      setCurrentLeaderboardRegionId(res.data.regions[0].id);
    }
    setIsLoading(false);
  };

  const selectedLeaderboardRegion = useMemo(
    () =>
      leaderboard.regions?.find(({ id }) => id === currentLeaderboardRegionId),
    [leaderboard, currentLeaderboardRegionId]
  );

  const topCandidates = useMemo(() => {
    if (!selectedLeaderboardRegion) return [];
    const topUsers = selectedLeaderboardRegion.users.slice(0, 3);

    if (topUsers.length === 3) return topUsers;
    if (topUsers.length === 2) {
      return [...topUsers, { id: "FAKE_USER_1", type: "fake" }];
    }
    if (topUsers.length === 1) {
      return [
        ...topUsers,
        { id: "FAKE_USER_1", type: "fake" },
        { id: "FAKE_USER_2", type: "fake" },
      ];
    }
    return [];
  }, [selectedLeaderboardRegion]);

  const notTopCandidates = useMemo(() => {
    if (!selectedLeaderboardRegion) return [];
    return selectedLeaderboardRegion.users.slice(3);
  }, [selectedLeaderboardRegion]);

  const filters = useMemo(() => {
    if (!leaderboard?.regions?.length) return [];
    return leaderboard.regions.map(({ id, name }) => ({
      value: id,
      label: name,
    }));
  }, [leaderboard]);

  if (isLoading) return <Loader />;

  return (
    <div className="space-y-8 p-4 pb-8">
      <LeaderboardHeader title={leaderboardTabName || "Leaderboard"} />

      {leaderboard?.regions?.length > 0 && (
        <FiltersGroup
          filters={filters}
          currentFilter={currentLeaderboardRegionId}
          backgroundColor={lightHighlightColor}
          onClick={setCurrentLeaderboardRegionId}
        />
      )}

      {selectedLeaderboardRegion?.users && (
        <div className="space-y-8">
          {/* Top 3 Candidates */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Top Candidates</h3>
            <div className="flex flex-col md:flex-row justify-center gap-4">
              {topCandidates.map((candidate, i) => (
                <TopCandidate key={i} {...candidate} place={i + 1} />
              ))}
            </div>
          </div>

          {/* Other Candidates */}
          {notTopCandidates.length > 0 && (
            <div className="space-y-4 bg-white/5 backdrop-blur-xl rounded-xl border border-white/10 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                Other Candidates
              </h3>
              <div className="space-y-2">
                {notTopCandidates.map((candidate, i) => (
                  <DefaultCandidate key={i} {...candidate} place={4 + i} />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const LeaderboardHeader = ({ title }) => (
  <div className="relative pb-2">
    <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-white to-white/60">
      {title}
    </h1>
    <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-white/5 via-white/10 to-transparent" />
  </div>
);

export default Leaderboard;
