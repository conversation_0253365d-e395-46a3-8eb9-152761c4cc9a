import React from "react";
import { Trophy } from "lucide-react";

const TopCandidate = ({ name, type, points, place }) => {
  const getPlaceStyles = (place) => {
    switch (place) {
      case 1:
        return {
          background: "rgba(255, 215, 0, 0.08)",
          border: "border-yellow-500/20",
          trophyColor: "#FFD700",
          badgeGradient: "from-yellow-400 to-yellow-500",
          pointsBackground: "bg-yellow-500/10",
          pointsText: "text-yellow-200",
        };
      case 2:
        return {
          background: "rgba(192, 192, 192, 0.08)",
          border: "border-gray-400/20",
          trophyColor: "#C0C0C0",
          badgeGradient: "from-gray-300 to-gray-400",
          pointsBackground: "bg-gray-500/10",
          pointsText: "text-gray-200",
        };
      case 3:
        return {
          background: "rgba(205, 127, 50, 0.08)",
          border: "border-amber-500/20",
          trophyColor: "#CD7F32",
          badgeGradient: "from-amber-400 to-amber-500",
          pointsBackground: "bg-amber-500/10",
          pointsText: "text-amber-200",
        };
      default:
        return {
          background: "bg-white/5",
          border: "border-white/10",
          trophyColor: "#ffffff",
          badgeGradient: "from-gray-400 to-gray-500",
          pointsBackground: "bg-white/10",
          pointsText: "text-gray-200",
        };
    }
  };

  if (type === "fake") return null;

  const {
    background,
    border,
    trophyColor,
    badgeGradient,
    pointsBackground,
    pointsText,
  } = getPlaceStyles(place);

  return (
    <div
      className={`relative ${
        place === 1 ? "order-2 scale-110" : place === 2 ? "order-1" : "order-3"
      }`}
    >
      <div
        className={`
          relative overflow-hidden
          flex flex-col items-center
          px-8 py-6 rounded-xl
          backdrop-blur-xl border ${border}
          transition-all duration-300
          hover:scale-105
        `}
        style={{ background }}
      >
        {/* Place Badge */}
        <div
          className={`
          absolute top-3 right-3
          text-xs font-medium px-2.5 py-1 rounded-full
          bg-gradient-to-r ${badgeGradient} text-black/80
        `}
        >
          #{place}
        </div>

        {/* Trophy Icon */}
        <div className="mb-4">
          <Trophy size={44} strokeWidth={1.5} style={{ color: trophyColor }} />
        </div>

        {/* User Info */}
        <div className="text-center">
          <h3 className="text-lg font-semibold text-white mb-2 truncate max-w-[150px]">
            {name}
          </h3>
          <div
            className={`
            inline-block px-3 py-1 rounded-full text-sm font-medium
            ${pointsBackground} ${pointsText}
          `}
          >
            {points} points
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopCandidate;
