import React, { useState, useEffect } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { Lock } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Loader from "../common/Loader";
import { toast } from "sonner";
import to from "await-to-js";
import { recoverPassword } from "../../actions/user";

const RecoverPassword = () => {
  const navigate = useNavigate();
  const params = useParams();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: "",
  });

  // Remove clientId from localStorage on component mount
  useEffect(() => {
    localStorage.removeItem("clientId");
  }, []);

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);
    setError("");

    const { password, confirmPassword } = formData;

    if (password !== confirmPassword) {
      const message = "Password & confirm password should be equal";
      setError(message);
      toast.error(message);
      setIsLoading(false);
      return;
    }

    const body = {
      password,
      teamId: params.teamId,
      code: params.code,
    };

    const [err] = await to(recoverPassword(body).payload);

    if (err) {
      const message = `Reset password error. ${err.response.data.message.toString()}`;
      setError(message);
      toast.error(message);
      setIsLoading(false);
      return;
    }

    toast.success("Password was successfully reset!");
    navigate("/");
    setIsLoading(false);
  };

  return (
    <div className="flex justify-center">
      <div className="w-[90vw] md:w-[30rem]">
        <div className="backdrop-blur-xl bg-transparent p-8 rounded-2xl shadow-xl border border-gray-800">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-2xl font-bold text-white mb-2">
              Reset Password
            </h1>
            <p className="text-gray-300">Enter your new password</p>
          </div>

          {error && (
            <Alert
              variant="destructive"
              className="mb-6 bg-red-500/10 text-red-300 border-red-500/20"
            >
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                New Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="block w-full pl-10 h-12 bg-white/5 border border-white/10 rounded-lg 
                           text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 
                           focus:border-transparent"
                  placeholder="Enter new password"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                Confirm Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="password"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="block w-full pl-10 h-12 bg-white/5 border border-white/10 rounded-lg 
                           text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 
                           focus:border-transparent"
                  placeholder="Confirm new password"
                />
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading || !formData.password.length}
              className={`w-full py-2 px-4 flex items-center justify-center
                bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg
                transition-colors duration-200
                ${
                  isLoading || !formData.password.length
                    ? "opacity-70 cursor-not-allowed"
                    : ""
                }`}
            >
              {isLoading ? (
                <>
                  <Loader fullScreen />
                </>
              ) : (
                "Reset Password"
              )}
            </button>
          </form>

          <div className="mt-6 text-center text-gray-300">
            Remember your password?{" "}
            <Link
              to="/"
              className="text-blue-400 hover:text-blue-300 font-medium"
            >
              Sign In
            </Link>
            {" or "}
            <Link
              to="/sign-up"
              className="text-blue-400 hover:text-blue-300 font-medium"
            >
              Sign Up
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecoverPassword;
