import React from 'react';

import { Col, FormControl, InputGroup, Panel, Row } from 'react-bootstrap';

export default function GlobalTeamMetrics(props) {
  const { globalTeamMetrics, onChange } = props;

  function changeGlobalTeamMetric(index, key, event) {
    const { value } = event.target;

    if (value > 100) return 100;

    if (value < 0) return 0;

    onChange(index, key, value);
  }

  function renderMetricsHeader(metric, i) {
    return (
      <FormControl
        type="text"
        value={metric.name}
        placeholder="Enter Metric Name"
        onChange={(e) => changeGlobalTeamMetric(i, 'name', e)}
      />
    );
  }

  return (
    <Row>
      <Col md={12}>
        <p>These metrics are visible on all pages on the frontend</p>
      </Col>

      {globalTeamMetrics.map((metric, i) => (
        // eslint-disable-next-line react/no-array-index-key
        <Col md={4} key={i} className="margin-bottom-20">
          <Panel header={renderMetricsHeader(metric, i)}>
            <Row>
              <Col md={12}>
                <InputGroup>
                  <FormControl
                    type="number"
                    value={metric.defaultValue}
                    placeholder="Enter number"
                    max={100}
                    min={0}
                    onChange={(e) => changeGlobalTeamMetric(i, 'defaultValue', e)}
                  />
                  <InputGroup.Addon>%</InputGroup.Addon>
                </InputGroup>
              </Col>
            </Row>
          </Panel>
        </Col>
      ))}
    </Row>
  );
}
