import React, { useRef, useState } from 'react';
import SketchPicker from 'react-color/lib/Sketch';
import { useClickAway } from 'react-use';

import styles from './color-picker.module.scss';

export default function ColorPicker({ value, onChange }) {
  const pickerRef = useRef(null);
  const [showPicker, setShowPicker] = useState(false);

  useClickAway(pickerRef, () => setShowPicker(false));

  return (
    <div className={styles.container} onClick={() => setShowPicker(true)}>
      <div className={styles.input}>
        <div className={styles.color} style={{ backgroundColor: value }} />
        <div className={styles.value}>{value}</div>
      </div>

      {showPicker ? (
        <div ref={pickerRef} className={styles.picker}>
          <SketchPicker color={value} onChange={({ hex }) => onChange(hex)} disableAlpha presetColors={[]} />
        </div>
      ) : null}
    </div>
  );
}
