.container {
  position: relative;
  width: fit-content;

  .input {
    cursor: pointer;
    overflow: hidden;
    width: 120px;
    height: 30px;
    display: flex;
    border-radius: 3px;
    border: 1px solid #e4e8f3;
    box-shadow: 0 2px 10px rgba(#4967ad, 0.2);
    background: white;

    .color {
      width: 25%;
      border-right: 1px solid #e4e8f3;
    }

    .value {
      font-size: 16px;
      font-weight: normal;
      line-height: 150%;
      width: 75%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .picker {
    position: absolute;
    top: 40px;
    z-index: 3;
  }
}
