import React from 'react';
import { ControlLabel, FormControl, FormGroup } from 'react-bootstrap';

export default function SchemeSelect(props) {
  const { title, placeholder, value, scheme, schemes, setSchemes } = props;

  return (
    <FormGroup>
      <ControlLabel>{title}</ControlLabel>
      <FormControl
        value={value}
        componentClass="select"
        placeholder={placeholder}
        onChange={(e) => setSchemes((prev) => ({ ...prev, [scheme]: e.target.value }))}
      >
        <option value={0} />
        {schemes.map((item, i) => (
          <option key={i} value={item.id}>
            {item.name}
          </option>
        ))}
      </FormControl>
    </FormGroup>
  );
}
