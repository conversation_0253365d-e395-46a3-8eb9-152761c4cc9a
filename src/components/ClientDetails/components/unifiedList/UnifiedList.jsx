import React, { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import UnifiedListItem from './UnifiedListItem';

export const schemeMapping = {
  welcomePages: { value: 'home', label: 'Home Scheme', placeholder: 'Select Home Scheme' },
  initiatives: { value: 'initiatives', label: 'Initiative Scheme', placeholder: 'Select Initiative Scheme' },
  challenges: { value: 'challenges', label: 'Challenge Scheme', placeholder: 'Select Challenge Scheme' },
  leaderboards: { value: 'leaderboard', label: 'Leaderboard Scheme', placeholder: 'Select Leaderboard Scheme' },
  orgCharts: { value: 'orgChart', label: 'Org Chart Scheme', placeholder: 'Select Org Chart Scheme' },
  selfAssessments: {
    value: 'selfAssessment',
    label: 'Self Assessment Scheme',
    placeholder: 'Select Self Assessment Scheme',
  },
  decisions: { value: 'decisions', label: 'Decision Scheme', placeholder: 'Select Decision Scheme' },
  // Add goals as a special case with noSchemeSelection set to true
  goals: { value: 'goals', label: 'Goals Tab', placeholder: 'No scheme selection needed', noSchemeSelection: true },
};
export default function UnifiedList({
  schemesData,
  selectedSchemes,
  pageToggle,
  tabNames,
  setSchemes,
  setPageToggle,
  setTabNames,
  schemeOrder = [], // Add this prop
  onOrderChange, // Add this prop
}) {
  const [items, setItems] = useState([]);

  useEffect(() => {
    // Create items array based on schemeOrder if it exists, otherwise use default order
    const orderedKeys = schemeOrder.length > 0 ? schemeOrder : Object.keys(schemeMapping);

    // Filter out 'goals' from the ordered keys since we'll handle it separately
    const filteredKeys = orderedKeys.filter(scheme => scheme !== 'goals');
    
    // Create items for all schemes except goals
    const schemeItems = filteredKeys
      .filter((scheme) => {
        // For other schemes, only include if they exist in schemesData
        return schemesData[scheme];
      })
      .map((scheme) => {
        const mappedScheme = schemeMapping[scheme];
        return {
          id: scheme,
          value: selectedSchemes[mappedScheme?.value],
          list: schemesData[scheme],
          label: mappedScheme?.label,
          placeholder: mappedScheme?.placeholder,
          isSelected: pageToggle[mappedScheme?.value],
          tabName: tabNames[mappedScheme?.value],
          noSchemeSelection: mappedScheme?.noSchemeSelection,
        };
      });

    setItems(schemeItems);
  }, [schemesData, selectedSchemes, pageToggle, tabNames, schemeOrder]);

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const newItems = Array.from(items);
    const [reorderedItem] = newItems.splice(result.source.index, 1);
    newItems.splice(result.destination.index, 0, reorderedItem);

    setItems(newItems);

    // Call onOrderChange with the new order of scheme IDs
    if (onOrderChange) {
      const newOrder = newItems.map((item) => item.id);
      onOrderChange(newOrder);
    }
  };

  // Create the goals item separately
  const goalsItem = {
    id: 'goals',
    value: null,
    list: [],
    label: schemeMapping.goals.label,
    placeholder: schemeMapping.goals.placeholder,
    isSelected: pageToggle.goals,
    tabName: tabNames.goals,
    noSchemeSelection: true,
  };

  return (
    <div>
      {/* Render Goals tab separately at the top, not draggable */}
      <div
        style={{
          padding: '16px',
          background: 'rgba(255, 255, 255, 0.3)',
          backdropFilter: 'blur(12px)',
          WebkitBackdropFilter: 'blur(12px)', // for Safari
          border: '1px solid rgba(255, 255, 255, 0.3)',
          borderRadius: '8px',
          marginBottom: 8,
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        }}
      >
        <UnifiedListItem
          itemData={goalsItem}
          index={-1}
          setSchemes={setSchemes}
          setPageToggle={setPageToggle}
          setTabNames={setTabNames}
        />
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="schemes">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              style={{
                borderRadius: '4px',
                minHeight: '100px',
              }}
            >
              {items.map((item, index) => (
                <Draggable key={item.id} draggableId={item.id} index={index}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      style={{
                        padding: '16px',
                        background: snapshot.isDragging ? 'rgba(233, 236, 239, 0.8)' : 'rgba(255, 255, 255, 0.3)',
                        backdropFilter: 'blur(12px)',
                        WebkitBackdropFilter: 'blur(12px)', // for Safari
                        border: '1px solid rgba(255, 255, 255, 0.3)',
                        borderRadius: '8px',
                        marginBottom: 8,
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        transition: 'all 0.3s ease',
                        ...provided.draggableProps.style,
                      }}
                    >
                      <UnifiedListItem
                        itemData={item}
                        index={index}
                        setSchemes={setSchemes}
                        setPageToggle={setPageToggle}
                        setTabNames={setTabNames}
                      />
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  );
}
