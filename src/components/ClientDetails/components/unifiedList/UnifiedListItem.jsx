import React from 'react';
import { FormControl, FormGroup, Checkbox } from 'react-bootstrap';
import styles from './unified-list.module.scss';
import { schemeMapping } from './UnifiedList';

export default function UnifiedListItem({ itemData, index, setSchemes, setPageToggle, setTabNames }) {
  const { id, label, value, list, placeholder, isSelected, tabName, noSchemeSelection } = itemData;

  const handleSchemeUpdate = (e) => {
    setSchemes((prev) => ({ ...prev, [schemeMapping[id].value]: e.target.value }));
  };
  const handleTabNameUpdate = (e) => {
    setTabNames((prev) => ({ ...prev, [schemeMapping[id].value]: e.target.value }));
  };
  const handlePageToggle = (e) => {
    setPageToggle((prev) => ({ ...prev, [schemeMapping[id].value]: e.target.checked }));
  };

  return (
    <div className={styles.listItem}>
      <div className={styles.header}>
        <div className={styles.label}>{label}</div>
        {/* Only show drag handle if not goals */}
        {id !== 'goals' && (
          <div className={styles.dragHandle}>
            <i className="fa fa-bars" />
          </div>
        )}
      </div>

      <div className={styles.controls}>
        {!noSchemeSelection ? (
          <FormGroup className={styles.select}>
            <FormControl
              value={value || 0}
              componentClass="select"
              placeholder={placeholder}
              onChange={handleSchemeUpdate}
            >
              <option value={0} />
              {list?.map((item, i) => (
                <option key={i} value={item.id}>
                  {item.name}
                </option>
              ))}
            </FormControl>
          </FormGroup>
        ) : (
          <div className={styles.select}>
            <span className={styles.noSchemeText}>No scheme selection needed</span>
          </div>
        )}

        <FormGroup className={styles.tabName}>
          <FormControl type="text" value={tabName || ''} placeholder="Tab Name" onChange={handleTabNameUpdate} />
        </FormGroup>
        <div className={styles.toggle}>
          <Checkbox checked={isSelected} onChange={handlePageToggle}>
            Show Tab
          </Checkbox>
        </div>
      </div>
    </div>
  );
}
