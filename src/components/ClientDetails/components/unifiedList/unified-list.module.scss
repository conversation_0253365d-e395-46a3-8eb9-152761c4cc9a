.d-list-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
  min-height: 100px;
}

.listItem {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  position: relative;
  z-index: 2;
  .headerContainer {
    display: flex;
    align-items: center;
    gap: 16px;
    .orderNumber {
      font-weight: 500;
      color: #666;
    }

    .label {
      font-weight: 500;
      min-width: 150px;
    }
  }

  .controls {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    justify-content: flex-end;

    .select {
      margin-bottom: 0px;
      flex: 1;
    }

    .toggle {
      min-width: 120px;
    }

    .tabName {
      min-width: 150px;
      margin-bottom: 0px;
      flex: 1;
    }
  }
}

.noSchemeText {
  color: #999;
  font-style: italic;
  padding: 6px 12px;
  display: block;
}
