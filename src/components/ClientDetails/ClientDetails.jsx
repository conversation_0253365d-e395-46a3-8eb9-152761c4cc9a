import React, { useState, useEffect, useMemo } from 'react';
import BlockUi from 'react-block-ui';
import to from 'await-to-js';
import cn from 'classnames';
import { Button, Col, Checkbox, ControlLabel, FormGroup, PageHeader, Row, ButtonGroup } from 'react-bootstrap';
import fileDownload from 'js-file-download';

import GlobalTeamMetrics from './components/GlobalTeamMetrics/GlobalTeamMetrics';
import ColorPicker from './components/ColorPicker/ColorPicker';
import SchemeSelect from './components/SchemeSelect/SchemeSelect';
import { createClient, updateClient, getClient, uploadImage, exportExcel, exportJson } from '../../actions/clients';
import { listAllChallenges } from '../../actions/challenge';
import { listAllInitiatives } from '../../actions/initiative';
import { getAllLeaderboards } from '../../actions/leaderboards';
import { getAllOrgCharts } from '../../actions/orgCharts';
import { getAllWelcomePages } from '../../actions/welcomePages';
import { getAllSelfAssessments } from '../../actions/selfAssessments';

import styles from './client-details.module.scss';
import UnifiedList from './components/unifiedList/UnifiedList';
import { getDecisions } from '../../actions/decisions';

export default function ClientDetails(props) {
  const [name, setName] = useState('');
  const [homeSchemes, setHomeSchemes] = useState([]);
  const [challengeSchemes, setChallengeSchemes] = useState([]);
  const [initiativeSchemes, setInitiativeSchemes] = useState([]);
  const [leaderboardSchemes, setLeaderboardSchemes] = useState([]);
  const [orgChartSchemes, setOrgChartSchemes] = useState([]);
  const [selfAssessmentSchemes, setSelfAssessmentSchemes] = useState([]);
  const [goalsSchemes, setGoalsSchemes] = useState([]);
  const [globalTeamMetricsSchemes, setGlobalTeamMetricsSchemes] = useState([
    {
      name: '',
      defaultValue: 0,
      alias: 'metric 1',
    },
    {
      name: '',
      defaultValue: 0,
      alias: 'metric 2',
    },
    {
      name: '',
      defaultValue: 0,
      alias: 'metric 3',
    },
  ]);
  const [schemes, setSchemes] = useState({
    home: 0,
    initiatives: 0, // Changed from 'initiative' to 'initiatives'
    challenges: 0,
    leaderboard: 0,
    orgChart: 0,
    selfAssessment: 0,
    decisions: 0,
  });
  const [pageToggle, setPageToggle] = useState({
    home: true,
    goals: true,
    initiatives: true,
    challenges: true,
    orgChart: true,
    leaderboard: true,
    selfAssessment: true,
    decisions: true,
  });
  const [tabNames, setTabNames] = useState({
    home: 'Welcome',
    goals: 'Goals',
    initiatives: 'Strategic Initiatives',
    challenges: 'Challenges',
    orgChart: 'Org Chart',
    selfAssessment: 'Self Assessment',
    leaderboard: 'Leaderboard',
    decisions: 'Decisions',
  });
  const [backgroundImage, setBackgroundImage] = useState('');
  const [darkHighlightColor, setDarkHighlightColor] = useState('#004864');
  const [lightHighlightColor, setLightHighlightColor] = useState('#00A3E3');
  const [logoImage, setLogoImage] = useState('');
  const [isSignUpEnabled, setSignUpEnabled] = useState(false);
  const [signUpEmailDomain, setSignUpEmailDomain] = useState('');
  const [workshopImage, setWorkshopImage] = useState('');
  const [investmentMax, setInvestmentMax] = useState('');
  const [fteMax, setFteMax] = useState('');
  const [aiSummaryTitle, setAiSummaryTitle] = useState('');
  const [isLoading, setLoading] = useState(false);
  const { params, alert, navigate } = props;
  const [isNewClient, setIsNewClient] = useState(params.clientId === 'new');
  const [schemeOrder, setSchemeOrder] = useState([]);

  const [allSchemeData, setAllSchemeData] = useState({});
  const isEveryGlobalMetricHaveName = useMemo(
    () => globalTeamMetricsSchemes.every(({ name }) => name),
    [globalTeamMetricsSchemes]
  );

  useEffect(() => {
    const isNew = params.clientId === 'new';

    proceedMetaInformation();

    if (isNew) return;

    setIsNewClient(false);
    proceedClientDetails(params.clientId);
  }, [isNewClient, params.clientId]);

  async function setImage(selector = 'background-image') {
    setLoading(true);

    const file = document.getElementById(selector).files[0];
    const [err, res] = await to(uploadImage(file));

    setLoading(false);

    if (err) return alert('danger', 'Error', `${selector} was not uploaded.`);

    const { url } = res.data;

    alert('success', 'Success', 'Image was successfully uploaded');

    switch (selector) {
      case 'background-image':
        return setBackgroundImage(url);
      case 'logo':
        return setLogoImage(url);
      case 'workshop':
        return setWorkshopImage(url);
    }
  }

  function removeImage(selector = 'background-image') {
    document.getElementById(selector).value = '';

    switch (selector) {
      case 'background-image':
        return setBackgroundImage('');
      case 'logo':
        return setLogoImage('');
      case 'workshop':
        return setWorkshopImage('');
    }
  }

  async function proceedMetaInformation() {
    try {
      // Get all the data in parallel, excluding goals since they don't have schemes
      const [welcomePages, challenges, initiatives, leaderboards, orgCharts, selfAssessments, decisions] =
        await Promise.all([
          getAllWelcomePages().payload,
          listAllChallenges().payload,
          listAllInitiatives().payload,
          getAllLeaderboards().payload,
          getAllOrgCharts().payload,
          getAllSelfAssessments().payload,
          getDecisions().payload,
        ]);

      setHomeSchemes(welcomePages.data);
      setChallengeSchemes(challenges.data);
      setInitiativeSchemes(initiatives.data);
      setLeaderboardSchemes(leaderboards.data);
      setOrgChartSchemes(orgCharts.data);
      setSelfAssessmentSchemes(selfAssessments.data);
      setAllSchemeData({
        welcomePages: welcomePages?.data,
        challenges: challenges?.data,
        initiatives: initiatives?.data,
        leaderboards: leaderboards?.data,
        orgCharts: orgCharts?.data,
        selfAssessments: selfAssessments?.data,
        decisions: decisions?.data,
        // Add an empty goals entry to ensure it's included in the UI
        goals: [],
      });
    } catch (error) {
      console.error('Error fetching meta information:', error);
      // Handle the error appropriately
    }
  }

  async function proceedClientDetails(clientId) {
    setLoading(true);

    const [err, res] = await to(getClient(clientId).payload);

    setLoading(false);

    if (err) return alert('danger', 'Error', 'Oops, something went wrong with fetching client details');

    const {
      backgroundImage,
      challengesTabName,
      goalsTabName,
      logoImage,
      name,
      homeTabName,
      strategicInitiativesTabName,
      orgChartTabName,
      selfAssessmentTabName,
      leaderboardTabName,
      darkHighlightColor,
      lightHighlightColor,
      homeSchemeId,
      initiativeSchemeId,
      challengeSchemeId,
      leaderboardSchemeId,
      orgChartSchemeId,
      selfAssessmentSchemeId,
      homeTabVisibility,
      challengesTabVisibility,
      goalsTabVisibility,
      initiativesTabVisibility,
      leaderboardTabVisibility,
      orgChartTabVisibility,
      selfAssessmentTabVisibility,
      globalTeamMetricsSchemes = [],
      isSignUpEnabled,
      signUpEmailDomain,
      workshopImage,
      decisionSchemeId,
      decisionsTabVisibility,
      decisionsTabName,
      schemeOrder = [],
      investmentMax,
      fteMax,
      aiSummaryTitle,
    } = res.data;

    setSignUpEnabled(isSignUpEnabled);
    setSignUpEmailDomain(signUpEmailDomain);
    setWorkshopImage(workshopImage);
    setInvestmentMax(investmentMax || '');
    setFteMax(fteMax || '');
    setAiSummaryTitle(aiSummaryTitle || '');
    setBackgroundImage(backgroundImage);
    setLogoImage(logoImage);
    setName(name);
    setPageToggle({
      home: homeTabVisibility,
      goals: goalsTabVisibility,
      initiatives: initiativesTabVisibility,
      challenges: challengesTabVisibility,
      orgChart: orgChartTabVisibility,
      leaderboard: leaderboardTabVisibility,
      selfAssessment: selfAssessmentTabVisibility, // Correctly map from API response
      decisions: decisionsTabVisibility,
    });
    setTabNames((prev) => ({
      ...prev,
      home: homeTabName,
      goals: goalsTabName,
      initiatives: strategicInitiativesTabName,
      challenges: challengesTabName,
      orgChart: orgChartTabName,
      selfAssessment: selfAssessmentTabName,
      leaderboard: leaderboardTabName,
      decisions: decisionsTabName,
    }));
    setSchemes({
      home: homeSchemeId,
      initiatives: initiativeSchemeId, // Changed from 'initiative' to 'initiatives'
      challenges: challengeSchemeId,
      leaderboard: leaderboardSchemeId,
      orgChart: orgChartSchemeId,
      selfAssessment: selfAssessmentSchemeId,
      decisions: decisionSchemeId,
    });
    setSchemeOrder(schemeOrder);

    if (darkHighlightColor) setDarkHighlightColor(darkHighlightColor);

    if (lightHighlightColor) setLightHighlightColor(lightHighlightColor);

    if (globalTeamMetricsSchemes && globalTeamMetricsSchemes.length) {
      const resultGlobalTeamMetricsSchemes = [];

      globalTeamMetricsSchemes.forEach((globalTeamMetricScheme) => {
        const { id, name, alias, default_value: defaultValue } = globalTeamMetricScheme;

        resultGlobalTeamMetricsSchemes.push({ id, name, alias, defaultValue });
      });

      setGlobalTeamMetricsSchemes(resultGlobalTeamMetricsSchemes);
    }
  }

  async function saveClient() {
    if (!name) return alert('danger', 'Error', 'Name is required field');

    setLoading(true);

    if (!isEveryGlobalMetricHaveName) {
      setLoading(false);
      return alert('danger', 'Error', 'All Global Team metrics should have a name');
    }

    const payload = {
      name,
      backgroundImage,
      logoImage,
      homeTabName: tabNames.home,
      challengesTabName: tabNames.challenges,
      goalsTabName: tabNames.goals,  // Ensure this is included
      strategicTabName: tabNames.initiatives, // Use strategicTabName to match the API
      orgChartTabName: tabNames.orgChart,
      selfAssessmentTabName: tabNames.selfAssessment,
      leaderboardTabName: tabNames.leaderboard,
      darkHighlightColor,
      lightHighlightColor,
      globalTeamMetricsSchemes,
      homeSchemeId: schemes.home,
      initiativeSchemeId: schemes.initiatives, // Changed from 'initiative' to 'initiatives'
      challengeSchemeId: schemes.challenges,
      leaderboardSchemeId: schemes.leaderboard,
      orgChartSchemeId: schemes.orgChart,
      selfAssessmentSchemeId: schemes.selfAssessment,
      // No goalsSchemeId here since it doesn't exist
      homeTabVisibility: pageToggle.home,
      goalsTabVisibility: pageToggle.goals,  // Ensure this is included
      challengesTabVisibility: pageToggle.challenges,
      initiativesTabVisibility: pageToggle.initiatives,
      orgChartTabVisibility: pageToggle.orgChart,
      selfAssessmentTabVisibility: pageToggle.selfAssessment,
      leaderboardTabVisibility: pageToggle.leaderboard,
      isSignUpEnabled,
      signUpEmailDomain,
      workshopImage,
      decisionSchemeId: parseInt(schemes.decisions),
      decisionsTabVisibility: pageToggle.decisions,
      decisionsTabName: tabNames.decisions,
      schemeOrder,
      investmentMax: investmentMax ? parseFloat(investmentMax) : null,
      fteMax: fteMax ? parseFloat(fteMax) : null,
      aiSummaryTitle: aiSummaryTitle || null,
    };

    if (!payload.darkHighlightColor) payload.darkHighlightColor = '#004864';

    if (!payload.lightHighlightColor) payload.lightHighlightColor = '#00A3E3';

    let err;
    let res;

    if (!isNewClient) [err, res] = await to(updateClient({ ...payload, id: params.clientId }).payload);
    else [err, res] = await to(createClient(payload).payload);

    setLoading(false);

    if (err) return alert('danger', 'Error', 'Oops, something went wrong with saving client details');

    alert('success', 'Success', `Client was successfully ${isNewClient ? 'created' : 'updated'}`);

    if (isNewClient) {
      const { id } = res.data;

      navigate(`/clients/${id}`, { replace: true });
    }
  }

  async function exportClient() {
    setLoading(true);

    const [err, res] = await to(exportJson(params.clientId));

    setLoading(false);

    if (err) return alert('danger', 'Error', `Oops, something went wrong. ${err.message}`);

    // Create CSV data with teams as columns
    const csvData = [];
  
    // Get all teams for header row
    const teams = res.teams || [];
    const headerRow = ['Source'];
    teams.forEach(team => {
      headerRow.push(team.name || `Team ${team.id}`);
    });
    csvData.push(headerRow);
  
    // Org Chart section
    if (res.orgCharts && res.orgCharts.length > 0) {
      csvData.push([]); // Empty row as separator
      csvData.push(['ORG CHART']);
      
      // Process each org chart
      res.orgCharts.forEach((orgChart, chartIndex) => {
        csvData.push([`Org Chart #${chartIndex + 1}`]);
        
        // Process each org chart type
        if (orgChart.orgChartTypes && orgChart.orgChartTypes.length > 0) {
          orgChart.orgChartTypes.forEach(orgChartType => {
            csvData.push([`Chart Type: ${orgChartType.name || orgChartType.type || 'Unknown'}`]);
            
            // Process users in this org chart type
            if (orgChartType.users && orgChartType.users.length > 0) {
              // Create a map of user statuses
              const statusMap = {
                '-1': 'Non-Supporter',
                '0': 'Unknown',
                '1': 'Neutral',
                '2': 'Supporter'
              };
              
              // Add each user as a row
              orgChartType.users.forEach(user => {
                const row = [`Person: ${user.name} (${user.title || 'No Title'})`];
                
                // For each team, find if they've met with this person
                teams.forEach(team => {
                  const teamOrgChartUser = res.teamSelectedOrgCharts?.find(
                    toc => toc.team_id === team.id && toc.org_chart_user_id === user.id
                  );
                  
                  if (teamOrgChartUser) {
                    // Check which meetings have been completed
                    const meetings = [];
                    if (user.meet_1) meetings.push('Meeting 1');
                    if (user.meet_2) meetings.push('Meeting 2');
                    if (user.meet_3) meetings.push('Meeting 3');
                    
                    if (meetings.length > 0) {
                      row.push(`Met: ${meetings.join(', ')}, Status: ${statusMap[user.status] || 'Unknown'}`);
                    } else {
                      row.push(`Status: ${statusMap[user.status] || 'Unknown'}`);
                    }
                  } else {
                    row.push('No interaction');
                  }
                });
                
                csvData.push(row);
              });
            }
          });
        }
      });
    }
  
    // Challenges section
    if (res.challenges && res.challenges.length > 0) {
      csvData.push([]); // Empty row as separator
      csvData.push(['CHALLENGES']);
      
      res.challenges.forEach(challenge => {
        const row = [`Challenge: ${challenge.description || challenge.name || challenge.id}`];
        
        // For each team, find their selected option for this challenge
        teams.forEach(team => {
          const teamSelection = res.selectedChallenges?.find(
            sc => sc.selected_challenge_id === challenge.id && sc.selected_team_id === team.id
          );
          
          if (teamSelection) {
            if (teamSelection.selected_option_a) row.push('Option A');
            else if (teamSelection.selected_option_b) row.push('Option B');
            else if (teamSelection.selected_option_c) row.push('Option C');
            else row.push('No selection');
          } else {
            row.push('No selection');
          }
        });
        
        csvData.push(row);
      });
    }
  
    // Initiatives section
    if (res.initiatives && res.initiatives.length > 0) {
      csvData.push([]); // Empty row as separator
      csvData.push(['INITIATIVES']);
      
      res.initiatives.forEach(initiative => {
        const row = [`Initiative: ${initiative.name || initiative.id}`];
        
        // For each team, check if they selected this initiative
        teams.forEach(team => {
          const teamSelection = res.selectedInitiatives?.find(
            si => si.selected_initiative_id === initiative.id && si.selected_team_id === team.id
          );
          
          row.push(teamSelection ? 'Selected' : 'Not selected');
        });
        
        csvData.push(row);
      });
    }
  
    // Decisions section
    if (res.decisionResults && res.decisionResults.length > 0) {
      csvData.push([]); // Empty row as separator
      csvData.push(['DECISIONS']);
      
      // Get the decision scheme to understand structure
      const decisionScheme = res.decisionScheme;
      if (decisionScheme && decisionScheme.pages) {
        // Process each page of decisions
        decisionScheme.pages.forEach((page, pageIndex) => {
          csvData.push([`Decision Page ${pageIndex + 1}`]);
          
          // Process sliders
          if (page.sliders) {
            Object.entries(page.sliders).forEach(([sliderId, sliderData]) => {
              const row = [`Slider: ${sliderData.fieldName || sliderId}`];
              
              // For each team, find their selected value for this slider
              teams.forEach(team => {
                const teamResult = res.decisionResults.find(dr => dr.user_id === team.id);
                if (teamResult && teamResult.selected_values && 
                    teamResult.selected_values.pages && 
                    teamResult.selected_values.pages[pageIndex]) {
                  
                  const selectedValue = teamResult.selected_values.pages[pageIndex].sliders?.[sliderId];
                  if (selectedValue !== undefined && sliderData.labels && sliderData.labels[selectedValue]) {
                    row.push(sliderData.labels[selectedValue].text || `Option ${selectedValue}`);
                  } else {
                    row.push('No selection');
                  }
                } else {
                  row.push('No selection');
                }
              });
              
              csvData.push(row);
            });
          }
          
          // Process incentives
          if (page.incentives && page.incentives.list && page.incentives.list.length > 0) {
            const row = ['Incentives Selected'];
            
            // For each team, find their selected incentives
            teams.forEach(team => {
              const teamResult = res.decisionResults.find(dr => dr.user_id === team.id);
              if (teamResult && teamResult.selected_values && 
                  teamResult.selected_values.pages && 
                  teamResult.selected_values.pages[pageIndex]) {
                
                const selectedIncentive = teamResult.selected_values.pages[pageIndex].selectedIncentive;
                if (selectedIncentive !== null && selectedIncentive !== undefined) {
                  const incentive = page.incentives.list[selectedIncentive];
                  row.push(incentive ? incentive.label : 'Invalid selection');
                } else {
                  row.push('None');
                }
              } else {
                row.push('No selection');
              }
            });
            
            csvData.push(row);
          }
          
          // Add totals for this page
          csvData.push(['FTE Total']);
          csvData.push(['Investment Total']);
        });
        
        // Add overall totals
        csvData.push(['Overall FTE Total']);
        teams.forEach(team => {
          const teamResult = res.decisionResults.find(dr => dr.user_id === team.id);
          csvData[csvData.length - 1].push(teamResult ? teamResult.total_fte || '0' : '0');
        });
        
        csvData.push(['Overall Investment Total']);
        teams.forEach(team => {
          const teamResult = res.decisionResults.find(dr => dr.user_id === team.id);
          csvData[csvData.length - 1].push(teamResult ? teamResult.total_investment || '0' : '0');
        });
      }
    }
  
    // Self Assessment section
    if (res.selfAssessmentAnswers && res.selfAssessmentAnswers.length > 0) {
      csvData.push([]); // Empty row as separator
      csvData.push(['SELF ASSESSMENT']);
      
      // Get the self assessment scheme to understand structure
      const selfAssessmentScheme = res.selfAssessmentScheme;
      
      if (selfAssessmentScheme) {
        // Add normal condition quadrant scores
        csvData.push(['Normal Condition Scores']);
        
        for (let i = 1; i <= 4; i++) {
          const row = [`Quadrant ${i}: ${selfAssessmentScheme[`quadrant_${i}_name`] || ''}`];
          
          teams.forEach(team => {
            const teamAssessment = res.selfAssessmentAnswers.find(sa => sa.team_id === team.id);
            if (teamAssessment) {
              row.push(teamAssessment[`normal_quadrant_${i}_questions`] || 'No data');
            } else {
              row.push('No data');
            }
          });
          
          csvData.push(row);
        }
        
        // Add stress condition quadrant scores
        csvData.push(['Stress Condition Scores']);
        
        for (let i = 1; i <= 4; i++) {
          const row = [`Quadrant ${i}: ${selfAssessmentScheme[`quadrant_${i}_name`] || ''}`];
          
          teams.forEach(team => {
            const teamAssessment = res.selfAssessmentAnswers.find(sa => sa.team_id === team.id);
            if (teamAssessment) {
              row.push(teamAssessment[`stress_quadrant_${i}_questions`] || 'No data');
            } else {
              row.push('No data');
            }
          });
          
          csvData.push(row);
        }
        
        // Add raw answers if available
        if (res.selfAssessmentAnswers.some(sa => sa.raw_answers)) {
          csvData.push(['Detailed Responses']);
          
          // Process normal condition questions
          for (let i = 1; i <= 4; i++) {
            const questionsKey = `normal_quadrant_${i}_questions`;
            const questions = selfAssessmentScheme[questionsKey];
            
            if (questions) {
              try {
                const parsedQuestions = typeof questions === 'string' ? JSON.parse(questions) : questions;
                
                parsedQuestions.forEach((question, qIndex) => {
                  const row = [`Normal Q${i}.${qIndex+1}: ${question}`];
                  
                  teams.forEach(team => {
                    const teamAssessment = res.selfAssessmentAnswers.find(sa => sa.team_id === team.id);
                    if (teamAssessment && teamAssessment.raw_answers) {
                      const rawAnswers = typeof teamAssessment.raw_answers === 'string' 
                        ? JSON.parse(teamAssessment.raw_answers) 
                        : teamAssessment.raw_answers;
                      
                      const answer = rawAnswers[`normal_${i}_${qIndex}`];
                      row.push(answer !== undefined ? answer : 'No response');
                    } else {
                      row.push('No data');
                    }
                  });
                  
                  csvData.push(row);
                });
              } catch (e) {
                console.error('Error parsing questions:', e);
              }
            }
          }
          
          // Process stress condition questions
          for (let i = 1; i <= 4; i++) {
            const questionsKey = `stress_quadrant_${i}_questions`;
            const questions = selfAssessmentScheme[questionsKey];
            
            if (questions) {
              try {
                const parsedQuestions = typeof questions === 'string' ? JSON.parse(questions) : questions;
                
                parsedQuestions.forEach((question, qIndex) => {
                  const row = [`Stress Q${i}.${qIndex+1}: ${question}`];
                  
                  teams.forEach(team => {
                    const teamAssessment = res.selfAssessmentAnswers.find(sa => sa.team_id === team.id);
                    if (teamAssessment && teamAssessment.raw_answers) {
                      const rawAnswers = typeof teamAssessment.raw_answers === 'string' 
                        ? JSON.parse(teamAssessment.raw_answers) 
                        : teamAssessment.raw_answers;
                      
                      const answer = rawAnswers[`stress_${i}_${qIndex}`];
                      row.push(answer !== undefined ? answer : 'No response');
                    } else {
                      row.push('No data');
                    }
                  });
                  
                  csvData.push(row);
                });
              } catch (e) {
                console.error('Error parsing questions:', e);
              }
            }
          }
        }
      }
    }
  
    // Global Team Metrics section
    if (res.globalTeamMetricsSchemes && res.globalTeamMetricsSchemes.length > 0) {
      csvData.push([]); // Empty row as separator
      csvData.push(['GLOBAL TEAM METRICS']);
      
      // Group metrics by name
      const metricsByName = {};
      res.globalTeamMetricsSchemes.forEach(metric => {
        if (!metricsByName[metric.name]) {
          metricsByName[metric.name] = {};
        }
        metricsByName[metric.name][metric.team_id] = metric.value || metric.default_value || '';
      });
      
      // Add each metric as a row
      Object.entries(metricsByName).forEach(([metricName, teamValues]) => {
        const row = [`Metric: ${metricName}`];
        
        teams.forEach(team => {
          row.push(teamValues[team.id] || '');
        });
        
        csvData.push(row);
      });
    }
  
    // Team Goals section
    if (res.teamGoals && res.teamGoals.length > 0) {
      csvData.push([]); // Empty row as separator
      csvData.push(['TEAM GOALS']);
      
      // Create rows for goals 1-3
      for (let i = 1; i <= 3; i++) {
        const row = [`Goal ${i}`];
        
        teams.forEach(team => {
          const teamGoal = res.teamGoals.find(tg => tg.team_id === team.id);
          row.push(teamGoal ? teamGoal[`goal_${i}`] || '' : '');
        });
        
        csvData.push(row);
      }
    }

    // Convert array to CSV string
    const csvString = csvData
      .map(row =>
        row
          .map(cell => {
            // Handle cells that might contain commas or quotes
            if (cell === null || cell === undefined) return '';
            const str = String(cell);
            if (str.includes(',') || str.includes('"') || str.includes('\n')) {
              return `"${str.replace(/"/g, '""')}"`;
            }
            return str;
          })
          .join(',')
      )
      .join('\n');

    // Create blob and download
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    fileDownload(blob, `${name}_export.csv`);

    alert('success', 'Success', 'Client was successfully exported');
  }

  function onGlobalTeamMetricsChange(index, key, value) {
    const updatedGlobalTeamMetricsSchemes = globalTeamMetricsSchemes.map((v, i) => {
      if (i !== index) return v;

      return {
        ...v,
        [key]: value,
      };
    });

    setGlobalTeamMetricsSchemes(updatedGlobalTeamMetricsSchemes);
  }
  console.log({ schemeOrder });

  return (
    <div className="container root-container">
      <BlockUi tag="div" blocking={isLoading}>
        <Row>
          <Col md={10}>
            <PageHeader>{isNewClient ? 'Create New Client' : 'Update client'}</PageHeader>
          </Col>
          <Col md={2}>
            {!isNewClient ? (
              <Button className="btn save-button" onClick={exportClient}>
                Export
              </Button>
            ) : null}
            <Button style={{ marginLeft: '10px' }} className="btn-info save-button" onClick={saveClient}>
              {isNewClient ? 'Create' : 'Save'}
            </Button>
          </Col>
        </Row>

        <form onSubmit={saveClient}>
          <Row>
            <Col md={6}>
              <FormGroup>
                <ControlLabel>Client Name*</ControlLabel>
                <input
                  className="form-control"
                  type="text"
                  name="client-name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter client's name"
                />
              </FormGroup>
            </Col>
          </Row>

          <hr className="margin-top-5 margin-bottom-20" />
          <h3>Schemes</h3>
          <UnifiedList
            tabNames={tabNames}
            schemesData={allSchemeData}
            selectedSchemes={schemes}
            pageToggle={pageToggle}
            setSchemes={setSchemes}
            setPageToggle={setPageToggle}
            setTabNames={setTabNames}
            schemeOrder={schemeOrder}
            onOrderChange={setSchemeOrder}
          />

          <hr className="margin-top-5 margin-bottom-20" />
          <h3>Global Team Metrics</h3>
          <GlobalTeamMetrics globalTeamMetrics={globalTeamMetricsSchemes} onChange={onGlobalTeamMetricsChange} />

          <hr className="margin-top-5 margin-bottom-20" />
          <h3>Decision Limits</h3>
          <Row>
            <Col md={6}>
              <FormGroup>
                <ControlLabel>Maximum Investment</ControlLabel>
                <input
                  className="form-control"
                  type="number"
                  name="investment-max"
                  value={investmentMax}
                  onChange={(e) => setInvestmentMax(e.target.value)}
                  placeholder="Enter maximum investment amount"
                  min="0"
                  step="0.01"
                />
                <small className="text-muted">
                  Set the maximum investment limit for decision-making scenarios (leave empty for no limit)
                </small>
              </FormGroup>
            </Col>
            <Col md={6}>
              <FormGroup>
                <ControlLabel>Maximum FTE</ControlLabel>
                <input
                  className="form-control"
                  type="number"
                  name="fte-max"
                  value={fteMax}
                  onChange={(e) => setFteMax(e.target.value)}
                  placeholder="Enter maximum FTE amount"
                  min="0"
                  step="0.1"
                />
                <small className="text-muted">
                  Set the maximum FTE (Full-Time Equivalent) limit for decision-making scenarios (leave empty for no limit)
                </small>
              </FormGroup>
            </Col>
          </Row>

          <hr className="margin-top-5 margin-bottom-20" />
          <h3>AI Summary Configuration</h3>
          <Row>
            <Col md={6}>
              <FormGroup>
                <ControlLabel>AI Summary Title</ControlLabel>
                <input
                  className="form-control"
                  type="text"
                  name="ai-summary-title"
                  value={aiSummaryTitle}
                  onChange={(e) => setAiSummaryTitle(e.target.value)}
                  placeholder="Enter custom title for AI summaries"
                />
                <small className="text-muted">
                  Set a custom title that will be displayed for AI-generated summaries (leave empty for default)
                </small>
              </FormGroup>
            </Col>
          </Row>

          <hr className="margin-top-5 margin-bottom-20" />
          <h3>Colors</h3>
          <Row>
            <Col md={3}>
              <FormGroup>
                <ControlLabel>Dark Highlight Color</ControlLabel>
                <ColorPicker value={darkHighlightColor} onChange={(e) => setDarkHighlightColor(e)} />
              </FormGroup>
            </Col>

            <Col md={3}>
              <FormGroup>
                <ControlLabel>Light Highlight Color</ControlLabel>
                <ColorPicker value={lightHighlightColor} onChange={(e) => setLightHighlightColor(e)} />
              </FormGroup>
            </Col>
          </Row>

          <hr className="margin-top-5 margin-bottom-20" />
          <Row>
            <Col md={2}>
              <FormGroup>
                <ControlLabel>Sign-up</ControlLabel>

                <ButtonGroup block>
                  <Button bsStyle={isSignUpEnabled ? 'success' : 'default'} onClick={() => setSignUpEnabled(true)}>
                    Active
                  </Button>
                  <Button bsStyle={!isSignUpEnabled ? 'success' : 'default'} onClick={() => setSignUpEnabled(false)}>
                    Inactive
                  </Button>
                </ButtonGroup>
              </FormGroup>
            </Col>

            <Col md={4}>
              <FormGroup>
                <ControlLabel>Client Email Domain</ControlLabel>
                <input
                  className="form-control"
                  type="text"
                  name="client-email-domain"
                  value={signUpEmailDomain}
                  onChange={(e) => setSignUpEmailDomain(e.target.value)}
                  placeholder="Enter client's email domain"
                />
              </FormGroup>
            </Col>

            <Col md={6}>
              <FormGroup>
                <ControlLabel>Workshop Image</ControlLabel>

                {workshopImage ? (
                  <div className={styles.preview}>
                    <img src={workshopImage} alt="logo" className={cn('img-responsive', styles.img)} />

                    <Button bsStyle="danger" className={styles.button} onClick={() => removeImage('workshop')}>
                      Remove workshop image
                    </Button>
                  </div>
                ) : null}

                <input
                  id="workshop"
                  className="form-control"
                  type="file"
                  name="workshop"
                  onChange={() => setImage('workshop')}
                />
              </FormGroup>
            </Col>
          </Row>

          <hr className="margin-top-5 margin-bottom-20" />
          <h3>Logo & background image</h3>
          <Row>
            <Col md={6}>
              <FormGroup>
                <ControlLabel>Background Image</ControlLabel>

                {backgroundImage ? (
                  <div className={styles.preview}>
                    <img src={backgroundImage} alt="background" className={cn('img-responsive', styles.img)} />

                    <Button bsStyle="danger" className={styles.button} onClick={() => removeImage()}>
                      Remove background image
                    </Button>
                  </div>
                ) : null}

                <input
                  id="background-image"
                  className="form-control"
                  type="file"
                  name="background-image"
                  onChange={() => setImage()}
                />
              </FormGroup>
            </Col>

            <Col md={6}>
              <FormGroup>
                <ControlLabel>Logo</ControlLabel>

                {logoImage ? (
                  <div className={styles.preview}>
                    <img src={logoImage} alt="logo" className={cn('img-responsive', styles.img)} />

                    <Button bsStyle="danger" className={styles.button} onClick={() => removeImage('logo')}>
                      Remove logo image
                    </Button>
                  </div>
                ) : null}

                <input id="logo" className="form-control" type="file" name="logo" onChange={() => setImage('logo')} />
              </FormGroup>
            </Col>
          </Row>
        </form>
      </BlockUi>
    </div>
  );
}

// const data = {
//   somekey: 'somevalue',
//   quadrant_1_questions: {
//     1: 'Olololo?',
//     2: 'Trolololo?',
//   },
//   quadrant_2_questions: {
//     1: 'Olololo?',
//     2: 'Trolololo?',
//   },
//   quadrant_3_questions: {
//     1: 'Olololo?',
//     2: 'Trolololo?',
//   },
// };
