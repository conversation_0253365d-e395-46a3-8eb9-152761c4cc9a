import React, { useEffect, useMemo, useState } from 'react';
import { useTheme } from '@/components/ThemeProvider/ThemeProvider';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  PieC<PERSON>,
  <PERSON>,
  Cell,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  LabelList,
  Label,
} from 'recharts';
import { formatCurrency, formatCurrencyAbbreviated } from '../../utils/formatters';
import { analyzePNL, analyzeTable, createDecisionResult } from '../../actions/user';
import to from 'await-to-js';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import DecisionPagePNL from './DecisionPagePNL';
import DecisionPageCharts from './DecisionPageCharts';

const CHARTS = {
  BAR: 'BAR',
  WATERFALL: 'WATERFALL',
  PIE: 'PIE',
};

const getChartColors = (client) => {
  const darkColor = client?.darkHighlightColor || '#004864';
  const lightColor = client?.lightHighlightColor || '#00A3E3';
  return [lightColor, darkColor, '#2ecc71', '#e74c3c', '#f1c40f', '#9b59b6'];
};

const DecisionPNL = ({ data, type, client, userSelections, user, pages, decisions, formData, calculatePageTotals, savedDecisions }) => {
  const { theme } = useTheme();
  const [aiAnalyses, setAiAnalyses] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  // console.log({ aiAnalyses, savedDecisions });

  // Add safety check for data
  if (!data?.data || !Array.isArray(data.data) || data.data.length === 0) {
    return (
      <div className="p-8">
        <h2 className={`text-2xl font-semibold mb-6 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>Profit & Loss Statement</h2>
        <div className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>No PNL data available</div>
      </div>
    );
  }
  const clientId = useMemo(() => localStorage.getItem('clientId'), []);

  // Restore AI analysis from saved decisions when component loads
  useEffect(() => {
    if (savedDecisions?.ai_analysis) {
      const aiAnalysis = savedDecisions.ai_analysis;

      // Check if it's the new format with allAnalyses
      if (aiAnalysis?.allAnalyses && Array.isArray(aiAnalysis.allAnalyses)) {
        setAiAnalyses(aiAnalysis.allAnalyses);
      }
      // Check if it's the old format with mainAnalysis (backward compatibility)
      else if (aiAnalysis?.mainAnalysis) {
        setAiAnalyses([aiAnalysis.mainAnalysis]);
      }
      // Handle legacy format where ai_analysis was a single analysis object
      else if (aiAnalysis?.analysis) {
        setAiAnalyses([aiAnalysis]);
      }
    }
  }, [savedDecisions]);

  const handleRegenerateAnalysis = async () => {
    setIsAnalyzing(true);

    // Collect all PNL data: main PNL + page PNLs
    const pnlDataArray = [data.data];

    // Add page PNLs if they exist
    if (pages?.length > 0) {
      pages.forEach(page => {
        if (page.page_pnl?.data) {
          pnlDataArray.push(page.page_pnl.data);
        }
      });
    }

    const [err, res] = await to(
      analyzePNL(pnlDataArray, {
        totalFTE: userSelections.totalFTE,
        totalInvestment: userSelections.totalInvestment,
      }, client?.decisionSchemeId, pages).payload
    );

    if (err) {
      toast.error('Failed to analyze P&L');
      setIsAnalyzing(false);
      return;
    }

    // The response now contains analyses array instead of single analysis
    const analyses = res.data.analyses;
    if (!analyses || !Array.isArray(analyses) || analyses.length === 0) {
      toast.error('Invalid analysis response');
      setIsAnalyzing(false);
      return;
    }

    // Make the second API call to analyze the table and get key metrics for the main PNL
    const mainAnalysis = analyses[0];
    const [metricsErr, metricsRes] = await to(analyzeTable(mainAnalysis.updatedPnL).payload);

    if (metricsErr) {
      toast.error('Failed to analyze key metrics');
      setIsAnalyzing(false);
      return;
    }

    // Combine main analysis with key metrics
    const enhancedAnalyses = analyses.map((analysis, index) => ({
      ...analysis,
      keyMetrics: index === 0 ? metricsRes.data.keyMetrics : null, // Only main PNL gets key metrics
    }));

    setAiAnalyses(enhancedAnalyses);

    const [storeErr] = await to(
      createDecisionResult({
        userId: user?.user?.id,
        clientId: clientId,
        decisionSchemeId: client?.decisionSchemeId,
        selectedValues: {
          pnlData: data.data,
          ...userSelections,
        },
        totalFTE: userSelections?.totalFTE,
        totalInvestment: userSelections?.totalInvestment,
        aiAnalysis: {
          // Store all analyses in the aiAnalysis field for database compatibility
          mainAnalysis: enhancedAnalyses[0], // Main analysis for backward compatibility
          allAnalyses: enhancedAnalyses, // All analyses including per-page ones
        },
      }).payload
    );

    if (storeErr) {
      console.error('Failed to store decision results:', storeErr);
      toast.error('Failed to save your decisions');
    }

    setIsAnalyzing(false);
  };

  const renderSummarySection = () => (
    <div className={`mb-6 p-6 rounded-lg ${theme === 'dark' ? 'bg-blue-900/30' : 'bg-blue-100/70'}`}>
      <h3 className={`text-xl font-medium mb-3 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>{client?.aiSummaryTitle || 'AI Analysis Summary'}</h3>
      <div className={`space-y-4 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
        {isAnalyzing ? (
          renderSummarySkeleton()
        ) : (
          <>
            {aiAnalyses?.[0]?.analysis ? (
              <>
                <p className='hidden'>{aiAnalyses[0].analysis}</p>

                <button
                  onClick={handleRegenerateAnalysis}
                  disabled={isAnalyzing}
                  className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 disabled:cursor-not-allowed text-white rounded-md transition-colors"
                >
                  {isAnalyzing ? 'Regenerating...' : 'Regenerate Analysis'}
                </button>
              </>
            ) : (
              <button
                onClick={handleRegenerateAnalysis}
                disabled={isAnalyzing}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 disabled:cursor-not-allowed text-white rounded-md transition-colors"
              >
                {isAnalyzing ? 'Generating...' : 'Generate Analysis'}
              </button>
            )}
            <div className="mt-4 hidden">
              <h4 className={`text-lg font-medium mb-2 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>Impact Overview:</h4>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Investment Impact: ${userSelections?.totalInvestment?.toLocaleString()}</li>
                <li>FTE Change: {userSelections?.totalFTE} FTEs</li>
                <li>
                  Final Adjusted PNL:{' '}
                  {aiAnalyses?.[0]
                    ? formatCurrency(parseFloat(aiAnalyses[0].updatedPnL[aiAnalyses[0].updatedPnL.length - 2][6]))
                    : 'N/A'}
                </li>
              </ul>
            </div>
          </>
        )}
      </div>
    </div>
  );

  const renderTableAndChartsSkeleton = () => (
    <div className="space-y-8 animate-pulse">
      {/* Table skeleton */}
      <div className={`${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'} p-4 rounded-lg`}>
        <div className="h-8 bg-gray-700 rounded w-1/4 mb-6"></div>
        <div className="space-y-4">
          {Array(8)
            .fill(0)
            .map((_, i) => (
              <div key={i} className="flex space-x-4">
                {Array(6)
                  .fill(0)
                  .map((_, j) => (
                    <div key={j} className="h-4 bg-gray-700 rounded flex-1"></div>
                  ))}
              </div>
            ))}
        </div>
      </div>

      {/* Charts skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
        <div className={`${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'} p-4 rounded-lg`}>
          <div className="h-6 bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="h-[300px] bg-gray-700/50 rounded"></div>
        </div>
      </div>
    </div>
  );

  const CHART_COLORS = getChartColors(client);
  const { charts = [] } = data;
  const cleanData = data.data.filter((row) => Array.isArray(row) && row.length > 1);

  // Safety check for cleanData
  if (cleanData.length === 0) {
    return (
      <div className="p-8">
        <h2 className={`text-2xl font-semibold mb-6 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>Profit & Loss Statement</h2>
        <div className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>Invalid PNL data format</div>
      </div>
    );
  }

  // Check if AI analysis preserves original structure or creates new structure
  const aiData = aiAnalyses?.[0]?.updatedPnL;
  const hasValidAiData = aiData && Array.isArray(aiData) && aiData.length > 0;

  // Determine if AI preserved original structure by checking if first column matches
  const aiPreservedStructure = hasValidAiData &&
    Array.isArray(aiData[0]) &&
    aiData[0].length > 0 &&
    (aiData[0][0] === cleanData[0][0] || aiData[0][0].toLowerCase().includes('category'));

  // Use appropriate headers and rows based on AI structure
  const headers = hasValidAiData && aiPreservedStructure
    ? aiData[0] // Use AI headers if structure is preserved
    : [...cleanData[0], 'Adjusted PNL']; // Use original headers with added column

  const rows = hasValidAiData && aiPreservedStructure
    ? aiData.slice(1).filter(row => Array.isArray(row)) // Use AI data if structure is preserved
    : cleanData.slice(1).map((row) => [...row, '0']); // Fallback to original data with placeholder

  // Transform data for charts
  const prepareChartData = () => {
    const valueColumnIndex = headers.findIndex((header, index) => {
      if (index === 0) return false; // Skip the label column
      // Check if this column has numeric values
      return rows.some((row) => !isNaN(parseFloat(row[index])) && isFinite(row[index]));
    });

    if (valueColumnIndex === -1) return [];

    const numericData = rows
      .map((row) => ({
        name: row[0],
        value: parseFloat(row[valueColumnIndex]) || 0,
      }))
      .filter((item) => !isNaN(item.value));

    return numericData.sort((a, b) => Math.abs(b.value) - Math.abs(a.value));
  };

  const chartData = prepareChartData();

  const renderCharts = () => {
    if (!aiAnalyses?.[0]?.keyMetrics) return null;

    const chartColors = {
      original: 'hsl(var(--chart-2))',
      updated: 'hsl(var(--chart-1))',
    };

    const metrics = Object.entries(aiAnalyses[0].keyMetrics).map(([key, values]) => ({
      name: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
      original: Math.abs(values.original),
      updated: Math.abs(values.updated),
      difference: Math.abs(values.updated - values.original),
    }));

    return (
      <div className="mt-8 mb-8 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {metrics.map((metric) => (
          <div key={metric.name} className={`p-4 rounded-lg ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <h3 className={`text-lg font-medium mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>{metric.name}</h3>
            <div className="space-y-6">
              {/* Stack Chart */}
              <div className="h-[240px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={[metric]} barSize={40}>
                    <XAxis dataKey="name" stroke="#fff" />
                    <YAxis
                      stroke="#fff"
                      tickFormatter={(value) => formatCurrencyAbbreviated(value)}
                    />
                    <Tooltip
                      formatter={(value, name) => [
                        formatCurrencyAbbreviated(value),
                        name === 'difference' ? 'Updated' : 'Original',
                      ]}
                      contentStyle={{
                        backgroundColor: '#1a1a1a',
                        border: 'none',
                        color: '#fff',
                      }}
                      labelStyle={{ color: '#fff' }}
                      cursor={false}
                    />
                    <Bar dataKey="original" stackId="a" fill={chartColors.original} />
                    <Bar dataKey="difference" stackId="a" fill={chartColors.updated} />
                  </BarChart>
                </ResponsiveContainer>
              </div>

              {/* Ring Chart */}
              {/* <div className="h-[150px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Original', value: metric.original },
                        { name: 'Updated', value: metric.difference },
                      ]}
                      dataKey="value"
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={60}
                      startAngle={90}
                      endAngle={-270}
                    >
                      {[chartColors.original, chartColors.updated].map((color, index) => (
                        <Cell key={`cell-${index}`} fill={color} />
                      ))}
                      <Label
                        value={`${((metric.updated / metric.original) * 100).toFixed(0)}%`}
                        position="center"
                        fill="#fff"
                      />
                    </Pie>
                    <Tooltip
                      formatter={(value, name) => [formatCurrency(value), name]}
                      contentStyle={{
                        backgroundColor: '#fff',
                        border: 'none',
                        color: '#fff',
                      }}
                      labelStyle={{ color: '#fff' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div> */}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderTableSkeleton = () => (
    <div className="animate-pulse">
      {Array(8)
        .fill(0)
        .map((_, index) => (
          <tr key={index} className="border-t border-gray-700">
            {Array(7)
              .fill(0)
              .map((_, cellIndex) => (
                <td key={cellIndex} className="px-6 py-4 whitespace-nowrap">
                  <div className="h-4 bg-gray-700 rounded w-24"></div>
                </td>
              ))}
          </tr>
        ))}
    </div>
  );

  const renderSummarySkeleton = () => (
    <div className="animate-pulse space-y-4">
      <div className="h-4 bg-gray-700 rounded w-3/4"></div>
      <div className="h-4 bg-gray-700 rounded w-2/3"></div>
      <div className="h-4 bg-gray-700 rounded w-1/2"></div>
      <div className="space-y-2 mt-4">
        <div className="h-4 bg-gray-700 rounded w-1/4"></div>
        <div className="h-4 bg-gray-700 rounded w-1/3"></div>
        <div className="h-4 bg-gray-700 rounded w-1/4"></div>
      </div>
    </div>
  );

  // Helper function to check if a row should be highlighted
  const isKeyPNLRow = (rowName) => {
    if (!rowName || typeof rowName !== 'string') return false;
    const name = rowName.toLowerCase().trim();
    return (
      name.includes('revenue') ||
      name.includes('gross margin') ||
      name.includes('operating profit') ||
      name.includes('gross profit') ||
      name === 'revenue' ||
      name === 'gross margin' ||
      name === 'operating profit' ||
      name === 'gross profit'
    );
  };

  // Modify the cell rendering logic to include safety checks and better currency formatting
  const renderCell = (cell, cellIndex) => {
    const header = headers[cellIndex];
    if (!header) return cell;

    const headerLower = header.toLowerCase();

    // Check if this column should be formatted as currency
    const shouldFormatAsCurrency =  !isNaN(parseFloat(cell)) && isFinite(cell);

    const formattedValue = shouldFormatAsCurrency
      ? formatCurrency(parseFloat(cell))
      : cell;

    return formattedValue;
  };

  return (
    <div>
      {/* <h2 className="text-2xl font-semibold text-white mb-6">Profit & Loss Statement</h2> */}

      {/* Decision Page Charts */}
      {decisions && formData && calculatePageTotals && (
        <DecisionPageCharts
          decisions={decisions}
          formData={formData}
          calculatePageTotals={calculatePageTotals}
          client={client}
        />
      )}

      {renderSummarySection()}

      {isAnalyzing ? (
        renderTableAndChartsSkeleton()
      ) : aiAnalyses?.[0] ? (
        <>
          {/* Render table only when AI analysis is available */}
          <div className={`overflow-hidden ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <table className="w-full mb-8">
              <thead>
                <tr className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-200'}`}>
                  {headers.map((header, index) => (
                    <th
                      key={index}
                      style={{
                        borderTopLeftRadius: index === 0 ? '8px' : '0',
                        borderTopRightRadius: index === headers.length - 1 ? '8px' : '0',
                        borderBottomLeftRadius: 0,
                        borderBottomRightRadius: 0,
                      }}
                      className={`py-3 px-4 ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'} ${index === 0 ? 'text-left' : 'text-right'}`}
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {rows.filter(row => Array.isArray(row)).map((row, rowIndex) => {
                  const isKeyRow = isKeyPNLRow(row[0]);
                  const baseClasses = `border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-300'}`;
                  const hoverClasses = theme === 'dark' ? 'hover:bg-gray-800/30' : 'hover:bg-gray-100/50';
                  const highlightClasses = isKeyRow
                    ? theme === 'dark'
                      ? 'bg-gray-600/70 hover:bg-gray-600/30'
                      : 'bg-blue-50 hover:bg-blue-100/70'
                    : hoverClasses;

                  return (
                    <tr key={rowIndex} className={`${baseClasses} ${highlightClasses}`}>
                    {row.map((cell, cellIndex) => {
                      const formattedValue = renderCell(cell, cellIndex);
                      const header = headers[cellIndex];
                      const headerLower = header ? header.toLowerCase() : '';

                      return (
                        <td
                          key={cellIndex}
                          className={`py-3 px-4 rounded-none ${cellIndex === 0 ? 'text-left' : 'text-right'} ${
                            headerLower.includes('pnl') || headerLower.includes('amount')
                              ? parseFloat(cell) < 0
                                ? 'text-red-400'
                                : parseFloat(cell) > 0
                                ? 'text-green-400'
                                : theme === 'dark' ? 'text-gray-200' : 'text-gray-700'
                              : theme === 'dark' ? 'text-gray-200' : 'text-gray-700'
                          }`}
                        >
                          {formattedValue}
                        </td>
                      );
                    })}
                  </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          {/* Render charts only when AI analysis is available */}
          {renderCharts()}
        </>
      ) : (
        <div className={`text-center py-8 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>Waiting for AI analysis...</div>
      )}

      {pages?.some((page) => page.page_pnl?.data) && (
        <div className="mt-12">
          <h2 className={`text-2xl font-semibold mb-6 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>P&L Statements</h2>
          {pages.map(
            (page, index) => {
              const pageAnalysis = aiAnalyses?.[index + 1];
              // console.log(`Page ${index} analysis:`, pageAnalysis);
              return page.page_pnl && (
                <div key={index} className="mb-8">
                  <DecisionPagePNL
                    data={page.page_pnl}
                    label={`${page.page_name} P&L` || `Page ${index + 1} P&L`}
                    aiAnalysis={pageAnalysis} // Page analyses start from index 1
                    client={client}
                  />
                </div>
              );
            }
          )}
        </div>
      )}
    </div>
  );
};

export default DecisionPNL;
