import React from 'react';
import { formatCurrency } from '../../utils/formatters';

const DecisionIncentives = ({ incentives, selectedIncentives, onIncentiveChange, client }) => {
  const lightHighlightColor = client?.lightHighlightColor || '#00A3E3';
  const darkHighlightColor = client?.darkHighlightColor || '#004864';

  // Get the number of dropdowns to render based on optionLimit
  const optionLimit = incentives?.optionLimit || 1;

  const handleDropdownChange = (dropdownIndex, event) => {
    const selectedLabel = event.target.value;
    const selected = selectedLabel ? incentives.list.find((incentive) => incentive.label === selectedLabel) : null;
    onIncentiveChange(dropdownIndex, selected);
  };

  return (
    <div className="space-y-4">
      {/* Render separate card for each initiative */}
      {Array.from({ length: optionLimit }, (_, index) => {
        const selectedIncentive = selectedIncentives?.[index];

        return (
          <div key={index} className="bg-white/5 p-6 rounded-lg" style={{ borderColor: lightHighlightColor }}>
            <h3 className="text-lg font-medium mb-4" style={{ color: lightHighlightColor }}>
              Initiative {index + 1}
            </h3>
            <div className="space-y-4">
              {/* Dropdown */}
              <select
                className="w-full p-3 rounded border focus:outline-none focus:ring-2"
                style={{
                  backgroundColor: 'rgba(107, 114, 128, 0.5)',
                  borderColor: lightHighlightColor,
                  color: 'white',
                  '--tw-ring-color': darkHighlightColor,
                }}
                onChange={(event) => handleDropdownChange(index, event)}
                value={selectedIncentive?.label || ''}
              >
                <option value="">Select an Initiative</option>
                {incentives.list.map((incentive, incentiveIndex) => (
                  <option key={incentiveIndex} value={incentive.label}>
                    {incentive.label}
                  </option>
                ))}
              </select>

              {/* FTE and Investment display for this specific initiative */}
              {selectedIncentive && (
                <div className="space-y-2">
                  <div className="p-2 rounded" style={{ backgroundColor: `${darkHighlightColor}33` }}>
                    <p style={{ color: lightHighlightColor }}>FTEs: {selectedIncentive.fte}</p>
                  </div>
                  <div className="p-2 rounded" style={{ backgroundColor: `${darkHighlightColor}33` }}>
                    <p style={{ color: lightHighlightColor }}>Investment: {formatCurrency(selectedIncentive.investment)}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default DecisionIncentives;
