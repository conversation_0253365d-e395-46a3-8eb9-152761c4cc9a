import React, { useState } from 'react';
import { Row, Col, FormGroup, ControlLabel, Button } from 'react-bootstrap';

export default function SliderConfigItem({ itemData, index, onConfigChange, handleDelete }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { id, config } = itemData;

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleChange = (configType, value) => {
    onConfigChange(id, configType, value);
  };

  const handleLabelChange = (index, value) => {
    const newLabels = [...(config.labels || ['Low', 'Medium', 'High'])];
    newLabels[index] = {
      ...newLabels[index],
      text: value,
    };
    handleChange('labels', newLabels);
  };

  const handleReflectionChange = (index, cell, value) => {
    const newLabels = [...(config.labels || ['Low', 'Medium', 'High'])];
    newLabels[index] = {
      ...newLabels[index],
      [cell]: value,
    };
    handleChange('labels', newLabels);
  };

  const addLabel = () => {
    const currentLabels = [...(config.labels || ['Low', 'Medium', 'High'])];
    if (currentLabels.length < 5) {
      currentLabels.push({ text: '', reflectionA: '', reflectionB: '' });
      handleChange('labels', currentLabels);
    }
  };

  // Convert string labels to object format if needed
  const normalizedLabels = (config.labels || ['Low', 'Medium', 'High']).map((label) =>
    typeof label === 'string' ? { text: label, reflectionA: '', reflectionB: '' } : label
  );

  return (
    <Row>
      <Col md={12}>
        <FormGroup>
          <div
            style={{
              border: '1px solid #ddd',
              borderRadius: '4px',
              marginBottom: '10px',
            }}
          >
            {/* Header section - always visible */}
            <div
              style={{
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderBottom: isExpanded ? '1px solid #ddd' : 'none',
                borderTopLeftRadius: '4px',
                borderTopRightRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                <span style={{ marginRight: '10px', color: '#666' }}>{index + 1}.</span>
                <input
                  type="text"
                  className="form-control"
                  value={config.fieldName || ''}
                  onChange={(e) => handleChange('fieldName', e.target.value)}
                  placeholder="Enter field name"
                  style={{ maxWidth: '60%' }}
                />
                {config.group && (
                  <span style={{
                    marginLeft: '10px',
                    padding: '4px 8px',
                    backgroundColor: '#dc7676',
                    color: 'white',
                    borderRadius: '4px',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}>
                    Group: {config.group}
                  </span>
                )}
              </div>

              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Button bsStyle="link" onClick={toggleExpand} style={{ padding: '6px', marginRight: '10px' }}>
                  <i className={`glyphicon glyphicon-chevron-${isExpanded ? 'up' : 'down'}`} />
                </Button>
                <Button bsStyle="link" onClick={handleDelete} style={{ padding: '6px', color: '#d9534f' }}>
                  <i className="glyphicon glyphicon-remove" />
                </Button>
              </div>
            </div>

            {/* Collapsible content */}
            <div
              style={{
                display: isExpanded ? 'block' : 'none',
                padding: '15px',
                backgroundColor: 'white',
              }}
            >
              <div style={{ margin: '10px 0px', width: '100%' }}>
                <p style={{ margin: '8px 0px', color: '#666', fontWeight: 'bold' }}>Brief Description (optional)</p>
                <textarea
                  className="form-control"
                  value={config.description || ''}
                  onChange={(e) => handleChange('description', e.target.value)}
                  placeholder="Enter field description (optional)"
                  rows={2}
                  style={{ resize: 'vertical' }}
                />
              </div>
              {normalizedLabels.map((label, i) => (
                <Row key={i} style={{ marginBottom: i < normalizedLabels.length - 1 ? '15px' : '0' }}>
                  <Col md={4}>
                    <FormGroup style={{ marginBottom: '0' }}>
                      <ControlLabel>Label {i + 1}</ControlLabel>
                      <input
                        type="text"
                        className="form-control"
                        value={label.text}
                        onChange={(e) => handleLabelChange(i, e.target.value)}
                        placeholder={`Enter label ${i + 1}`}
                      />
                    </FormGroup>
                  </Col>
                  <Col md={4}>
                    <FormGroup style={{ marginBottom: '0' }}>
                      <ControlLabel>FTEs</ControlLabel>
                      <input
                        type="text"
                        className="form-control"
                        value={label.fte}
                        onChange={(e) => handleReflectionChange(i, 'fte', e.target.value)}
                        placeholder="Enter FTE value"
                      />
                    </FormGroup>
                  </Col>
                  <Col md={4}>
                    <FormGroup style={{ marginBottom: '0' }}>
                      <ControlLabel>Investment</ControlLabel>
                      <input
                        type="text"
                        className="form-control"
                        value={label.investment}
                        onChange={(e) => handleReflectionChange(i, 'investment', e.target.value)}
                        placeholder="Enter investment value"
                      />
                    </FormGroup>
                  </Col>
                </Row>
              ))}

              <Button onClick={addLabel} disabled={normalizedLabels.length >= 5} style={{ marginTop: '15px' }}>
                Add Label
              </Button>
            </div>
          </div>
        </FormGroup>
      </Col>
    </Row>
  );
}
