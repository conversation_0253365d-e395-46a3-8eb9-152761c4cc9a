.previewContainer {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.sliderTitle {
  font-weight: bold;
  margin-bottom: 15px;
}

.contentRow {
  display: flex;
  align-items: center;
  gap: 20px;

  @media (max-width: 600px) {
    flex-direction: column;
    align-items: stretch;
  }
}

.sliderSection {
  flex: 2;
  min-width: 0; // Prevents flex item from overflowing
  padding: 0px 1em;
}

.sliderContainer {
  position: relative;
  padding: 20px 0 30px;
  user-select: none;
}

.sliderTrack {
  position: relative;
  height: 4px;
  background-color: #e0e0e0;
  border-radius: 2px;
  cursor: pointer;
}

.sliderProgress {
  position: absolute;
  height: 100%;
  background-color: #dc7676;
  border-radius: 2px;
  pointer-events: none;
}

.sliderHandle {
  position: absolute;
  top: 50%;
  width: 20px;
  height: 20px;
  background-color: #dc7676;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: grab;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.labelContainer {
  position: relative;
  margin-top: 10px;
  height: 20px;
}

.label {
  position: absolute;
  transform: translateX(-50%);
  font-size: 14px;
  color: #666;
  transition: color 0.2s ease;

  &.activeLabel {
    color: #dc7676;
    font-weight: bold;
  }
}

.reflectionSection {
  display: flex;
  gap: 10px;
  flex-direction: column;
}

.reflectionCell {
  flex: 1;
  background-color: #dc7676;
  padding: 8px 12px;
  border-radius: 4px;
}

.reflectionContent {
  font-size: 14px;
  color: white;
  white-space: nowrap;
}
