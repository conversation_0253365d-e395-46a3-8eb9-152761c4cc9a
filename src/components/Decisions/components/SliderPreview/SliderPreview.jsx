import React, { useState, useRef, useEffect } from 'react';
import { formatCurrency } from '../../../../utils/formatters';
import styles from './slider-preview.module.scss';

const SliderPreview = ({ config }) => {
  const { fieldName, labels } = config;
  const [currentPosition, setCurrentPosition] = useState(1); // Default to middle position
  const [isDragging, setIsDragging] = useState(false);
  const sliderRef = useRef(null);
  const normalizedLabels = labels.map((label) => (typeof label === 'string' ? { text: label } : label));

  const calculatePosition = (clientX) => {
    const track = sliderRef.current;
    if (!track) return 0;

    const trackRect = track.getBoundingClientRect();
    const relativeX = clientX - trackRect.left;
    const percentage = Math.max(0, Math.min(1, relativeX / trackRect.width));

    // Calculate nearest label position
    const labelCount = normalizedLabels.length;
    const stepSize = 1 / (labelCount - 1);
    const nearestStep = Math.round(percentage / stepSize);
    return Math.max(0, Math.min(labelCount - 1, nearestStep));
  };

  const handleMouseDown = (e) => {
    setIsDragging(true);
    const position = calculatePosition(e.clientX);
    setCurrentPosition(position);
    e.preventDefault(); // Prevent text selection while dragging
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    const position = calculatePosition(e.clientX);
    setCurrentPosition(position);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  const currentLabel = normalizedLabels[currentPosition];
  const handlePosition = `${(currentPosition / (normalizedLabels.length - 1)) * 100}%`;

  return (
    <div className={styles.previewContainer}>
      <div className={styles.sliderTitle}>{fieldName}</div>
      <div className={styles.contentRow}>
        <div className={styles.sliderSection}>
          <div className={styles.sliderContainer}>
            <div ref={sliderRef} className={styles.sliderTrack} onMouseDown={handleMouseDown}>
              <div className={styles.sliderHandle} style={{ left: handlePosition }} />
              <div className={styles.sliderProgress} style={{ width: handlePosition }} />
            </div>
            <div className={styles.labelContainer}>
              {normalizedLabels.map((label, index) => (
                <span
                  key={index}
                  className={`${styles.label} ${index === currentPosition ? styles.activeLabel : ''}`}
                  style={{
                    left: `${(index / (normalizedLabels.length - 1)) * 100}%`,
                  }}
                >
                  {label.text}
                </span>
              ))}
            </div>
          </div>
        </div>
        {(currentLabel?.fte || currentLabel?.investment) && (
          <div className={styles.reflectionSection}>
            <div className={styles.reflectionCell}>
              <div className={styles.reflectionContent}>FTEs: {currentLabel?.fte || ''}</div>
            </div>
            <div className={styles.reflectionCell}>
              <div className={styles.reflectionContent}>Investment: {formatCurrency(currentLabel?.investment)}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SliderPreview;
