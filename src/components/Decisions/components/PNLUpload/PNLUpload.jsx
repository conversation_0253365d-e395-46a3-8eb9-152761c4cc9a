import React, { useState } from 'react';
import { Row, Col, FormGroup, ControlLabel, Button, Alert, Checkbox } from 'react-bootstrap';
import { CHARTS } from '../../constants';

const PNLUpload = ({ pnlData, onChange, label }) => {
  const [error, setError] = useState(null);
  const uploadId = `pnl-upload-${Math.random().toString(36).substr(2, 9)}`;

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (file.type !== 'text/csv') {
      setError('Please upload a CSV file');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csvContent = e.target.result;
        
        // Parse CSV with proper handling of quoted values
        const parseCSV = (text) => {
          const result = [];
          const lines = text.split(/\r?\n/);
          
          lines.forEach(line => {
            if (!line.trim()) return; // Skip empty lines
            
            const row = [];
            let inQuotes = false;
            let currentValue = '';
            
            for (let i = 0; i < line.length; i++) {
              const char = line[i];
              
              if (char === '"') {
                inQuotes = !inQuotes;
                // Add the quote for proper processing later
                currentValue += char;
              } else if (char === ',' && !inQuotes) {
                // End of field
                row.push(currentValue);
                currentValue = '';
              } else {
                // Regular character
                currentValue += char;
              }
            }
            
            // Add the last field
            if (currentValue) {
              row.push(currentValue);
            }
            
            // Process quoted values
            const processedRow = row.map(cell => {
              // If it's a quoted value, remove quotes and handle numeric values
              if (cell.startsWith('"') && cell.endsWith('"')) {
                return cell.substring(1, cell.length - 1);
              }
              return cell;
            });
            
            result.push(processedRow);
          });
          
          return result;
        };
        
        const rows = parseCSV(csvContent);

        onChange({
          data: rows,
          charts: [],
        });
        setError(null);
      } catch (err) {
        setError('Error parsing CSV file');
        console.error('CSV parsing error:', err);
      }
    };

    reader.onerror = () => {
      setError('Error reading file');
    };

    reader.readAsText(file);
  };

  const handleRemoveFile = () => {
    onChange({
      data: null,
      charts: [],
    });
  };

  const handleChartSelection = (chartType) => {
    const currentCharts = pnlData?.charts || [];
    const newCharts = currentCharts.includes(chartType)
      ? currentCharts.filter((c) => c !== chartType)
      : [...currentCharts, chartType];

    onChange({
      ...pnlData,
      charts: newCharts,
    });
  };

  const renderChartOptions = () => (
    <div style={{ marginTop: '15px', marginBottom: '15px' }}>
      <ControlLabel>Select Charts to Display:</ControlLabel>
      <div style={{ marginTop: '10px' }}>
        <Checkbox
          checked={pnlData?.charts?.includes(CHARTS.BAR)}
          onChange={() => handleChartSelection(CHARTS.BAR)}
          inline
        >
          Bar Chart
        </Checkbox>
        {/* <Checkbox
          checked={pnlData?.charts?.includes(CHARTS.WATERFALL)}
          onChange={() => handleChartSelection(CHARTS.WATERFALL)}
          inline
        >
          Waterfall Chart
        </Checkbox> */}
        <Checkbox
          checked={pnlData?.charts?.includes(CHARTS.PIE)}
          onChange={() => handleChartSelection(CHARTS.PIE)}
          inline
        >
          Pie Chart
        </Checkbox>
      </div>
    </div>
  );

  return (
    <div
      style={{
        border: '1px solid #ddd',
        borderRadius: '4px',
        padding: '15px',
        marginBottom: '15px',
      }}
    >
      <Row>
        <Col md={12}>
          <FormGroup>
            <ControlLabel>{label || 'P&L Data'}</ControlLabel>
            {error && (
              <Alert bsStyle="danger" onDismiss={() => setError(null)}>
                {error}
              </Alert>
            )}

            {!pnlData?.data ? (
              <div style={{ marginTop: '10px' }}>
                <input
                  type="file"
                  accept=".csv"
                  onChange={handleFileUpload}
                  style={{ display: 'none' }}
                  id={uploadId}
                />
                <label htmlFor={uploadId}>
                  <Button bsStyle="primary" onClick={() => document.getElementById(uploadId).click()}>
                    Upload P&L CSV
                  </Button>
                </label>
              </div>
            ) : (
              <div style={{ marginTop: '10px' }}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: '10px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '4px',
                  }}
                >
                  <div>
                    <i className="glyphicon glyphicon-file" style={{ marginRight: '10px' }} />
                    CSV File Uploaded
                  </div>
                  <Button bsStyle="link" onClick={handleRemoveFile} style={{ color: '#d9534f' }}>
                    <i className="glyphicon glyphicon-remove" />
                  </Button>
                </div>

                {renderChartOptions()}

                {pnlData.data && (
                  <div style={{ marginTop: '20px', overflowX: 'auto' }}>
                    <table className="table table-bordered table-striped">
                      <tbody>
                        {pnlData.data.slice(0, 10).map((row, rowIndex) => (
                          <tr key={rowIndex}>
                            {row.map((cell, cellIndex) => (
                              <td key={cellIndex}>{cell}</td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    {pnlData.data.length > 10 && (
                      <div className="text-muted">Showing first 10 rows of {pnlData.data.length} total rows</div>
                    )}
                  </div>
                )}
              </div>
            )}
          </FormGroup>
        </Col>
      </Row>
    </div>
  );
};

export default PNLUpload;
