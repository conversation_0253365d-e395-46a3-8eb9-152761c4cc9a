import React, { useState, useEffect } from 'react';
import { Row, Col, Button, FormGroup, ControlLabel, ListGroup, ListGroupItem } from 'react-bootstrap';
import { getDecisionGroups, createDecisionGroup, deleteDecisionGroup } from '../../../actions/decisionGroups';

const GroupsSection = ({ state, setState, showAlert }) => {
  const [groups, setGroups] = useState([]);
  const [newGroupName, setNewGroupName] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadGroups();
  }, []);

  const loadGroups = async () => {
    setIsLoading(true);
    try {
      const response = await getDecisionGroups();
      setGroups(response.data || []);
    } catch (err) {
      console.error('Error loading groups:', err);
      showAlert('danger', 'Error loading groups');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateGroup = async () => {
    if (!newGroupName.trim()) {
      showAlert('danger', 'Please enter a group name');
      return;
    }

    setIsLoading(true);
    try {
      await createDecisionGroup(newGroupName.trim());
      showAlert('success', 'Group created successfully');
      setNewGroupName('');
      loadGroups();
    } catch (err) {
      console.error('Error creating group:', err);
      showAlert('danger', 'Error creating group');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteGroup = async (groupId, groupName) => {
    if (!window.confirm(`Are you sure you want to delete the group "${groupName}"? This will remove the group from all fields.`)) {
      return;
    }

    setIsLoading(true);
    try {
      await deleteDecisionGroup(groupId);

      // Remove group property from all fields that had this group
      const newSliders = { ...state.sliders };
      Object.keys(newSliders).forEach(fieldId => {
        if (newSliders[fieldId].group === groupName) {
          newSliders[fieldId] = { ...newSliders[fieldId] };
          delete newSliders[fieldId].group;
        }
      });

      setState(prevState => ({
        ...prevState,
        sliders: newSliders,
      }));

      showAlert('success', 'Group deleted successfully');
      loadGroups();
    } catch (err) {
      console.error('Error deleting group:', err);
      showAlert('danger', 'Error deleting group');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddFieldToGroup = (fieldId, groupName) => {
    setState(prevState => ({
      ...prevState,
      sliders: {
        ...prevState.sliders,
        [fieldId]: {
          ...prevState.sliders[fieldId],
          group: groupName,
        },
      },
    }));
    showAlert('success', `Field added to group "${groupName}"`);
  };

  const handleRemoveFieldFromGroup = (fieldId, groupName) => {
    setState(prevState => ({
      ...prevState,
      sliders: {
        ...prevState.sliders,
        [fieldId]: {
          ...prevState.sliders[fieldId],
          group: undefined,
        },
      },
    }));
    showAlert('success', `Field removed from group "${groupName}"`);
  };

  const getAvailableFields = () => {
    return Object.entries(state.sliders).map(([fieldId, config]) => ({
      id: fieldId,
      name: config.fieldName || `Field ${fieldId}`,
    }));
  };

  const getFieldsInGroup = (groupName) => {
    return Object.entries(state.sliders)
      .filter(([, config]) => config.group === groupName)
      .map(([fieldId, config]) => ({
        id: fieldId,
        name: config.fieldName || `Field ${fieldId}`,
      }));
  };

  return (
    <div
      style={{
        border: '1px solid #ddd',
        borderRadius: '4px',
        padding: '15px',
        marginBottom: '15px',
      }}
    >
      <Row>
        <Col md={12}>
          <FormGroup>
            <ControlLabel>Groups</ControlLabel>

            {/* Add new group section */}
            <div style={{ marginBottom: '15px' }}>
              <Row>
                <Col md={6}>
                  <input
                    type="text"
                    className="form-control"
                    value={newGroupName}
                    onChange={(e) => setNewGroupName(e.target.value)}
                    placeholder="Enter Group Name"
                    style={{ marginBottom: '10px' }}
                    disabled={isLoading}
                  />
                </Col>
                <Col md={2}>
                  <Button
                    onClick={handleCreateGroup}
                    disabled={isLoading || !newGroupName.trim()}
                  >
                    Add
                  </Button>
                </Col>
              </Row>
            </div>

          {/* Existing groups */}
          {groups.map(group => (
            <div key={group.id} style={{ marginBottom: '20px' }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '10px' }}>
                <h5 style={{ margin: 0, fontWeight: 'bold' }}>{group.name}</h5>
                <Button
                  bsStyle="link"
                  onClick={() => handleDeleteGroup(group.id, group.name)}
                  disabled={isLoading}
                  style={{ padding: '0', color: '#d9534f' }}
                >
                  <i className="glyphicon glyphicon-remove" /> Remove Group
                </Button>
              </div>

              <div style={{ marginBottom: '10px' }}>
                <Row>
                  <Col md={8}>
                    <select
                      className="form-control"
                      disabled={isLoading}
                      defaultValue=""
                    >
                      <option value="" disabled>Select a field to add to this group</option>
                      {getAvailableFields().map(field => (
                        <option key={field.id} value={field.id}>
                          {field.name}
                        </option>
                      ))}
                    </select>
                  </Col>
                  <Col md={2}>
                    <Button
                      onClick={(e) => {
                        const select = e.target.parentElement.previousElementSibling.querySelector('select');
                        if (select.value) {
                          handleAddFieldToGroup(select.value, group.name);
                          select.value = '';
                        }
                      }}
                      disabled={isLoading}
                    >
                      Add
                    </Button>
                  </Col>
                </Row>
              </div>

              {/* Fields in this group */}
              <ListGroup>
                {getFieldsInGroup(group.name).length === 0 ? (
                  <ListGroupItem style={{ fontStyle: 'italic', color: '#666' }}>
                    No fields in this group
                  </ListGroupItem>
                ) : (
                  getFieldsInGroup(group.name).map(field => (
                    <ListGroupItem
                      key={field.id}
                      style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                    >
                      <div style={{ flex: 1 }}>
                        <strong>{field.name}</strong>
                      </div>
                      <Button
                        bsStyle="link"
                        onClick={() => handleRemoveFieldFromGroup(field.id, group.name)}
                        style={{ padding: '0', color: '#d9534f' }}
                      >
                        <i className="glyphicon glyphicon-remove" />
                      </Button>
                    </ListGroupItem>
                  ))
                )}
              </ListGroup>
            </div>
          ))}

          {groups.length === 0 && !isLoading && (
            <small className="text-muted">No groups created yet.</small>
          )}
          </FormGroup>
        </Col>
      </Row>
    </div>
  );
};

export default GroupsSection;
