.container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .container {
    grid-template-columns: repeat(2, 1fr);
  }
}

.card {
  padding: 1.5rem;
  border-radius: 0.5rem;
  backdrop-filter: blur(24px);
  transition: all 0.3s ease;
}

.card:hover {
  transform: scale(1.02);
}

.cardContent {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.iconContainer {
  padding: 0.75rem;
  border-radius: 50%;
}

.label {
  font-size: 0.875rem;
}

.value {
  font-size: 1.5rem;
  font-weight: 600;
}

/* Light theme styles */
.card {
  border: 1px solid #e0e7ff;
  background-color: #f0f7ff;
}

.iconContainer {
  background-color: #e0e7ff;
}

.icon {
  color: #3b82f6;
}

.label {
  color: #4b5563;
}

.value {
  color: #2563eb;
}

/* Dark theme styles */
:global(.dark) .card {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(59, 130, 246, 0.1);
}

:global(.dark) .iconContainer {
  background-color: rgba(59, 130, 246, 0.2);
}

:global(.dark) .icon {
  color: #60a5fa;
}

:global(.dark) .label {
  color: #9ca3af;
}

:global(.dark) .value {
  color: #60a5fa;
}

/* Error styles */
.error {
  border: 1px solid #fee2e2;
  background-color: #fef2f2;
}

.errorIcon {
  background-color: #fee2e2;
  color: #ef4444;
}

.errorValue {
  color: #dc2626;
}

/* Dark theme error styles */
:global(.dark) .error {
  border: 1px solid rgba(239, 68, 68, 0.5);
  background-color: rgba(239, 68, 68, 0.2);
}

:global(.dark) .errorIcon {
  background-color: rgba(239, 68, 68, 0.3);
  color: #f87171;
}

:global(.dark) .errorValue {
  color: #f87171;
}