import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>A<PERSON><PERSON>, Tooltip, ResponsiveContainer } from 'recharts';
import { formatCurrency, formatCurrencyAbbreviated } from '../../utils/formatters';
import { useTheme } from '@/components/ThemeProvider/ThemeProvider';

const getChartColors = (client) => {
  const darkColor = client?.darkHighlightColor || '#004864';
  const lightColor = client?.lightHighlightColor || '#00A3E3';
  return [lightColor, darkColor, '#2ecc71', '#e74c3c', '#f1c40f', '#9b59b6'];
};

const DecisionPageCharts = ({ decisions, formData, calculatePageTotals, client }) => {
  const { theme } = useTheme();

  // Use colors that match OverallTotals component
  const FTE_COLOR = theme === 'dark' ? '#818cf8' : '#4338ca'; // indigo-400 / indigo-700
  const INVESTMENT_COLOR = theme === 'dark' ? '#34d399' : '#047857'; // emerald-400 / emerald-700

  // Prepare data for both charts
  const prepareChartData = () => {
    if (!decisions?.pages || !formData?.pages) return [];

    return decisions.pages.map((page, index) => {
      const totals = calculatePageTotals(index);
      return {
        name: page.page_name || page.title || `Page ${index + 1}`,
        fte: totals.pageFTE,
        investment: totals.pageInvestment,
      };
    });
  };

  const chartData = prepareChartData();

  // Filter out pages with zero values for each chart
  const fteData = chartData.filter(item => item.fte > 0);
  const investmentData = chartData.filter(item => item.investment > 0);

  const renderChart = (data, dataKey, title, color, formatter) => {
    if (data.length === 0) return null;

    return (
      <div className={`p-6 rounded-lg ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
        <h3 className={`text-lg font-medium mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
          {title}
        </h3>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <XAxis 
                dataKey="name" 
                stroke={theme === 'dark' ? '#fff' : '#000'}
                fontSize={10}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis
                stroke={theme === 'dark' ? '#fff' : '#000'}
                fontSize={12}
                tickFormatter={(value) =>
                  title.includes('Investment') ? formatCurrencyAbbreviated(value) : value
                }
              />
              <Tooltip
                formatter={(value) => [formatter(value), title]}
                contentStyle={{
                  backgroundColor: theme === 'dark' ? '#1a1a1a' : '#fff',
                  border: 'none',
                  color: theme === 'dark' ? '#fff' : '#000',
                  borderRadius: '8px',
                }}
                labelStyle={{ color: theme === 'dark' ? '#fff' : '#000' }}
                cursor={{ fill: 'rgba(255, 255, 255, 0.1)' }}
              />
              <Bar 
                dataKey={dataKey} 
                fill={color}
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  };

  // Don't render if no data
  if (fteData.length === 0 && investmentData.length === 0) {
    return null;
  }

  return (
    <div className="mb-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {renderChart(
          fteData,
          'fte',
          'Total FTEs',
          FTE_COLOR,
          (value) => `${value} FTEs`
        )}
        {renderChart(
          investmentData,
          'investment',
          'Total Investment',
          INVESTMENT_COLOR,
          (value) => formatCurrencyAbbreviated(value)
        )}
      </div>
    </div>
  );
};

export default DecisionPageCharts;
