import React from 'react';
import { useTheme } from '@/components/ThemeProvider/ThemeProvider';
import { formatCurrency, formatCurrencyAbbreviated } from '../../utils/formatters';

const DecisionTotals = ({ pageFTE, pageInvestment, client }) => {
  const { theme } = useTheme();
  const darkColor = client?.darkHighlightColor || '#004864';
  const lightColor = client?.lightHighlightColor || '#00A3E3';

  return (
    <div className="space-y-4">
      <div
        className="p-4 rounded cursor-pointer transition-colors"
        style={{
          backgroundColor: theme === 'dark' ? `${darkColor}80` : `${lightColor}20`,
          '&:hover': { backgroundColor: theme === 'dark' ? `${darkColor}90` : `${lightColor}30` },
        }}
      >
        <p style={{ color: theme === 'dark' ? lightColor : darkColor }}>FTEs: {pageFTE}</p>
      </div>
      <div
        className="p-4 rounded cursor-pointer transition-colors"
        style={{
          backgroundColor: theme === 'dark' ? `${darkColor}80` : `${lightColor}20`,
          '&:hover': { backgroundColor: theme === 'dark' ? `${darkColor}90` : `${lightColor}30` },
        }}
      >
        <p style={{ color: theme === 'dark' ? lightColor : darkColor }}>Investment Budget: {formatCurrencyAbbreviated(pageInvestment)}</p>
      </div>
    </div>
  );
};

export default DecisionTotals;
