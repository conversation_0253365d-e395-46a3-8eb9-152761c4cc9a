import React from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Label } from 'recharts';
import { formatCurrency, formatCurrencyAbbreviated } from '../../utils/formatters';
import { useTheme } from '@/components/ThemeProvider/ThemeProvider';

const getChartColors = (client) => {
  const darkColor = client?.darkHighlightColor || '#004864';
  const lightColor = client?.lightHighlightColor || '#00A3E3';
  return [lightColor, darkColor];
};

const DecisionCharts = ({ pageFTE, pageInvestment, totalFTE, totalInvestment, client }) => {
  const { theme } = useTheme();
  const CHART_COLORS = getChartColors(client);

  // Calculate percentages based on page values relative to totals
  const ftePercentage = totalFTE > 0 ? Math.round((pageFTE / totalFTE) * 100) : 0;
  const investmentPercentage = totalInvestment > 0 ? Math.round((pageInvestment / totalInvestment) * 100) : 0;

  const renderPercentageChart = (title, percentage, value, isInvestment = false) => {
    const data = [
      { name: 'Allocated', value: percentage },
      { name: 'Remaining', value: 100 - percentage },
    ];

    return (
      <div className="mb-6">
        <h3 className={`text-sm font-medium text-center mt-2 mb-4 ${
          theme === 'dark' ? 'text-white' : 'text-gray-800'
        }`}>{title}</h3>

        <div className="h-[150px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                dataKey="value"
                cx="50%"
                cy="50%"
                innerRadius={45}
                outerRadius={60}
                startAngle={90}
                endAngle={-270}
              >
                {data.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={index === 0
                      ? CHART_COLORS[0]
                      : (theme === 'dark' ? '#1a1a1a' : '#e5e7eb')
                    }
                  />
                ))}
                <Label
                  value={`${percentage}%`}
                  position="center"
                  className="text-base font-semibold"
                  fill={theme === 'dark' ? '#fff' : '#000'}
                />
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className={`text-center text-sm ${
          theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
        }`}>
          {isInvestment ? formatCurrencyAbbreviated(value) : `${value} FTEs`}
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col space-y-4">
      {renderPercentageChart('FTEs Allocation', ftePercentage, pageFTE, false)}
      {renderPercentageChart('Budget Allocation', investmentPercentage, pageInvestment, true)}
    </div>
  );
};

export default DecisionCharts;
