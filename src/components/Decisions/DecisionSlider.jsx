import React from 'react';
import * as SliderPrimitive from '@radix-ui/react-slider';
import { formatCurrency } from '@/utils/formatters';

function DecisionSlider({ slider, value, onChange, client, isGrouped = false }) {
  const darkColor = client?.darkHighlightColor || '#004864';
  const lightColor = client?.lightHighlightColor || '#00A3E3';

  const handleValueChange = (newValue) => {
    onChange(newValue[0]);
  };

  return (
    <div className={isGrouped ? "p-4 rounded-lg bg-white/3" : "bg-white/5 p-6 rounded-lg"}>
      <h3 className="text-lg font-medium mb-4" style={{ color: '#fff' }}>
        {slider.fieldName}
      </h3>
      {slider.description && <p className="text-sm text-gray-400 mb-4">{slider.description}</p>}
      <div className="relative h-12">
        <SliderPrimitive.Root
          className="relative flex items-center select-none touch-none w-full h-5"
          value={[value]}
          min={0}
          max={slider.labels.length - 1}
          step={1}
          onValueChange={handleValueChange}
        >
          <SliderPrimitive.Track
            className="relative grow h-1 rounded-full"
            style={{ backgroundColor: `${darkColor}40` }}
          >
            <SliderPrimitive.Range className="absolute h-full rounded-full" style={{ backgroundColor: lightColor }} />
          </SliderPrimitive.Track>
          <SliderPrimitive.Thumb
            className="block w-4 h-4 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2"
            style={{
              backgroundColor: lightColor,
              '--tw-ring-color': darkColor,
              '--tw-ring-offset-color': '#000000',
            }}
          />
        </SliderPrimitive.Root>

        {/* Labels */}
        <div className="flex justify-between text-sm text-gray-400 mt-6">
          {slider.labels.map((label, i) => (
            <span key={i} style={{ color: i === value ? lightColor : '#9CA3AF' }}>
              {label.text}
            </span>
          ))}
        </div>
      </div>

      {/* Reflection Values */}
      <div className="mt-12 space-y-2">
        {slider.labels[value].fte != 0 && (
          <div className="p-2 rounded" style={{ backgroundColor: `${darkColor}20` }}>
            <p style={{ color: lightColor }}>FTEs: {slider.labels[value].fte}</p>
          </div>
        )}
        {slider.labels[value].investment != 0 && (
          <div className="p-2 rounded" style={{ backgroundColor: `${darkColor}20` }}>
            <p style={{ color: lightColor }}>Investment: {formatCurrency(slider.labels[value].investment)}</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default DecisionSlider;
