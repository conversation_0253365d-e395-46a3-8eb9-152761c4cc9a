import React from 'react';
import { Users, DollarSign } from 'lucide-react';
import { formatCurrency, formatCurrencyAbbreviated } from '../../utils/formatters';
import { useTheme } from '@/components/ThemeProvider/ThemeProvider';
import styles from './overall-totals.module.css';

const OverallTotals = ({ totalFTE, totalInvestment, client }) => {
  const { theme } = useTheme();

  // Calculate FTE availability (countdown format)
  const fteAvailable = client?.fteMax ? client.fteMax - totalFTE : null;
  const fteExceedsMax = client?.fteMax && totalFTE > client.fteMax;
  const investmentExceedsMax = client?.investmentMax && totalInvestment > client.investmentMax;

  // Determine FTE display value and label
  const getFTEDisplayValue = () => {
    if (!client?.fteMax) {
      // If no cap is set, show the traditional total
      return `${totalFTE} FTEs`;
    }

    if (totalFTE === 0) {
      // If no options selected, show the full cap
      return `${client.fteMax} FTEs`;
    }

    // Show remaining FTEs (can be negative if over limit)
    return `${fteAvailable} FTEs`;
  };

  return (
    <div className={styles.container}>
      <div className={`${styles.card} ${fteExceedsMax ? styles.error : ''}`}>
        <div className={styles.cardContent}>
          <div className={`${styles.iconContainer} ${fteExceedsMax ? styles.errorIcon : ''}`}>
            <Users size={24} className={fteExceedsMax ? styles.errorIcon : styles.icon} />
          </div>
          <div>
            <p className={styles.label}>
              {client?.fteMax ? 'FTEs Available' : 'Total FTE Impact'}
            </p>
            <p className={`${styles.value} ${fteExceedsMax ? styles.errorValue : ''}`}>
              {getFTEDisplayValue()}
            </p>
          </div>
        </div>
      </div>

      <div className={`${styles.card} ${investmentExceedsMax ? styles.error : ''}`}>
        <div className={styles.cardContent}>
          <div className={`${styles.iconContainer} ${investmentExceedsMax ? styles.errorIcon : ''}`}>
            <DollarSign size={24} className={investmentExceedsMax ? styles.errorIcon : styles.icon} />
          </div>
          <div>
            <p className={styles.label}>Total Investment Budget</p>
            <p className={`${styles.value} ${investmentExceedsMax ? styles.errorValue : ''}`}>
              {formatCurrencyAbbreviated(totalInvestment)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OverallTotals;
