import React, { useState, useEffect } from 'react';
import { Row, Col, Button, InputGroup, FormControl, PageHeader, Table } from 'react-bootstrap';
import ResultLimit from '../App/ResultLimit';
import { getDecisions } from '../../actions/decisions';
import BlockUi from 'react-block-ui';
import 'react-block-ui/style.css';

const Decisions = ({ alert, navigate }) => {
  const [state, setState] = useState({
    query: '',
    showDisabled: false,
    currentPage: 1,
    totalItems: 0,
    limit: 20,
    offset: 0,
    sort: 'name',
    ascending: true,
    decisions: [],
    blocking: false,
  });

  useEffect(() => {
    proceedDecisions();
  }, []);

  const handleLimitChange = (e) => {
    setState(
      (prevState) => ({
        ...prevState,
        limit: e.target.value,
        offset: 0,
        currentPage: 1,
      }),
      () => {
        proceedDecisions();
      }
    );
  };

  const updateQuery = (field, value) => {
    setState((prevState) => ({
      ...prevState,
      [field]: value,
    }));
  };

  const proceedDecisions = () => {
    const { query, limit, offset, sort, ascending, showDisabled } = state;
    const q = {
      query,
      limit,
      offset,
      sort,
      ascending,
      showDisabled,
    };

    blockUi();
    getDecisions(q).payload.then(
      (results) => {
        setState((prevState) => ({
          ...prevState,
          decisions: results.data,
          totalItems: results.data.length ? Math.ceil(results.data[0].total / limit) : 0,
          blocking: false,
        }));
      },
      (error) => {
        alert('danger', 'Error', `Error listing decisions: ${error.toString()}`);
        unBlockUi();
      }
    );
  };

  const viewDecision = (id) => {
    navigate(`/decisions/${id}`);
  };

  const blockUi = () => {
    setState((prevState) => ({
      ...prevState,
      blocking: true,
    }));
  };

  const unBlockUi = () => {
    setState((prevState) => ({
      ...prevState,
      blocking: false,
    }));
  };

  const toggleDisabled = () => {
    setState(
      (prevState) => ({
        ...prevState,
        showDisabled: !prevState.showDisabled,
      }),
      () => {
        proceedDecisions();
      }
    );
  };

  const { showDisabled, decisions } = state;

  return (
    <div className="container root-container">
      <BlockUi tag="div" blocking={state.blocking}>
        <PageHeader>Decisions</PageHeader>
        <Row>
          <Col lg={2} md={2} xs={6}>
            <ResultLimit onChange={handleLimitChange} />
          </Col>
          <Col lg={4} md={4} xs={6}>
            <InputGroup>
              <FormControl
                type="text"
                placeholder="Search"
                name="query"
                onChange={(e) => updateQuery('query', e.target.value)}
              />
              <InputGroup.Button>
                <Button type="submit" className="pull-right" name="list" onClick={proceedDecisions}>
                  Go
                </Button>
              </InputGroup.Button>
            </InputGroup>
          </Col>
          <Col lg={6} md={6} xs={12}>
            <Button className="pull-right" bsStyle="primary" onClick={() => navigate('/decisions/add')}>
              Add Decision
            </Button>
            <Button className="pull-right" bsStyle={showDisabled ? 'success' : 'default'} onClick={toggleDisabled}>
              {showDisabled ? 'Hide Disabled' : 'Show Disabled'}
            </Button>
          </Col>
        </Row>

        <Table striped bordered hover responsive>
          <thead>
            <tr>
              <th>Name</th>
              <th>Created</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {decisions.map((decision) => (
              <tr key={decision.id}>
                <td>{decision.name}</td>
                <td>{decision.created_at}</td>
                <td>{decision.disabled ? 'Disabled' : 'Active'}</td>
                <td>
                  <Button bsSize="small" onClick={() => viewDecision(decision.id)}>
                    View
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      </BlockUi>
    </div>
  );
};

export default Decisions;
