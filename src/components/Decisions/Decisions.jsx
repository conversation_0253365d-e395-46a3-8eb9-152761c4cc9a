import React, { useState, useEffect, useMemo } from 'react';
import { useTheme } from '@/components/ThemeProvider/ThemeProvider';
import { usePageBackground } from '../../contexts/PageBackgroundContext';
import { toast } from 'sonner';
import to from 'await-to-js';
import Loader from '../common/Loader';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { createDecisionResult, getDecision, getUserDecisionResults } from '../../actions/user';
import { Button } from '@/components/ui/button';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import DecisionSlider from './DecisionSlider';
import DecisionIncentives from './DecisionIncentives';
import DecisionPNL from './DecisionPNL';
import DecisionTotals from './DecisionTotals';
import DecisionCharts from './DecisionCharts';
import DecisionPagePNL from './DecisionPagePNL';
import OverallTotals from './OverallTotals';

const FilterButton = ({ active, children, onClick }) => {
  const { theme } = useTheme();
  return (
    <button
      onClick={onClick}
      className={`px-4 py-2 rounded-full transition-all text-sm font-medium ${
        theme === 'dark'
          ? `border border-white/10 ${active ? 'bg-white/10 text-white' : 'bg-black/40 text-white/70 hover:bg-white/5'}`
          : `border border-gray-300/30 ${active ? 'bg-white/70 text-gray-800' : 'bg-white/30 text-gray-600 hover:bg-white/50'}`
      }`}
    >
      {children}
    </button>
  );
};

const DecisionComplete = ({ handleSave, userSelections, isSaving, client }) => {
  const { theme } = useTheme();

  // Calculate FTE display for summary
  const getFTESummaryText = () => {
    if (!client?.fteMax) {
      return `Total FTE Impact: ${userSelections.totalFTE} FTEs`;
    }

    const fteAvailable = client.fteMax - userSelections.totalFTE;
    if (userSelections.totalFTE === 0) {
      return `FTEs Available: ${client.fteMax} FTEs`;
    }

    return `FTEs Available: ${fteAvailable} FTEs`;
  };

  return (
    <div className={`p-8 rounded-lg ${theme === 'dark' ? 'bg-white/5' : 'bg-white/60 border border-gray-300/30'}`}>
      <h2 className={`text-2xl font-semibold mb-6 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>Decision Complete</h2>
      <div className="space-y-6">
        <div className={`${theme === 'dark' ? 'text-gray-200' : 'text-gray-800'}`}>
          <p className="mb-4">Thank you for completing your decision process. Here's a summary of your selections:</p>
          <ul className="list-disc list-inside space-y-2 ml-4">
            <li>{getFTESummaryText()}</li>
            <li>Total Investment Budget: ${userSelections.totalInvestment.toLocaleString()}</li>
          </ul>
        </div>
        <button
          onClick={handleSave}
          disabled={isSaving}
          className={`px-6 py-3 bg-blue-600 text-white rounded-md transition-colors ${
            isSaving ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-700'
          }`}
        >
          {isSaving ? 'Saving...' : 'Save Decisions'}
        </button>
      </div>
    </div>
  );
};

function Decisions({ user, navigate }) {
  const { theme } = useTheme();
  const { setPageImage } = usePageBackground();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [decisions, setDecisions] = useState(null);
  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [activeGroupTab, setActiveGroupTab] = useState('');
  const [formData, setFormData] = useState({
    pages: [],
  });
  const [savedDecisions, setSavedDecisions] = useState(null);
  const lightHighlightColor = user?.client?.lightHighlightColor || '#00A3E3';
  const {
    client: { decisionsTabName, decisionsTabVisibility },
  } = user;

  // Add this helper function to track formData changes
  // For some reason this fixes the issue with decision pages being loaded, likely a minification issue
  const trackFormDataChange = (source, newData) => {
    // console.log(`FormData changed by: ${source}`);
    // console.log('New formData:', JSON.stringify({
    //   hasPages: !!newData.pages,
    //   pagesLength: newData.pages?.length || 0,
    //   hasOtherProps: Object.keys(newData).filter(k => k !== 'pages')
    // }));
    return newData;
  };

  // Modify setFormData to track all changes
  const setFormDataWithTracking = (updater) => {
    if (typeof updater === 'function') {
      setFormData((prev) => {
        const result = updater(prev);
        return trackFormDataChange('function updater', result);
      });
    } else {
      setFormData(trackFormDataChange('direct assignment', updater));
    }
  };

  useEffect(() => {
    if (user?.client?.hasOwnProperty('decisionsTabVisibility') && !decisionsTabVisibility) {
      navigate('/home');
      return;
    }
  }, [user?.client, decisionsTabVisibility, navigate]);

  const fetchDecisions = async () => {
    setIsLoading(true);
    const [err, res] = await to(getDecision().payload);

    if (err) {
      toast.error('Failed to load decisions');
      setError('Failed to load decisions');
      setIsLoading(false);
      return;
    }

    setDecisions(res.data);

    // After loading the decision scheme, fetch user's saved decisions
    fetchUserDecisions();
  };

  // Fetch user's saved decisions
  const fetchUserDecisions = async () => {
    if (!user?.user?.id) return;

    const [err, res] = await to(getUserDecisionResults(user.user.id).payload);

    if (err) {
      console.error('Failed to load saved decisions:', err);
      setIsLoading(false);
      return;
    }

    if (res.data && res.data.length > 0) {
      // Get the most recent saved decision
      const latestDecision = res.data[res.data.length - 1];
      setSavedDecisions({
        ...savedDecisions,
        ...latestDecision
      });
    }

    setIsLoading(false);
  };

  useEffect(() => {
    fetchDecisions();
  }, []);

  // Initialize form data with saved decisions if available
  useEffect(() => {
    if (decisions && savedDecisions && savedDecisions.selected_values) {
      setFormDataWithTracking({
        ...formData,
        ...savedDecisions.selected_values
      });
      toast.info('Your previous decisions have been loaded');
    } else if (decisions) {
      // Initialize with default values if no saved decisions
      setFormDataWithTracking({
        ...formData,
        pages: decisions.pages.map((page) => ({
          sliders: Object.fromEntries(Object.entries(page.sliders).map(([key, slider]) => [key, 0])),
          selectedIncentives: Array.from({ length: page.incentives?.optionLimit || 1 }, () => null),
        })),
      });
    }
  }, [decisions, savedDecisions]);

  // Update page background image when current page changes
  useEffect(() => {
    if (decisions && decisions.pages && currentPageIndex < decisions.pages?.length) {
      const currentPage = decisions.pages[currentPageIndex];
      setPageImage(currentPage?.page_image || null);
    } else {
      // Clear page image when on final PNL summary page or no page data
      setPageImage(null);
    }
  }, [currentPageIndex, decisions, setPageImage]);

  // Initialize active group tab when page changes
  useEffect(() => {
    if (decisions && decisions.pages && currentPageIndex < decisions.pages?.length) {
      const currentPage = decisions.pages[currentPageIndex];
      const availableTabs = getGroupTabs(currentPage.sliders);

      // Set the first available tab as active, or reset if current tab is not available
      if (availableTabs.length > 0 && (!activeGroupTab || !availableTabs.includes(activeGroupTab))) {
        setActiveGroupTab(availableTabs[0]);
      }
    }
  }, [currentPageIndex, decisions, activeGroupTab]);

  const handleNextPage = () => {
    if (currentPageIndex < decisions.pages?.length) {
      setCurrentPageIndex((prev) => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPageIndex > 0) {
      setCurrentPageIndex((prev) => prev - 1);
    }
  };

  const handleSliderChange = (pageIndex, sliderId, value) => {
    setFormDataWithTracking((prev) => ({
      ...prev,
      pages: prev.pages.map((page, idx) =>
        idx === pageIndex
          ? {
              ...page,
              sliders: {
                ...page.sliders,
                [sliderId]: value,
              },
            }
          : page
      ),
    }));
  };

  const handleIncentiveChange = (dropdownIndex, selected) => {
    setFormDataWithTracking((prev) => ({
      ...prev,
      pages: prev.pages.map((page, idx) =>
        idx === currentPageIndex
          ? {
              ...page,
              selectedIncentives: page.selectedIncentives.map((incentive, incentiveIdx) =>
                incentiveIdx === dropdownIndex ? selected : incentive
              ),
            }
          : page
      ),
    }));
  };

  const calculatePageTotals = (pageIndex) => {
    if (!formData?.pages || !decisions?.pages) {
      console.warn('Missing required data for calculations');
      return { pageFTE: 0, pageInvestment: 0 };
    }
    if (pageIndex >= formData.pages?.length || pageIndex >= decisions.pages?.length) {
      console.warn(`Invalid page index: ${pageIndex}`);
      return { pageFTE: 0, pageInvestment: 0 };
    }
    const page = formData.pages[pageIndex];
    const pageData = decisions.pages[pageIndex];
    if (!page || !pageData) {
      console.warn(`Missing page data for index: ${pageIndex}`);
      return { pageFTE: 0, pageInvestment: 0 };
    }

    let pageFTE = 0;
    let pageInvestment = 0;
    if (page.sliders && pageData.sliders) {
      Object.entries(page.sliders).forEach(([sliderId, value]) => {
        const sliderData = pageData.sliders[sliderId];
        if (sliderData?.labels && Array.isArray(sliderData.labels) && sliderData.labels[value]) {
          const selectedLabel = sliderData.labels[value];
          pageFTE += Number(selectedLabel.fte || 0);
          pageInvestment += Number(selectedLabel.investment || 0);
        }
      });
    }

    // Add values from selected incentives
    if (page.selectedIncentives && Array.isArray(page.selectedIncentives)) {
      page.selectedIncentives.forEach((incentive) => {
        if (incentive) {
          pageFTE += Number(incentive.fte || 0);
          pageInvestment += Number(incentive.investment || 0);
        }
      });
    }

    return { pageFTE, pageInvestment };
  };
  const handleSave = async () => {
    setIsSaving(true);
    try {
      const [storeErr] = await to(
        createDecisionResult({
          userId: user?.user?.id,
          clientId: localStorage.getItem('clientId'),
          decisionSchemeId: user?.client?.decisionSchemeId,
          selectedValues: formData,
          ...overallTotals,
        }).payload
      );

      if (storeErr) {
        toast.error('Failed to save your decisions');
        console.error('Failed to store decision results:', storeErr);
        return;
      }

      toast.success('Decisions saved successfully');
    } catch (error) {
      console.error('Error saving decisions:', error);
      toast.error('An error occurred while saving your decisions');
    } finally {
      setIsSaving(false);
    }
  };
  const pageTotals = useMemo(() => {
    if (!decisions || !decisions.pages || !formData.pages?.length || currentPageIndex >= decisions.pages?.length)
      return {};
    return calculatePageTotals(currentPageIndex);
  }, [currentPageIndex, decisions, formData]);

  const calculateOverallTotals = () => {
    if (!formData?.pages || !decisions?.pages) {
      return { totalFTE: 0, totalInvestment: 0 };
    }

    return formData.pages.reduce(
      (totals, _, pageIndex) => {
        const pageTotals = calculatePageTotals(pageIndex);
        return {
          totalFTE: totals.totalFTE + pageTotals.pageFTE,
          totalInvestment: totals.totalInvestment + pageTotals.pageInvestment,
        };
      },
      { totalFTE: 0, totalInvestment: 0 }
    );
  };

  const preparePageChartData = () => {
    if (!currentPage || !formData.pages[currentPageIndex]) return [];

    const chartData = [];

    // Add data from sliders
    Object.entries(currentPage.sliders).forEach(([key, slider]) => {
      const value = formData.pages[currentPageIndex].sliders[key];
      const selectedLabel = slider.labels[value];

      chartData.push(
        ['FTE', selectedLabel.text, Number(selectedLabel.fte)],
        ['Investment', selectedLabel.text, Number(selectedLabel.investment)]
      );
    });

    // Add data from selected incentives
    const selectedIncentives = formData.pages[currentPageIndex].selectedIncentives;
    if (selectedIncentives && Array.isArray(selectedIncentives)) {
      selectedIncentives.forEach((incentive) => {
        if (incentive) {
          chartData.push(
            ['FTE', incentive.label, Number(incentive.fte)],
            ['Investment', incentive.label, Number(incentive.investment)]
          );
        }
      });
    }

    // Add totals
    chartData.push(['Total', 'FTE', pageTotals.pageFTE], ['Total', 'Investment', pageTotals.pageInvestment]);

    return chartData;
  };
  const overallTotals = useMemo(() => calculateOverallTotals(), [formData]);

  // Helper function to get available group tabs
  const getGroupTabs = (sliders) => {
    const groups = new Set();
    let hasUngrouped = false;

    Object.entries(sliders).forEach(([key, slider]) => {
      if (slider.group) {
        groups.add(slider.group);
      } else {
        hasUngrouped = true;
      }
    });

    const tabs = Array.from(groups);
    if (hasUngrouped) {
      tabs.push('Other');
    }

    return tabs;
  };

  // Helper function to group sliders by their group property
  const groupSliders = (sliders) => {
    const grouped = {};
    const ungrouped = {};

    Object.entries(sliders).forEach(([key, slider]) => {
      if (slider.group) {
        if (!grouped[slider.group]) {
          grouped[slider.group] = {};
        }
        grouped[slider.group][key] = slider;
      } else {
        ungrouped[key] = slider;
      }
    });

    return { grouped, ungrouped };
  };

  // Helper function to get sliders for the active tab
  const getSlidersForActiveTab = (sliders, activeTab) => {
    const { grouped, ungrouped } = groupSliders(sliders);

    if (activeTab === 'Other') {
      return ungrouped;
    } else if (grouped[activeTab]) {
      return grouped[activeTab];
    }

    return {};
  };

  // console.log(decisions);

  if (isLoading) return <Loader />;

  // Check if formData has the required structure
  const hasValidFormData = formData && 
                           formData.pages && 
                           Array.isArray(formData.pages) && 
                           formData.pages.length > 0;

  if (!decisions || !decisions.pages || !Array.isArray(decisions.pages) || !hasValidFormData) {
    return (
      <div className="p-8">
        <div className="mb-8">
          <h1 className={`text-2xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
            {decisionsTabName || 'Decisions'}
          </h1>
        </div>
        {error && (
          <Alert variant="destructive" className="mb-6 bg-red-500/10 text-red-300 border-red-500/20">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        <div className="p-4 rounded-lg bg-yellow-500/10 border border-yellow-500/20 text-yellow-300">
          <p>Loading decision data... If this persists, please refresh the page.</p>
        </div>
      </div>
    );
  }

  const currentPage = decisions.pages[currentPageIndex];

  return (
    <div className="p-8">
      {error && (
        <Alert variant="destructive" className="mb-6 bg-red-500/10 text-red-300 border-red-500/20">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="mb-8 flex justify-between items-center">
        <h1 className={`text-2xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
          {currentPageIndex < decisions.pages?.length
            ? (currentPage?.page_name || currentPage?.title || decisionsTabName || 'Decisions')
            : (decisionsTabName || 'Decisions')
          }
        </h1>

        <div className="flex justify-end items-center">
          <Button
            onClick={handleSave}
            disabled={isSaving}
            style={{ background: lightHighlightColor }}
            className="font-semibold hover:opacity-90 mr-2 transition-opacity"
          >
            {isSaving ? (
              <>
                <Loader className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Decisions'
            )}
          </Button>
          <div className="space-x-2">
            <Button onClick={handlePrevPage} disabled={currentPageIndex === 0} variant="secondary" size="sm">
              {'<'}
            </Button>
            <Button
              onClick={handleNextPage}
              disabled={currentPageIndex === decisions.pages?.length}
              variant="secondary"
              size="sm"
            >
              {'>'}
            </Button>
          </div>
        </div>
      </div>

      <div className="space-y-8">
        <OverallTotals totalFTE={overallTotals.totalFTE} totalInvestment={overallTotals.totalInvestment} client={user.client} />
        {currentPageIndex < decisions.pages?.length ? (
          <div className="space-y-8">
            {/* Group Tabs */}
            {(() => {
              const availableTabs = getGroupTabs(currentPage.sliders);

              if (availableTabs.length > 1) {
                return (
                  <div className="flex gap-2 mb-6">
                    {availableTabs.map((tab) => (
                      <FilterButton
                        key={tab}
                        active={activeGroupTab === tab}
                        onClick={() => setActiveGroupTab(tab)}
                      >
                        {tab}
                      </FilterButton>
                    ))}
                  </div>
                );
              }
              return null;
            })()}

            <div className="grid grid-cols-10 gap-8">
              <div className="col-span-7 space-y-8">
                {(() => {
                  const availableTabs = getGroupTabs(currentPage.sliders);

                  if (availableTabs.length > 1) {
                    // Tab-based rendering
                    const slidersForTab = getSlidersForActiveTab(currentPage.sliders, activeGroupTab);

                    return Object.entries(slidersForTab).map(([key, slider]) => (
                      <DecisionSlider
                        key={key}
                        slider={slider}
                        value={formData.pages[currentPageIndex]?.sliders[key] || 0}
                        onChange={(value) => handleSliderChange(currentPageIndex, key, value)}
                        client={user.client}
                      />
                    ));
                  } else {
                    // Original rendering for pages without groups or with only one group
                    const { grouped, ungrouped } = groupSliders(currentPage.sliders);

                    return (
                      <>
                        {/* Render ungrouped sliders first */}
                        {Object.entries(ungrouped).map(([key, slider]) => (
                          <DecisionSlider
                            key={key}
                            slider={slider}
                            value={formData.pages[currentPageIndex]?.sliders[key] || 0}
                            onChange={(value) => handleSliderChange(currentPageIndex, key, value)}
                            client={user.client}
                          />
                        ))}

                        {/* Render grouped sliders in collapsible sections */}
                        {Object.entries(grouped).map(([groupName, groupSliders]) => (
                          <div key={groupName} className="bg-white/5 p-6 rounded-lg">
                            <Accordion type="single" collapsible defaultValue={groupName}>
                              <AccordionItem value={groupName} className="border-none">
                                <AccordionTrigger className="text-lg font-medium text-white hover:no-underline">
                                  {groupName}
                                </AccordionTrigger>
                                <AccordionContent className="space-y-6 pt-4">
                                  {Object.entries(groupSliders).map(([key, slider]) => (
                                    <DecisionSlider
                                      key={key}
                                      slider={slider}
                                      value={formData.pages[currentPageIndex]?.sliders[key] || 0}
                                      onChange={(value) => handleSliderChange(currentPageIndex, key, value)}
                                      client={user.client}
                                      isGrouped={true}
                                    />
                                  ))}
                                </AccordionContent>
                              </AccordionItem>
                            </Accordion>
                          </div>
                        ))}
                      </>
                    );
                  }
                })()}
              </div>
              <div className="col-span-3 space-y-8">
                <DecisionTotals
                  pageFTE={pageTotals.pageFTE}
                  pageInvestment={pageTotals.pageInvestment}
                  client={user.client}
                />

                <DecisionIncentives
                  incentives={currentPage.incentives}
                  selectedIncentives={formData.pages[currentPageIndex]?.selectedIncentives}
                  onIncentiveChange={handleIncentiveChange}
                  client={user.client}
                />

                <DecisionCharts
                  pageFTE={pageTotals.pageFTE}
                  pageInvestment={pageTotals.pageInvestment}
                  totalFTE={overallTotals.totalFTE}
                  totalInvestment={overallTotals.totalInvestment}
                  client={user?.client}
                />
              </div>
            </div>

            {/* Page PNL section */}
            {/* {currentPage?.page_pnl?.data && currentPage?.page_pnl?.data?.length > 0 && (
              <div className="mt-8">
                <DecisionPagePNL data={currentPage.page_pnl} />
              </div>
            )} */}
          </div>
        ) : (
          <>
            {decisions?.pnl?.data && Array.isArray(decisions.pnl.data) && decisions.pnl.data.length > 0 ? (
              <DecisionPNL
                data={decisions.pnl}
                type={decisions.pnl.charts}
                client={user?.client}
                userSelections={overallTotals}
                user={user}
                pages={decisions.pages}
                decisions={decisions}
                formData={formData}
                calculatePageTotals={calculatePageTotals}
                savedDecisions={savedDecisions}
              />
            ) : (
              <DecisionComplete userSelections={overallTotals} handleSave={handleSave} isSaving={isSaving} client={user.client} />
            )}
          </>
        )}
        <div className="flex justify-between items-center mb-4">
          <h2 className={`text-xl ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
            {currentPageIndex === decisions.pages?.length ? '' : `${currentPageIndex + 1} of ${decisions.pages?.length}`}
          </h2>
        </div>
      </div>
    </div>
  );
}

export default Decisions;
