import React, { useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Col, Button, Alert, Nav, NavItem, FormGroup, ControlLabel } from 'react-bootstrap';
import BlockUi from 'react-block-ui';
import { reduxForm } from 'redux-form';
import { createDecision } from '../../actions/decisions';
import { INITIAL_PAGE_STATE, MAX_PAGES } from './constants';
import DecisionForm from './components/DecisionForm';
import PNLUpload from './components/PNLUpload/PNLUpload';

const validate = (values) => {
  const errors = {};
  let hasErrors = false;

  if (!values.name || values.name.trim() === '') {
    errors.name = 'Enter a decision name';
    hasErrors = true;
  }

  if (!values.description || values.description.trim() === '') {
    errors.description = 'Enter a description';
    hasErrors = true;
  }

  return hasErrors && errors;
};

const AddDecision = ({ alert, navigate }) => {
  const [blocking, setBlocking] = useState(false);
  const [alertInfo, setAlertInfo] = useState(null);
  const [pages, setPages] = useState([{ ...INITIAL_PAGE_STATE }]);
  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [decisionName, setDecisionName] = useState(''); // Add this state
  const [pnlData, setPnlData] = useState({ data: null, charts: [] });

  // AI Prompt Template state
  const [useCustomGlobalTemplate, setUseCustomGlobalTemplate] = useState(false);
  const [globalTemplate, setGlobalTemplate] = useState('');

  // Use useMemo to memoize the current page state
  const currentPageState = useMemo(() => pages[currentPageIndex], [pages, currentPageIndex]);

  const setCurrentPageState = (updater) => {
    setPages((prevPages) => {
      const newPages = [...prevPages];
      if (typeof updater === 'function') {
        newPages[currentPageIndex] = updater(prevPages[currentPageIndex]);
      } else {
        newPages[currentPageIndex] = updater;
      }
      console.log({ newPages });

      return newPages;
    });
  };

  const handleAddPage = () => {
    if (pages.length >= MAX_PAGES) {
      showAlert('danger', `Maximum ${MAX_PAGES} pages allowed`);
      return;
    }
    setPages([...pages, { ...INITIAL_PAGE_STATE }]);
    setCurrentPageIndex(pages.length);
  };

  const handleDeletePage = (indexToDelete) => {
    if (pages.length <= 1) {
      showAlert('danger', 'Cannot delete the last page');
      return;
    }

    const newPages = pages.filter((_, index) => index !== indexToDelete);
    setPages(newPages);

    // Adjust current page index if necessary
    if (currentPageIndex >= indexToDelete) {
      setCurrentPageIndex(Math.max(0, currentPageIndex - 1));
    }
  };

  const showAlert = (type, message) => {
    setAlertInfo({ type, message });
    setTimeout(() => setAlertInfo(null), 3000);
  };

  const handleFormSubmit = async (event) => {
    event.preventDefault();

    if (!decisionName.trim()) {
      showAlert('danger', 'Please enter a decision name');
      return;
    }

    setBlocking(true);

    try {
      const payload = {
        name: decisionName.trim(),
        pnl: pnlData,
        globalTemplate: useCustomGlobalTemplate ? globalTemplate : null,
        useCustomGlobalTemplate: useCustomGlobalTemplate,
        decisions: pages.map((page, index) => ({
          page_name: page.page_name || `Page ${index + 1}`,
          page_image: page.page_image || '',
          sliders: page.sliders,
          incentives: page.incentives,
          pnl: page.pnl || null,
          page_pnl_prompt_template: page.useCustomPageTemplate ? page.pageTemplate : null,
          use_custom_page_template: page.useCustomPageTemplate || false,
        })),
      };

      const response = await createDecision(payload);

      if (response.error) {
        showAlert('danger', 'Failed to create decision');
        return;
      }

      showAlert('success', 'Decision created successfully');
      setTimeout(() => {
        navigate('/decisions');
      }, 2000);
    } catch (error) {
      console.error('Error creating decision:', error);
      showAlert('danger', 'An error occurred while creating the decision');
    } finally {
      setBlocking(false);
    }
  };
  console.log({ pages, pnlData });

  return (
    <div className="container root-container">
      <BlockUi tag="div" blocking={blocking}>
        {alertInfo && (
          <Alert bsStyle={alertInfo.type} onDismiss={() => setAlertInfo(null)} style={{ marginBottom: '20px' }}>
            {alertInfo.message}
          </Alert>
        )}
        <Row>
          <Col md={10}>
            <PageHeader>Add Decision</PageHeader>
          </Col>
          <Col md={2}>
            <Button
              className="pull-right btn-info save-button"
              onClick={handleFormSubmit}
              disabled={!decisionName.trim()} // Disable if no name
            >
              Save
            </Button>
          </Col>
        </Row>

        {/* Add the name input field */}
        <Row style={{ marginBottom: '20px' }}>
          <Col md={12}>
            <FormGroup>
              <ControlLabel>Decision Name</ControlLabel>
              <input
                type="text"
                className="form-control"
                value={decisionName}
                onChange={(e) => setDecisionName(e.target.value)}
                placeholder="Enter decision name"
              />
            </FormGroup>
          </Col>
        </Row>

        {/* AI Prompt Template Configuration */}
        <Row style={{ marginBottom: '20px' }}>
          <Col md={12}>
            <FormGroup>
              <div className="checkbox">
                <label>
                  <input
                    type="checkbox"
                    checked={useCustomGlobalTemplate}
                    onChange={(e) => setUseCustomGlobalTemplate(e.target.checked)}
                  />
                  Use Custom AI Prompt Template (Global)
                </label>
              </div>
              {useCustomGlobalTemplate && (
                <div style={{ marginTop: '10px' }}>
                  <ControlLabel>Custom AI Prompt Template</ControlLabel>
                  <textarea
                    className="form-control"
                    rows="6"
                    value={globalTemplate}
                    onChange={(e) => setGlobalTemplate(e.target.value)}
                    placeholder="Enter your custom AI prompt template. Use {{VARIABLE_NAME}} for dynamic content substitution."
                  />
                  <small className="help-block">
                    Available variables: {{USER_SELECTIONS}}, {{FORM_DATA}}, {{PAGE_NAME}}, {{SCHEME_NAME}}
                  </small>
                </div>
              )}
            </FormGroup>
          </Col>
        </Row>

        <Button className="pull-right btn-primary" onClick={handleAddPage} disabled={pages.length >= MAX_PAGES}>
          Add Page
        </Button>
        <Nav bsStyle="tabs" activeKey={currentPageIndex} onSelect={setCurrentPageIndex} style={{ marginBottom: 8 }}>
          {pages.map((_, index) => (
            <NavItem key={index} eventKey={index}>
              Page {index + 1}
              {pages.length > 1 && (
                <Button
                  bsStyle="link"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleDeletePage(index);
                  }}
                  style={{ padding: '0 5px', color: 'red' }}
                >
                  ✕
                </Button>
              )}
            </NavItem>
          ))}
        </Nav>
        <DecisionForm
          key={currentPageIndex}
          state={currentPageState}
          setState={setCurrentPageState}
          showAlert={showAlert}
        />
        <PNLUpload pnlData={pnlData} onChange={setPnlData} label={'Global P&L Data'} />
      </BlockUi>
    </div>
  );
};

export default reduxForm({
  form: 'AddDecision',
  validate,
})(AddDecision);
