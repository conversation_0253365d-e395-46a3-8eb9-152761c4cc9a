import React, { Component } from 'react';
import to from 'await-to-js';
import { PageHeader, Button, Row, Col, FormGroup, ControlLabel } from 'react-bootstrap';
import BlockUi from 'react-block-ui';
import { reduxForm, Field } from 'redux-form';

import SelectClients from './SelectClients';
import { renderField } from '../App/RenderField';
import { addTeam } from '../../actions/team';
import { getAllClients } from '../../actions/clients';

const validate = (values) => {
  const errors = {};
  let hasErrors = false;
  if (!values.name || values.name.trim() === '') {
    errors.name = 'Enter a team name';
    hasErrors = true;
  }

  if (!values.email || values.email.trim() === '') {
    errors.email = 'Enter a team email';
    hasErrors = true;
  }

  if (!values.password || values.password.trim() === '') {
    errors.password = 'Enter a team password';
    hasErrors = true;
  }

  if (!values.selectedClients?.length) {
    errors.selectedClients = 'Please select at least one client';
    hasErrors = true;
  }

  return hasErrors && errors;
};

class AddTeam extends Component {
  constructor(props) {
    super(props);
    this.state = {
      email: '',
      password: '',
      selectedClients: [],
      clients: [],
    };
    this.handleInputChange = this.handleInputChange.bind(this);
    this.handleFormSubmit = this.handleFormSubmit.bind(this);
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillMount() {
    this.proceedMetaInformation();
  }

  handleInputChange(event) {
    const { target } = event;
    const value = target.type === 'checkbox' ? target.checked : target.value;
    const { name } = target;
    console.log('setting: ', [name], value, this.state);
    this.setState({
      [name]: value,
    });
  }

  async handleFormSubmit(event) {
    event.preventDefault();
    this.blockUi();
    const { email, password, name, selectedClients } = this.state;
    const { alert, navigate } = this.props;
    const body = {
      email,
      password,
      name,
      selectedClients,
    };

    const [err] = await to(addTeam(body).payload);

    this.unBlockUi();

    if (err) {
      if (err?.response?.data?.message)
        alert('danger', 'Error', `Team not created: ${err.response.data.message.toString()}`);
      else alert('danger', 'Error', `Team not created: ${err.toString()}`);

      return;
    }

    alert('success', 'Success', 'Team created successfully');
    navigate('/teams');
  }

  async proceedMetaInformation() {
    this.blockUi();

    const clients = await getAllClients().payload;

    this.setState({
      clients: clients.data,
    });

    this.unBlockUi();
  }

  blockUi() {
    this.setState({
      blocking: true,
    });
  }

  unBlockUi() {
    this.setState({
      blocking: false,
    });
  }

  renderClientsSelect() {
    const { selectedClients, clients } = this.state;

    return (
      <FormGroup>
        <ControlLabel>Clients</ControlLabel>
        <SelectClients clients={clients} selectedClients={selectedClients} onUpdate={this.handleInputChange} />
      </FormGroup>
    );
  }

  render() {
    const { blocking } = this.state;

    return (
      <div className="container root-container">
        <BlockUi tag="div" blocking={blocking}>
          <Row>
            <Col md={10}>
              <PageHeader>Create New Team</PageHeader>
            </Col>
            <Col md={2}>
              <Button
                className="pull-right btn-info save-button"
                onClick={(e) => this.handleFormSubmit(e)}
                disabled={!!validate(this.state)}
              >
                Save
              </Button>
            </Col>
          </Row>
          <form onSubmit={this.handleFormSubmit}>
            <Row>
              <Col md={6}>
                <FormGroup>
                  <ControlLabel>Team Name</ControlLabel>
                  <Field
                    name="name"
                    type="text"
                    noMessage
                    component={renderField}
                    onChange={this.handleInputChange}
                    label="Enter Team Name*"
                  />
                </FormGroup>
              </Col>
              <Col md={6}>{this.renderClientsSelect()}</Col>
            </Row>
            <hr className="margin-top-5 margin-bottom-20" />
            <Row>
              <Col md={12}>
                <ControlLabel>Login</ControlLabel>
              </Col>
              <Col md={6}>
                <Field
                  name="email"
                  type="text"
                  noMessage
                  component={renderField}
                  onChange={this.handleInputChange}
                  label="Enter Team Name Email"
                />
              </Col>
              <Col md={6}>
                <Field
                  name="password"
                  type="password"
                  noMessage
                  component={renderField}
                  onChange={this.handleInputChange}
                  label="Enter Team Name Password"
                />
              </Col>
            </Row>
          </form>
        </BlockUi>
      </div>
    );
  }
}

export default reduxForm({
  form: 'AddTeam',
  validate,
})(AddTeam);
