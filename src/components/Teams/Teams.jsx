import React, { Component } from 'react';
import to from 'await-to-js';
import { PageHeader, InputGroup, FormControl, Button, Row, Col, Table, Pagination, FormGroup } from 'react-bootstrap';

import BlockUi from 'react-block-ui';

import { importExcel, listTeams, toggleDisabled } from '../../actions/team';
import { getAllClients } from '../../actions/clients';
import ResultLimit from '../App/ResultLimit';
import CONFIG from '../../config';

class Teams extends Component {
  constructor(props) {
    super(props);
    this.state = {
      query: '',
      showDisabled: false,
      currentPage: 1,
      totalItems: 0,
      limit: 20,
      offset: 0,
      sort: 'team_name',
      clientId: -1,
      ascending: true,
      teams: [],
      clients: [],
    };
    this.inputRef = React.createRef();
    this.pageEvent = this.pageEvent.bind(this);
    this.handleClientChange = this.handleClientChange.bind(this);
    this.onImport = this.onImport.bind(this);
  }

  componentDidMount() {
    this.list();
  }

  handleLimitChange(e) {
    this.setState(
      {
        limit: e.target.value,
        offset: 0,
        currentPage: 1,
      },
      () => {
        this.list();
      }
    );
  }

  handleClientChange(e) {
    this.setState(
      {
        currentPage: 1,
        offset: 0,
        clientId: e.target.value,
      },
      () => {
        this.list();
      }
    );
  }

  async onImport(e) {
    const { alert } = this.props;

    this.blockUi();

    const [err] = await to(importExcel(e.target.files[0]).payload);

    if (err) return alert('danger', 'Error', 'Something went wrong with importing clients');

    this.unBlockUi();

    window.location.reload();
  }

  updateQuery(key, value) {
    this.setState({
      [key]: value,
      currentPage: 1,
      offset: 0,
    });
  }

  sort(column) {
    this.setState(
      (prevState) => ({
        ascending: !prevState.ascending,
        sort: column,
        currentPage: 1,
        offset: 0,
      }),
      () => {
        this.list();
      }
    );
  }

  pageEvent(e) {
    const { limit } = this.state;
    console.log('setting offset: ', (e - 1) * limit);
    this.setState(
      {
        currentPage: e,
        offset: (e - 1) * limit,
      },
      () => {
        this.list();
      }
    );
  }

  list() {
    const { query, limit, offset, sort, ascending, showDisabled, clientId } = this.state;
    const { alert } = this.props;
    const q = {
      query,
      limit,
      offset,
      sort,
      ascending,
      showDisabled,
      clientId,
    };
    // console.log('querying :', q);
    this.blockUi();
    listTeams(q).payload.then(
      (results) => {
        console.log('got teams: ', results);
        this.setState({ teams: results.data });
        if (results.data.length)
          this.setState({
            totalItems: Math.ceil(results.data[0].total / limit),
          });

        this.unBlockUi();
      },
      (error) => {
        console.log(error);
        alert('danger', 'Error', `Error listing teams: ${error.toString()}`);
        this.unBlockUi();
      }
    );

    getAllClients().payload.then((res) => {
      this.setState({ clients: res.data });
    });
  }

  viewTeam(id) {
    const { navigate } = this.props;

    navigate(`/teams/id/${id}`);
  }

  blockUi() {
    this.setState({
      blocking: true,
    });
  }

  unBlockUi() {
    this.setState({
      blocking: false,
    });
  }

  toggleTeamDisable(team) {
    this.blockUi();
    toggleDisabled(team.id).payload.then(() => {
      this.list();
    });
  }

  toggleShowDisabled() {
    const { showDisabled } = this.state;

    this.setState(
      {
        showDisabled: !showDisabled,
      },
      () => {
        this.list();
      }
    );
  }

  renderButtons(team) {
    let disable;
    if (team.disabled)
      disable = (
        <Button bsSize="xsmall" bsStyle="info" onClick={() => this.toggleTeamDisable(team)}>
          Enable
        </Button>
      );
    else
      disable = (
        <Button bsSize="xsmall" onClick={() => this.toggleTeamDisable(team)}>
          Disable
        </Button>
      );

    return (
      <td className="text-right">
        <Button bsSize="xsmall" onClick={() => this.viewTeam(team.id)} style={{ marginRight: '10px' }}>
          View
        </Button>
        {disable}
      </td>
    );
  }

  renderTeamsList() {
    const { teams } = this.state;
    return (
      <tbody>
        {teams.map((team, i) => (
          <tr key={i}>
            <td>{team.name}</td>
            <td className="text-right">{team.created}</td>
            {this.renderButtons(team)}
          </tr>
        ))}
      </tbody>
    );
  }

  renderClientsSelect() {
    const { clients, clientId } = this.state;
    return (
      <FormGroup>
        <FormControl
          value={clientId}
          componentClass="select"
          placeholder="Select Client"
          onChange={this.handleClientChange}
        >
          <option value={0}>Select client...</option>
          {clients.map((client) => (
            <option key={client.id} value={client.id}>
              {client.name}
            </option>
          ))}
        </FormControl>
      </FormGroup>
    );
  }

  render() {
    const { showDisabled, blocking, totalItems, currentPage } = this.state;
    const { navigate } = this.props;
    return (
      <div className="container root-container">
        <input
          ref={this.inputRef}
          id="import-teams"
          className="form-control"
          type="file"
          accept=".csv"
          onChange={this.onImport}
          style={{ height: 0, visibility: 'hidden', padding: 0 }}
        />
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Col md={10}>
            <PageHeader>Teams</PageHeader>
          </Col>
          <div style={{ width: '100%' }}>
            <a href={CONFIG.WEB_URL} className="btn btn-info pull-right" target="_blank" rel="noreferrer noopener">
              Team Web Site
            </a>
          </div>
        </div>
        <Row>
          <Col lg={2} md={2} xs={6}>
            <ResultLimit onChange={(e) => this.handleLimitChange(e)} />
          </Col>
          <Col lg={3} md={3} xs={6}>
            <InputGroup>
              <FormControl
                type="text"
                placeholder="Search"
                name="query"
                onChange={(e) => this.updateQuery('query', e.target.value)}
              />
              <InputGroup.Button>
                <Button type="submit" className="pull-right" name="list" onClick={() => this.list()}>
                  Go
                </Button>
              </InputGroup.Button>
            </InputGroup>
          </Col>
          <Col lg={3} md={3} xs={12}>
            {this.renderClientsSelect()}
          </Col>
          <Col lg={4} md={4} xs={12}>
            <Button className="pull-right" bsStyle="primary" onClick={() => this.inputRef.current.click()}>
              Import Teams
            </Button>
            <Button
              className="pull-right"
              bsStyle="primary"
              onClick={() => navigate('/teams/add')}
              style={{ marginRight: '10px' }}
            >
              Add Team
            </Button>
            <Button
              className="pull-right"
              bsStyle={showDisabled ? 'success' : 'default'}
              onClick={() => this.toggleShowDisabled()}
              style={{ marginRight: '10px' }}
            >
              Show Disabled
            </Button>
          </Col>
        </Row>
        <br />
        <Row>
          <Col md={12}>
            <BlockUi tag="div" blocking={blocking}>
              <Table striped bordered hover>
                <thead className="thead-light">
                  <tr>
                    <th role="button" onClick={() => this.sort('team_name')}>
                      Team Name
                      <i className="glyphicon glyphicon-sort" style={{ marginLeft: '5px' }} />
                    </th>
                    <th className="text-right" role="button" onClick={() => this.sort('team_created_at')}>
                      Created At
                      <i className="glyphicon glyphicon-sort" style={{ marginLeft: '5px' }} />
                    </th>
                    <th className="text-right">Actions</th>
                  </tr>
                </thead>
                {this.renderTeamsList()}
              </Table>
            </BlockUi>
          </Col>
        </Row>
        <Row>
          <Col md={12}>
            <Pagination
              className="pull-right"
              prev
              next
              first
              last
              boundaryLinks
              items={totalItems}
              maxButtons={5}
              activePage={currentPage}
              onSelect={this.pageEvent}
            />
          </Col>
        </Row>
      </div>
    );
  }
}

export default Teams;
