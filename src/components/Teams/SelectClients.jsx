import React, { useMemo } from 'react';
import Select from 'react-select';

export default function SelectClients(props) {
  const { clients = [], selectedClients = [], onUpdate = () => {} } = props;
  const modifiedClients = useMemo(
    () =>
      clients.reduce((acc, curr) => {
        const { id, name } = curr;

        acc.push({
          value: id,
          label: name,
        });

        return acc;
      }, []),
    [clients]
  );
  const modifiedSelectedClients = useMemo(() => {
    if (!modifiedClients.length) return;

    const mappedClients = [];

    selectedClients.forEach((selectedClient) => {
      const relatedClient = modifiedClients.find(({ value }) => value === selectedClient);

      if (relatedClient) mappedClients.push(relatedClient);
    });

    return mappedClients;
  }, [modifiedClients, selectedClients]);

  function changeSelectedClients(e) {
    if (!e.length) return onUpdate({ target: { name: 'selectedClients', value: [] } });

    const selectedClientIds = e.map(({ value }) => value);

    onUpdate({ target: { name: 'selectedClients', value: selectedClientIds } });
  }

  return (
    <Select
      name="selectedClients"
      options={modifiedClients}
      value={modifiedSelectedClients}
      isMulti
      onChange={changeSelectedClients}
    />
  );
}
