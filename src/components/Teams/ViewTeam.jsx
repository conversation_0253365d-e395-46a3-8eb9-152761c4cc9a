import React, { useState, useEffect } from 'react';
import to from 'await-to-js';
import { PageHeader, FormControl, Button, Row, Col, FormGroup, ControlLabel } from 'react-bootstrap';
import { reduxForm, Field } from 'redux-form';
import BlockUi from 'react-block-ui';

import SelectClients from './SelectClients';
import ResetModal from './ResetModal';
import { renderField } from '../App/RenderField';
import {
  updateTeam,
  getTeam,
  resetTeamHistory,
  exportSelfAssessmentPdf,
  getTeamDecisionResults,
} from '../../actions/team';
import { getAllClients } from '../../actions/clients';

const validate = (values) => {
  const errors = {};
  let hasErrors = false;

  if (!values.name || values.name.trim() === '') {
    errors.name = 'Enter a team name';
    hasErrors = true;
  }

  if (!values.email || values.email.trim() === '') {
    errors.email = 'Enter a team email';
    hasErrors = true;
  }

  if (!values.selectedClients?.length) {
    errors.selectedClients = 'Please select at least one client';
    hasErrors = true;
  }

  return hasErrors && errors;
};

const ViewTeam = (props) => {
  const [state, setState] = useState({
    name: '',
    email: '',
    password: '',
    selectedClients: [],
    clients: [],
    isResetModalOpen: false,
    isReportButtonDisabled: false,
    blocking: false,
    id: null,
  });

  useEffect(() => {
    blockUi();
    const {
      params: { teamId },
      destroy,
      initialize,
    } = props;

    getTeam(teamId).payload.then((result) => {
      destroy();
      initialize(result.data);

      console.log('got team: ', result);
      const { data } = result;
      setState((prevState) => ({
        ...prevState,
        ...data,
        isReportButtonDisabled: !data.isCompletedSelfAssessment,
      }));

      unBlockUi();
    });

    proceedMetaInformation();
  }, []); // Empty dependency array to mimic componentWillMount

  const handleInputChange = (event) => {
    const { target } = event;
    const value = target.type === 'checkbox' ? target.checked : target.value;
    const { name } = target;
    setState((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handleFormSubmit = async (event) => {
    event.preventDefault();
    blockUi();
    const { id, email, password, name, selectedClients } = state;
    const { navigate, alert } = props;
    const body = {
      id,
      email,
      password,
      name,
      selectedClients,
    };

    const [err] = await to(updateTeam(body).payload);

    unBlockUi();

    if (err) {
      if (err?.response?.data?.message)
        alert('danger', 'Error', `Team not updated: ${err.response.data.message.toString()}`);
      else alert('danger', 'Error', `Team not updated: ${err.toString()}`);

      return;
    }

    alert('success', 'Success', 'Team updated successfully');
    navigate('/teams');
  };

  const downloadReportHandler = async () => {
    blockUi();
    const { alert } = props;

    const [err, res] = await to(
      exportSelfAssessmentPdf({ teamId: state?.id, clientId: state.selectedClients?.[0] }).payload
    );

    unBlockUi();

    if (err) {
      const textDecoder = new TextDecoder('utf-8');
      const jsonString = textDecoder.decode(err.response.data);
      const errorObject = JSON.parse(jsonString);

      return alert('danger', 'Error', errorObject.message);
    }
    const { name } = state;
    const blob = new Blob([res.data], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const anchor = document.createElement('a');
    anchor.href = url;
    anchor.download = `${name} Self Assessment.pdf`;
    anchor.click();

    alert('success', 'Success', 'Report was successfully downloaded');
  };

  const downloadDecisionResultsHandler = async () => {
    blockUi();
    const { alert } = props;

    const [err, res] = await to(getTeamDecisionResults(state.id).payload);

    unBlockUi();

    if (err) {
      return alert('danger', 'Error', 'Failed to download decision results');
    }

    if (!res.data || !res.data.length) {
      return alert('warning', 'No Results', 'No decision results found for this team');
    }

    const blob = new Blob([JSON.stringify(res.data, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const anchor = document.createElement('a');
    anchor.href = url;
    anchor.download = `${state.name} Decision Results.json`;
    anchor.click();

    alert('success', 'Success', 'Decision results were successfully downloaded');
  };

  const proceedMetaInformation = async () => {
    blockUi();

    const clients = await getAllClients().payload;

    setState((prevState) => ({
      ...prevState,
      clients: clients.data,
    }));

    unBlockUi();
  };

  const resetHistory = async () => {
    blockUi();

    const {
      params: { teamId },
    } = props;

    await resetTeamHistory(teamId).payload;

    setState((prevState) => ({
      ...prevState,
      isResetModalOpen: false,
    }));

    unBlockUi();
  };

  const blockUi = () => {
    setState((prevState) => ({
      ...prevState,
      blocking: true,
    }));
  };

  const unBlockUi = () => {
    setState((prevState) => ({
      ...prevState,
      blocking: false,
    }));
  };
  console.log({ state });

  const renderClientsSelect = () => {
    const { selectedClients, clients } = state;

    return (
      <FormGroup>
        <ControlLabel>Clients</ControlLabel>
        <SelectClients clients={clients} selectedClients={selectedClients} onUpdate={handleInputChange} />
      </FormGroup>
    );
  };

  const { name, blocking, isResetModalOpen } = state;

  return (
    <>
      <div className="container root-container">
        <BlockUi tag="div" blocking={blocking}>
          <Row>
            <Col md={8}>
              <PageHeader>{name} Stats</PageHeader>
            </Col>
            <Col md={4}>
              <Button
                className="pull-right btn-info save-button"
                onClick={(e) => handleFormSubmit(e)}
                disabled={!!validate(state)}
                style={{ marginLeft: '10px' }}
              >
                Save
              </Button>
              <Button
                className="btn save-button pull-right"
                onClick={() => setState((prevState) => ({ ...prevState, isResetModalOpen: true }))}
                style={{ marginLeft: '10px' }}
              >
                Reset
              </Button>

              <Button
                className="pull-right btn-info save-button"
                onClick={() => downloadReportHandler()}
                disabled={state.isReportButtonDisabled}
                style={{ marginLeft: '10px' }}
              >
                Download Report
              </Button>
              <Button
                className="pull-right btn-info save-button"
                onClick={() => downloadDecisionResultsHandler()}
                disabled={!state.selectedClients?.length}
                style={{ marginLeft: '10px' }}
              >
                Download Decision Results
              </Button>
            </Col>
          </Row>
          <form onSubmit={handleFormSubmit}>
            <Row>
              <Col md={6}>
                <FormGroup>
                  <ControlLabel>Team Name</ControlLabel>
                  <Field
                    name="name"
                    type="text"
                    component={renderField}
                    onChange={handleInputChange}
                    label="Enter Team Name*"
                  />
                </FormGroup>
              </Col>
              <Col md={6}>{renderClientsSelect()}</Col>
            </Row>
            <hr className="margin-top-5 margin-bottom-20" />
            <Row>
              <Col md={12}>
                <ControlLabel>Login</ControlLabel>
              </Col>
              <Col md={6}>
                <Field
                  name="email"
                  type="text"
                  noMessage
                  component={renderField}
                  onChange={handleInputChange}
                  label="Enter Team Name Email"
                />
              </Col>
              <Col md={6}>
                <FormControl name="password" type="password" placeholder="Password" onChange={handleInputChange} />
              </Col>
            </Row>
          </form>
        </BlockUi>
      </div>

      {isResetModalOpen ? (
        <ResetModal
          onUpdate={(e) =>
            e
              ? resetHistory()
              : setState((prevState) => ({
                  ...prevState,
                  isResetModalOpen: false,
                }))
          }
        />
      ) : null}
    </>
  );
};

export default reduxForm({
  form: 'ViewTeam',
  validate,
})(ViewTeam);
