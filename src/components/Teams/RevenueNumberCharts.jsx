import React, { Component } from 'react';
import { Row, Col } from 'react-bootstrap';
import _ from 'lodash';
import { BarChart, Bar, XAxis, YAxis, Cell } from 'recharts';

class RevenueNumberChart extends Component {
  getBorderClass(i) {
    if (i === 0) return 'chart-col-0';
    if (i === 2) return 'border-left chart-col-2';

    return 'chart-col-1';
  }

  getChartBackground(i, j) {
    if ((i + j + 2) % 2) return 'background-grayer';

    return 'background-gray';
  }

  getChartWidth(i) {
    if (i === 2) return 350;

    return 350;
  }

  getHeaderBarColor(i, j) {
    if ((i + j + 2) % 2) return 'header-bar-color-1';

    return 'header-bar-color-2';
  }

  getChartHeight(i) {
    if (i === 2) return 220;

    return 200;
  }

  getChartHeightClass(i) {
    if (i === 2) return 'height-220';

    return 'height-200';
  }

  render() {
    const { revenueNumbers } = this.props;
    const rows = _.chunk(revenueNumbers, 3);
    return (
      <div>
        {rows.map((row, j) => (
          // eslint-disable-next-line react/no-array-index-key
          <Row key={j}>
            {row.map((number, i) => {
              const data = [
                {
                  name: 'Your Company',
                  value: number.companyValue,
                  planned: number.plannedValue - number.companyValue,
                },
                {
                  name: 'Industry Average',
                  value: number.industryValue,
                  planned: 0,
                },
              ];
              return (
                // eslint-disable-next-line react/no-array-index-key
                <Col md={4} key={i} className={`${this.getBorderClass(i)}`}>
                  <div className={`${this.getChartBackground(i, j)} border margin-bottom-0`}>
                    <Row className="margin-bottom-15 small-text">
                      <Col md={12} className="text-center">
                        <h4 className={this.getHeaderBarColor(i, j)}>{number.name}</h4>
                      </Col>
                    </Row>
                    <div align="center">
                      <div className={this.getChartHeightClass(i)}>
                        <BarChart width={this.getChartWidth(i)} height={this.getChartHeight(i)} data={data}>
                          <YAxis stroke="#252525" width={40} />
                          <XAxis dataKey="name" stroke="#252525" />
                          <Bar dataKey="value" stackId="a">
                            {data.map((entry, index) => (
                              // eslint-disable-next-line react/no-array-index-key
                              <Cell fill={index % 2 ? '#004864' : '#00A3E3'} key={index} />
                            ))}
                          </Bar>
                          <Bar dataKey="planned" stackId="a" fill="white" />
                        </BarChart>
                      </div>
                    </div>
                    <div className="plan-number">
                      Plan:{' '}
                      <b>
                        <span className="large-text">{number.plannedValue}</span>
                      </b>
                    </div>
                  </div>
                </Col>
              );
            })}
          </Row>
        ))}
      </div>
    );
  }
}

export default RevenueNumberChart;
