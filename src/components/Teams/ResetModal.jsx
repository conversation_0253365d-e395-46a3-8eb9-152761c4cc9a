import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';

import styles from './reset-modal.module.scss';

export default function ResetModal(props) {
  const { onUpdate = () => {} } = props;

  return (
    <Modal show onHide={() => onUpdate(false)} bsSize="sm">
      <ModalHeader closeButton>
        <ModalTitle>Reset History</ModalTitle>
      </ModalHeader>
      <ModalBody>
        <p style={{ textAlign: 'center' }}>
          Are your sure? Resetting the user’s history will erase all progress they have made, in all clients they have
          signed up, or been assigned to.
        </p>
      </ModalBody>
      <ModalFooter>
        <div className={styles.buttonsWrapper}>
          <Button className="btn-danger" onClick={() => onUpdate(false)}>
            Cancel
          </Button>

          <Button className="btn-info save-button" onClick={() => onUpdate(true)}>
            Proceed
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  );
}
