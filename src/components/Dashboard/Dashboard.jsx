import React, { Component } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Row, Col, Well } from 'react-bootstrap';

import BlockUi from 'react-block-ui';

import RevenueNumberCharts from './RevenueNumberCharts';
// import MetricCharts from './MetricCharts';
import { getDashboard } from '../../actions/user';

class Dashboard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      blocking: false,
      revenueNumbers: [],
      metrics: [],
    };
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillMount() {
    this.blockUi();
    getDashboard().payload.then((result) => {
      this.unBlockUi();
      console.log('got dashboard: ', result);
      this.setState({
        ...result.data,
      });
    });
  }

  numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  blockUi() {
    this.setState({
      blocking: true,
    });
  }

  unBlockUi() {
    this.setState({
      blocking: false,
    });
  }

  renderRevenueNumbers() {
    const { revenueNumbers } = this.state;
    return (
      <Row>
        {revenueNumbers.map((number, i) => (
          <Col md={4} key={i}>
            <Well bsSize="small">
              {number.name}: ${this.numberWithCommas(number.companyValue)}
            </Well>
          </Col>
        ))}
      </Row>
    );
  }

  renderMetrics() {
    const { metrics } = this.state;
    return (
      <Row>
        {metrics.map((metric, i) => (
          <Col md={4} key={i}>
            <Well bsSize="small">
              {metric.name}: {metric.companyValue}%
            </Well>
          </Col>
        ))}
      </Row>
    );
  }

  render() {
    const { blocking, revenueNumbers } = this.state;
    return (
      <div className="container root-container">
        <BlockUi tag="div" blocking={blocking}>
          <PageHeader>Financials</PageHeader>
          {this.renderRevenueNumbers()}
          <hr className="no-margin margin-bottom-20" />
          <RevenueNumberCharts revenueNumbers={revenueNumbers} />
          <hr className="no-margin margin-bottom-20" />

          {/* <PageHeader>Key Metrics</PageHeader> */}
          {/* {this.renderMetrics()} */}
          {/* <hr className="no-margin margin-bottom-20"/> */}

          {/* <MetricCharts metrics={metrics}/> */}
        </BlockUi>
      </div>
    );
  }
}

export default Dashboard;
