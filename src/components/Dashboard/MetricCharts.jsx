import React, { PureComponent } from 'react';
import { Row, Col } from 'react-bootstrap';
import { Pie<PERSON><PERSON>, Pie, Label } from 'recharts';

class MetricCharts extends PureComponent {
  render() {
    const { metrics } = this.props;

    return (
      <Row>
        {metrics.map((metric, i) => (
          <Col md={6} align="center" key={i}>
            <Row>
              <Col md={12} className="margin-top-15 margin-bottom-15">
                <b>{metric.name}</b>
              </Col>
              <Col md={6} className="metric-div-1">
                <div className="background-metric-1">
                  <PieChart width={270} height={230} key={`metric-${i}`}>
                    <Pie
                      data={[
                        {
                          name: 'Company Value',
                          value: parseInt(metric.companyValue, 10),
                          fill: '#00A3E3',
                        },
                        {
                          name: 'Industry Average',
                          value: parseInt(100 - metric.companyValue, 10),
                          fill: '#004864',
                        },
                      ]}
                      cx={130}
                      cy={120}
                      stroke="none"
                      dataKey="value"
                      innerRadius={50}
                      outerRadius={80}
                      startAngle={90}
                      endAngle={-270}
                      fill="#8884d8"
                    >
                      <Label width={50} position="center" className="metric-chart-label">
                        {`${parseInt(metric.companyValue, 10)}%`}
                      </Label>
                    </Pie>
                  </PieChart>
                  Your Company
                </div>
              </Col>
              <Col md={6} className="metric-div-2">
                <div className="background-metric-2">
                  <PieChart width={270} height={230} key={`metric-${2 * i + 10}`}>
                    <Pie
                      data={[
                        {
                          name: 'Industry Average',
                          value: parseInt(metric.industryValue, 10),
                          fill: '#00A3E3',
                        },
                        {
                          name: 'Company Value',
                          value: parseInt(100 - metric.industryValue, 10),
                          fill: '#004864',
                        },
                      ]}
                      cx={130}
                      cy={120}
                      stroke="none"
                      dataKey="value"
                      innerRadius={50}
                      outerRadius={80}
                      startAngle={90}
                      endAngle={-270}
                      fill="#8884d8"
                    >
                      <Label width={50} position="center" className="metric-chart-label">
                        {`${parseInt(metric.industryValue, 10)}%`}
                      </Label>
                    </Pie>
                  </PieChart>
                  Industry Average
                </div>
              </Col>
            </Row>
          </Col>
        ))}
      </Row>
    );
  }
}

export default MetricCharts;
