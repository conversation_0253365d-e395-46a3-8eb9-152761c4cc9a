import {
  useNavigate,
  useLocation,
  useMatch,
  useParams,
} from "react-router-dom";

export function withReactRouterHooks(Component) {
  return function WrappedComponent(props) {
    const navigate = useNavigate();
    const location = useLocation();
    const params = useParams();

    return (
      <Component
        {...props}
        navigate={navigate}
        location={location}
        params={params}
      />
    );
  };
}
