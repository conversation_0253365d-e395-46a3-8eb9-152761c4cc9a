import React from 'react';

import { Button, FormGroup } from 'react-bootstrap';

import styles from './leaderboard-users.module.scss';

export default function LeaderboardUsers(props) {
  const { users = [], onUpdate = () => {}, onRemove = () => {} } = props;

  return (
    <div>
      {users.map((user, index) => (
        <div key={user.id} className={styles.wrapper}>
          <FormGroup>
            <input
              className="form-control"
              type="text"
              name="user-name"
              value={user.name}
              onChange={(e) => onUpdate('name', e.target.value, user)}
              placeholder="First and Last Name"
            />
          </FormGroup>
          <FormGroup>
            <input
              className="form-control"
              type="number"
              name="user-points"
              value={user.points}
              onChange={(e) => onUpdate('points', e.target.value, user)}
              placeholder="Points Value"
            />
          </FormGroup>

          {index !== 0 ? (
            <Button bsStyle="danger" onClick={() => onRemove(user)}>
              X
            </Button>
          ) : null}
        </div>
      ))}
    </div>
  );
}
