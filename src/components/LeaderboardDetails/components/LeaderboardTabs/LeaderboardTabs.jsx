import React from 'react';
import { Col, FormGroup, Row, Tab, Tabs } from 'react-bootstrap';

export default function LeaderboardTabs(props) {
  const { regions, selectedRegionId, onUpdate = () => {}, setSelectedRegionId = () => {} } = props;

  return (
    <Tabs onSelect={(e) => setSelectedRegionId(e)} activeKey={selectedRegionId} id="tabs-container">
      {regions.map((region, i) => (
        <Tab eventKey={region.id} title={`Leaderboard #${i + 1}`} key={region.id}>
          <Row>
            <Col md={12}>
              <FormGroup>
                <input
                  className="form-control"
                  type="text"
                  name="region-name"
                  value={region.name}
                  onChange={(e) => onUpdate('name', e.target.value, region)}
                  placeholder={'Header Tab Name & Title (e.g. "Global")'}
                />
              </FormGroup>
            </Col>
          </Row>
        </Tab>
      ))}
    </Tabs>
  );
}
