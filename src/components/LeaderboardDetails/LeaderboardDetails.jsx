import React, { useState, useEffect, useMemo } from 'react';
import BlockUi from 'react-block-ui';
import to from 'await-to-js';
import { <PERSON><PERSON>, Col, ControlLabel, FormGroup, PageHeader, Row } from 'react-bootstrap';

import LeaderboardTabs from './components/LeaderboardTabs/LeaderboardTabs';
import LeaderboardUsers from './components/LeaderboardUsers/LeaderboardUsers';
import { createLeaderboard, updateLeaderboard, getLeaderboard } from '../../actions/leaderboards';

import styles from './leaderboard-details.module.scss';

export default function LeaderboardDetails(props) {
  const [name, setName] = useState('');
  const [regions, setRegions] = useState([{ id: 1, name: '', users: [{ id: 1, name: '', points: 0 }] }]);
  const [selectedRegionId, setSelectedRegionId] = useState(-1);
  const [isLoading, setLoading] = useState(false);
  const { params, alert, navigate } = props;
  const [isNewLeaderboard, setIsNewLeaderboard] = useState(params.leaderboardId === 'new');
  const selectedRegion = useMemo(() => regions.find(({ id }) => id === selectedRegionId), [regions, selectedRegionId]);

  useEffect(() => {
    const isNew = params.leaderboardId === 'new';

    if (isNew) {
      setSelectedRegionId(regions[0].id);
      return;
    }

    setIsNewLeaderboard(false);
    proceedLeaderboardDetails(params.leaderboardId);
  }, [isNewLeaderboard, params.leaderboardId]);

  async function proceedLeaderboardDetails(leaderboardId) {
    setLoading(true);

    const [err, res] = await to(getLeaderboard(leaderboardId).payload);

    setLoading(false);

    if (err) return alert('danger', 'Error', 'Oops, something went wrong with fetching leaderboard details');

    const { name, regions } = res.data;

    setName(name);
    setRegions(regions);

    if (regions.length) setSelectedRegionId(regions[0].id);
  }

  async function saveLeaderboard() {
    if (!validate()) return;

    setLoading(true);

    const payload = {
      name,
      regions,
    };

    let err;
    let res;

    if (!isNewLeaderboard) [err, res] = await to(updateLeaderboard({ ...payload, id: params.leaderboardId }).payload);
    else [err, res] = await to(createLeaderboard(payload).payload);

    setLoading(false);

    if (err) return alert('danger', 'Error', 'Oops, something went wrong with saving leaderboard details');

    alert('success', 'Success', `Leaderboard was successfully ${isNewLeaderboard ? 'created' : 'updated'}`);

    if (isNewLeaderboard) {
      const { id } = res.data;

      navigate(`/leaderboards/${id}`, { replace: true });
    }
  }

  function validate() {
    if (!name.trim()) {
      alert('danger', 'Error', 'Name is required field');
      return false;
    }

    for (let i = 0, n = regions.length; i < n; ++i) {
      const { name, users } = regions[i];

      if (!name) {
        alert('danger', 'Error', 'Name is required field for the region');
        return false;
      }

      for (let j = 0, k = users.length; j < k; ++j) {
        const { name } = users[j];

        if (!name) {
          alert('danger', 'Error', 'Name is required field for the leaderboard user');
          return false;
        }
      }
    }

    return true;
  }

  function updateRegions(key, value, region) {
    return setRegions((prev) =>
      prev.map((e) => {
        if (e.id !== region.id) return e;

        return {
          ...e,
          [key]: value,
        };
      })
    );
  }

  function addRegion() {
    if (regions.length >= 6) return;

    setRegions((prev) => [
      ...prev,
      {
        id: +new Date(),
        name: '',
        users: [{ id: 1, name: '', points: 0 }],
      },
    ]);
  }

  function deleteRegion() {
    const removalIndex = regions.findIndex(({ id }) => id === selectedRegionId);

    if (removalIndex === 0) setSelectedRegionId(regions[1].id);
    else setSelectedRegionId(regions[removalIndex - 1].id);

    setRegions((prev) => prev.filter(({ id }) => id !== selectedRegionId));
  }

  function addUser() {
    if (selectedRegion.users.length === 10) return;

    setRegions((prev) =>
      prev.map((region) => {
        if (selectedRegionId !== region.id) return region;

        return {
          ...region,
          users: [...region.users, { id: +new Date(), name: '', points: 0 }],
        };
      })
    );
  }

  function updateUser(key, value, user) {
    setRegions((prev) =>
      prev.map((region) => {
        if (selectedRegionId !== region.id) return region;

        return {
          ...region,
          users: region.users.map((u) => {
            if (u.id !== user.id) return u;

            return {
              ...u,
              [key]: value,
            };
          }),
        };
      })
    );
  }

  function removeUser(user) {
    setRegions((prev) =>
      prev.map((region) => {
        if (selectedRegionId !== region.id) return region;

        return {
          ...region,
          users: region.users.filter(({ id }) => id !== user.id),
        };
      })
    );
  }

  return (
    <div className="container root-container">
      <BlockUi tag="div" blocking={isLoading}>
        <Row>
          <Col md={10}>
            <PageHeader>{isNewLeaderboard ? 'Create New Leaderboard Scheme' : 'Update Leaderboard Scheme'}</PageHeader>
          </Col>
          <Col md={2}>
            <Button className="btn-info save-button pull-right" onClick={saveLeaderboard}>
              {isNewLeaderboard ? 'Create' : 'Save'}
            </Button>
          </Col>
        </Row>

        <form onSubmit={saveLeaderboard}>
          <Row>
            <Col md={6}>
              <FormGroup>
                <ControlLabel>Leaderboard Scheme Name*</ControlLabel>
                <input
                  className="form-control"
                  type="text"
                  name="leaderboard-name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter leaderboard scheme's name"
                />
              </FormGroup>
            </Col>
          </Row>

          <hr className="margin-top-5 margin-bottom-20" />
          <div className={styles.flexRow}>
            <h3 className={styles.header}>Leaderboards</h3>
            <div>
              {regions.length > 1 ? (
                <Button bsStyle="danger" style={{ marginRight: '10px' }} onClick={deleteRegion}>
                  Delete Leaderboard
                </Button>
              ) : null}

              <Button bsStyle="success" onClick={addRegion} disabled={regions.length === 6}>
                Add Leaderboard
              </Button>
            </div>
          </div>
          <Row>
            <Col md={12}>
              <LeaderboardTabs
                regions={regions}
                onUpdate={updateRegions}
                selectedRegionId={selectedRegionId}
                setSelectedRegionId={setSelectedRegionId}
              />
            </Col>
          </Row>

          {selectedRegion ? (
            <>
              <hr className="margin-top-5 margin-bottom-20" />
              <div className={styles.flexRow}>
                <h3 className={styles.header}>Users</h3>
                <div>
                  <Button bsStyle="success" onClick={addUser} disabled={selectedRegion.users.length === 10}>
                    Add User
                  </Button>
                </div>
              </div>
              <Row>
                <Col md={12}>
                  <LeaderboardUsers users={selectedRegion.users} onUpdate={updateUser} onRemove={removeUser} />
                </Col>
              </Row>
            </>
          ) : null}
        </form>
      </BlockUi>
    </div>
  );
}
