import React, { Component } from 'react';
import { PageHeader, InputGroup, FormControl, Button, Row, Col, Table, Pagination } from 'react-bootstrap';
import BlockUi from 'react-block-ui';

import ResultLimit from '../App/ResultLimit';
import { getLeaderboards, toggleDisabled } from '../../actions/leaderboards';

class Leaderboards extends Component {
  constructor(props) {
    super(props);
    this.state = {
      query: '',
      showDisabled: false,
      currentPage: 1,
      totalItems: 0,
      limit: 20,
      offset: 0,
      sort: 'name',
      ascending: true,
      leaderboards: [],
    };

    this.pageEvent = this.pageEvent.bind(this);
  }

  componentDidMount() {
    this.proceedLeaderboards();
  }

  handleLimitChange(e) {
    this.setState(
      {
        limit: e.target.value,
        offset: 0,
        currentPage: 1,
      },
      () => {
        this.proceedLeaderboards();
      }
    );
  }

  updateQuery(key, value) {
    this.setState({
      [key]: value,
      currentPage: 1,
      offset: 0,
    });
  }

  sort(column) {
    this.setState(
      (prevState) => ({
        ascending: !prevState.ascending,
        sort: column,
        currentPage: 1,
        offset: 0,
      }),
      () => {
        this.proceedLeaderboards();
      }
    );
  }

  pageEvent(e) {
    const { limit } = this.state;
    console.log('setting offset: ', (e - 1) * limit);
    this.setState(
      {
        currentPage: e,
        offset: (e - 1) * limit,
      },
      () => {
        this.proceedLeaderboards();
      }
    );
  }

  proceedLeaderboards() {
    const { query, limit, offset, sort, ascending, showDisabled } = this.state;
    const { alert } = this.props;
    const q = {
      query,
      limit,
      offset,
      sort,
      ascending,
      showDisabled,
    };

    this.blockUi();

    getLeaderboards(q).payload.then(
      (results) => {
        console.log('got leaderboards: ', results);
        this.setState({ leaderboards: results.data });

        if (results.data.length)
          this.setState({
            totalItems: Math.ceil(results.data[0].total / limit),
          });

        this.unBlockUi();
      },
      (error) => {
        console.log(error);
        alert('danger', 'Error', `Error listing leaderboards: ${error.toString()}`);
        this.unBlockUi();
      }
    );
  }

  viewLeaderboard(id) {
    const { navigate } = this.props;

    navigate(`/leaderboards/${id}`);
  }

  blockUi() {
    this.setState({
      blocking: true,
    });
  }

  unBlockUi() {
    this.setState({
      blocking: false,
    });
  }

  toggleLeaderboardDisable(leaderboard) {
    this.blockUi();
    toggleDisabled(leaderboard.id).payload.then(() => {
      this.proceedLeaderboards();
    });
  }

  toggleShowDisabled() {
    const { showDisabled } = this.state;

    this.setState(
      {
        showDisabled: !showDisabled,
      },
      () => {
        this.proceedLeaderboards();
      }
    );
  }

  renderButtons(leaderboard) {
    let disable;

    if (leaderboard.disabled)
      disable = (
        <Button bsSize="xsmall" bsStyle="info" onClick={() => this.toggleLeaderboardDisable(leaderboard)}>
          Enable
        </Button>
      );
    else
      disable = (
        <Button bsSize="xsmall" onClick={() => this.toggleLeaderboardDisable(leaderboard)}>
          Disable
        </Button>
      );

    return (
      <td className="text-right">
        <Button bsSize="xsmall" onClick={() => this.viewLeaderboard(leaderboard.id)} style={{ marginRight: '10px' }}>
          View
        </Button>
        {disable}
      </td>
    );
  }

  renderLeaderboardsList() {
    const { leaderboards } = this.state;
    return (
      <tbody>
        {leaderboards.map((leaderboard) => (
          <tr key={leaderboard.id}>
            <td>{leaderboard.name}</td>
            <td className="text-right">{leaderboard.created_at}</td>
            {this.renderButtons(leaderboard)}
          </tr>
        ))}
      </tbody>
    );
  }

  render() {
    const { showDisabled, blocking, totalItems, currentPage } = this.state;
    const { navigate } = this.props;
    return (
      <div className="container root-container">
        <PageHeader>Leaderboards</PageHeader>
        <Row>
          <Col lg={2} md={2} xs={6}>
            <ResultLimit onChange={(e) => this.handleLimitChange(e)} />
          </Col>
          <Col lg={4} md={4} xs={6}>
            <InputGroup>
              <FormControl
                type="text"
                placeholder="Search"
                name="query"
                onChange={(e) => this.updateQuery('query', e.target.value)}
              />
              <InputGroup.Button>
                <Button type="submit" className="pull-right" name="list" onClick={() => this.proceedLeaderboards()}>
                  Go
                </Button>
              </InputGroup.Button>
            </InputGroup>
          </Col>
          <Col lg={6} md={6} xs={12}>
            <Button className="pull-right" bsStyle="primary" onClick={() => navigate('/leaderboards/new')}>
              Add Leaderboard
            </Button>
            <Button
              className="pull-right"
              bsStyle={showDisabled ? 'success' : 'default'}
              onClick={() => this.toggleShowDisabled()}
              style={{ marginRight: '10px' }}
            >
              Show Disabled
            </Button>
          </Col>
        </Row>
        <br />
        <Row>
          <Col md={12}>
            <BlockUi tag="div" blocking={blocking}>
              <Table striped bordered hover>
                <thead className="thead-light">
                  <tr>
                    <th role="button" onClick={() => this.sort('name')}>
                      Leaderboard Name
                      <i className="glyphicon glyphicon-sort" style={{ marginLeft: '5px' }} />
                    </th>
                    <th className="text-right" role="button" onClick={() => this.sort('created_at')}>
                      Created At
                      <i className="glyphicon glyphicon-sort" style={{ marginLeft: '5px' }} />
                    </th>
                    <th className="text-right">Actions</th>
                  </tr>
                </thead>
                {this.renderLeaderboardsList()}
              </Table>
            </BlockUi>
          </Col>
        </Row>
        <Row>
          <Col md={12}>
            <Pagination
              className="pull-right"
              prev
              next
              first
              last
              boundaryLinks
              items={totalItems}
              maxButtons={5}
              activePage={currentPage}
              onSelect={this.pageEvent}
            />
          </Col>
        </Row>
      </div>
    );
  }
}

export default Leaderboards;
