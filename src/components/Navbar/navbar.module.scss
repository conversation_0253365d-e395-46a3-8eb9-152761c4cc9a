.customNav {
  display: flex;
  flex-wrap: nowrap;
  -webkit-overflow-scrolling: touch;

  > li {
    flex: 0 0 auto;

    > a {
      padding: 15px 10px !important;
      white-space: nowrap;
      font-size: 13px;

      i {
        margin-right: 3px;
      }
    }
  }
}

// Adjust navbar height
.navbarContainer {
  min-height: 52px;

  :global(.navbar-brand) {
    padding: 0 15px;
    height: 52px;

    img {
      height: 40px;
      margin: 6px 0;
    }
  }
}

// Make navbar more compact on medium screens
@media (max-width: 1200px) {
  .customNav > li > a {
    padding: 15px 8px !important;
    font-size: 12px;

    i {
      margin-right: 2px;
    }
  }
}

// Switch to vertical layout on mobile
@media (max-width: 768px) {
  .customNav {
    flex-direction: column;
    width: 100%;

    > li {
      width: 100%;

      > a {
        padding: 12px 15px !important;
        font-size: 14px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);

        i {
          margin-right: 10px;
          width: 20px; // Fixed width for icons to align text
          text-align: center;
        }
      }

      &:last-child > a {
        border-bottom: none;
      }
    }
  }

  .navbarContainer {
    :global(.navbar-collapse) {
      max-height: calc(100vh - 52px); // Ensure scrolling works if needed
      overflow-y: auto;
    }
  }
}
