import React, { Component } from 'react';
import cn from 'classnames';
import { Link, NavLink } from 'react-router-dom';
import { Navbar, Nav, NavItem, MenuItem, NavDropdown } from 'react-bootstrap';
import { LinkContainer } from 'react-router-bootstrap';
import { signOut } from '../../actions/user';
import Logo from '../../img/svb-logo.png';
import styles from './navbar.module.scss';

class NavbarComponent extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isNavbarOpen: false,
    };
    this.signOut = this.signOut.bind(this);
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(nextProps) {
    const { user, navigate } = this.props;

    // redirect user to the homepage on sign-out
    // if (user.user && !nextProps.user.user) navigate('/');
  }

  signOut() {
    const { dispatch } = this.props;
    dispatch(signOut());
  }

  userIcon() {
    return (
      <span>
        <i className="glyphicon glyphicon-user" />
      </span>
    );
  }

  renderSignInLinks(authenticatedUser) {
    const { isNavbarOpen } = this.state;
    const { authenticatedUser: propsAuthenticatedUser } = this.props;

    if (authenticatedUser)
      return (
        <Nav pullRight onClick={() => this.setState({ isNavbarOpen: !isNavbarOpen })}>
          <NavDropdown className={cn({ open: isNavbarOpen })} eventKey={3} id="nav-dropdown" title={this.userIcon()}>
            <MenuItem eventKey={3.1} disabled>
              {propsAuthenticatedUser.email}
            </MenuItem>
            <MenuItem eventKey={3.1} onSelect={this.signOut}>
              Sign Out
            </MenuItem>
          </NavDropdown>
        </Nav>
      );

    return (
      <Nav pullRight onClick={() => this.setState({ isNavbarOpen: !isNavbarOpen })}>
        <NavDropdown className={cn({ open: isNavbarOpen })} eventKey={3} id="nav-dropdown" title={this.userIcon()}>
          <MenuItem eventKey={3.1} componentClass={Link} href="/" to="/">
            Sign In
          </MenuItem>
        </NavDropdown>
      </Nav>
    );
  }

  renderLinks(authenticatedUser) {
    if (authenticatedUser)
      return (
        <Nav className={styles.customNav}>
          <LinkContainer to="/clients">
            <NavItem eventKey={0}>
              <i className="glyphicon glyphicon-briefcase" />
              <span>&nbsp;Clients</span>
            </NavItem>
          </LinkContainer>
          <LinkContainer to="/teams">
            <NavItem eventKey={1}>
              <i className="glyphicon glyphicon-user" />
              <span>&nbsp;Teams</span>
            </NavItem>
          </LinkContainer>
          <LinkContainer to="/welcome-pages">
            <NavItem eventKey={2}>
              <i className="glyphicon glyphicon-home" />
              <span>&nbsp;Welcome Pages</span>
            </NavItem>
          </LinkContainer>
          <LinkContainer to="/strategic-initiatives">
            <NavItem eventKey={3}>
              <i className="glyphicon glyphicon-book" />
              <span>&nbsp;Initiatives</span>
            </NavItem>
          </LinkContainer>
          <LinkContainer to="/challenges">
            <NavItem eventKey={4}>
              <i className="glyphicon glyphicon-bookmark" />
              <span>&nbsp;Challenges</span>
            </NavItem>
          </LinkContainer>
          <LinkContainer to="/decisions">
            <NavItem eventKey={5}>
              <i className="glyphicon glyphicon-list" />
              <span>&nbsp;Decisions</span>
            </NavItem>
          </LinkContainer>
          <LinkContainer to="/leaderboards">
            <NavItem eventKey={6}>
              <i className="glyphicon glyphicon-stats" />
              <span>&nbsp;Leaderboard</span>
            </NavItem>
          </LinkContainer>
          <LinkContainer to="/org-charts">
            <NavItem eventKey={7}>
              <i className="glyphicon glyphicon-list-alt" />
              <span>&nbsp;Org Charts</span>
            </NavItem>
          </LinkContainer>
          <LinkContainer to="/self-assessments">
            <NavItem eventKey={8}>
              <i className="glyphicon glyphicon-check" />
              <span>&nbsp;Assessment</span>
            </NavItem>
          </LinkContainer>
        </Nav>
      );
  }

  render() {
    const { authenticatedUser } = this.props;

    return (
      <Navbar fixedTop className={styles.navbarContainer}>
        <Navbar.Header>
          <Navbar.Brand>
            <NavLink to="#">
              <img src={Logo} alt="Logo" />
            </NavLink>
          </Navbar.Brand>
          <Navbar.Toggle />
        </Navbar.Header>
        <Navbar.Collapse>
          {this.renderLinks(authenticatedUser)}
          {this.renderSignInLinks(authenticatedUser)}
        </Navbar.Collapse>
      </Navbar>
    );
  }
}

export default NavbarComponent;
