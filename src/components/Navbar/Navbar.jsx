import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  LayoutDashboard,
  LogOut,
  RotateCcw,
  ChevronDown,
  User,
  Sun,
  Moon,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { useTheme } from "../ThemeProvider/ThemeProvider";
import { resetTeamHistory, signOut } from "../../actions/user";
import defaultLogo from "../../img/svb-logo.png";

const Navbar = ({ authenticatedUser, user, dispatch }) => {
  const navigate = useNavigate();
  const [isResetModalOpen, setIsResetModalOpen] = useState(false);
  const clientLogo = user?.client?.logoImage || defaultLogo;
  const { theme, toggleTheme } = useTheme();

  const handleSignOut = () => {
    dispatch(signOut());
    navigate("/");
  };

  const handleResetHistory = async () => {
    await resetTeamHistory().payload;
    localStorage.clear();
    setIsResetModalOpen(false);
    navigate("/");
    dispatch(signOut());
  };

  if (!authenticatedUser) return null;

  return (
    <nav className={`fixed top-0 w-[90vw] max-w-[1200px] mt-[10px] z-10 py-2 border-b backdrop-blur-[5px] ${theme === 'dark' ? 'border-white/5' : 'border-gray-300/20'}`}>
      <div className="container mx-auto">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className={`flex items-center justify-center size-14 overflow-hidden rounded-lg border-2 ${theme === 'dark' ? 'border-white/10' : 'border-gray-300/30'}`}>
            <img
              src={clientLogo}
              alt="Workshop Logo"
              className="size-11 rounded-md object-cover"
            />
          </div>

          {/* Theme Toggle and User Menu */}
          <div className="flex items-center gap-4">
            {/* Theme Toggle */}
            <div className="flex items-center gap-2">
              <Sun className={`h-4 w-4 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`} />
              <Switch
                checked={theme === "light"}
                onCheckedChange={toggleTheme}
                aria-label="Toggle theme"
              />
              <Moon className={`h-4 w-4 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`} />
            </div>

            {/* User Menu */}
            <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className={`gap-2 ${theme === 'dark' ? 'text-gray-200 hover:text-white hover:bg-white/10' : 'text-gray-700 hover:text-gray-900 hover:bg-gray-200/50'}`}
              >
                <User className="h-4 w-4" />
                {authenticatedUser.email}
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className={`w-56 mr-4 mt-2 backdrop-blur-xl ${theme === 'dark' ? 'bg-gray-900/95 border-gray-800' : 'bg-white/95 border-gray-200'}`}>
              <DropdownMenuLabel className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                {user?.client?.name || "Account"}
              </DropdownMenuLabel>
              <DropdownMenuSeparator className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-200'}`} />
              <DropdownMenuItem
                className={`cursor-pointer ${theme === 'dark' ? 'text-gray-300 focus:text-white focus:bg-white/10' : 'text-gray-700 focus:text-gray-900 focus:bg-gray-200/50'}`}
                onClick={() => navigate("/workshops")}
              >
                <LayoutDashboard className="mr-2 h-4 w-4" />
                Change Workshop
              </DropdownMenuItem>
              <DropdownMenuItem
                className={`cursor-pointer ${theme === 'dark' ? 'text-gray-300 focus:text-white focus:bg-white/10' : 'text-gray-700 focus:text-gray-900 focus:bg-gray-200/50'}`}
                onClick={() => setIsResetModalOpen(true)}
              >
                <RotateCcw className="mr-2 h-4 w-4" />
                Reset History
              </DropdownMenuItem>
              <DropdownMenuSeparator className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-200'}`} />
              <DropdownMenuItem
                className={`cursor-pointer ${theme === 'dark' ? 'text-gray-300 focus:text-white focus:bg-white/10' : 'text-gray-700 focus:text-gray-900 focus:bg-gray-200/50'}`}
                onClick={handleSignOut}
              >
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Reset History Modal */}
      <Dialog open={isResetModalOpen} onOpenChange={setIsResetModalOpen}>
        <DialogContent className={`backdrop-blur-xl ${theme === 'dark' ? 'bg-gray-900/95 border-gray-800' : 'bg-white/95 border-gray-200'}`}>
          <DialogHeader>
            <DialogTitle className={`text-xl ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              Reset History
            </DialogTitle>
            <DialogDescription className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              Are you sure? Resetting your history will erase all progress you
              have made, in all clients you have signed up, or been assigned to.
              <p className="mt-2">This action will log you out.</p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2 sm:gap-0">
            <Button
              variant="destructive"
              onClick={() => setIsResetModalOpen(false)}
              className="bg-red-600 hover:bg-red-700"
            >
              Cancel
            </Button>
            <Button
              onClick={handleResetHistory}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Proceed
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </nav>
  );
};

export default Navbar;
