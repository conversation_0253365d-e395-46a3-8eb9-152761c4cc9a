import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import to from "await-to-js";
import { useTheme } from "@/components/ThemeProvider/ThemeProvider";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import Loader from "../common/Loader";
import { getChallenges, saveChallenges, getTeam } from "../../actions/user";
import Metrics from "../Metrics/Metrics";
import { motion, AnimatePresence } from "framer-motion";
import styles from "./Challenges.module.css";

const Challenges = ({ user, navigate, location }) => {
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [challengeData, setChallengeData] = useState({
    challengeNumber: 0,
    challenges: [],
    globalTeamMetrics: [],
    currentChallengeId: null,
  });
  const [activeChallenge, setActiveChallenge] = useState(0);
  const [animating, setAnimating] = useState(false);

  const client = user?.client || {};
  const { challengesTabName, challengesTabVisibility } = client;

  useEffect(() => {
    if (
      client?.hasOwnProperty("challengesTabVisibility") &&
      !challengesTabVisibility
    ) {
      navigate("/leaderboard");
      return;
    }

    fetchChallenges();
  }, [client, challengesTabVisibility, navigate]);

  const fetchChallenges = async () => {
    setIsLoading(true);
    const [err, res] = await to(getChallenges().payload);

    const team = await getTeam().payload;

    if (err) {
      toast.error("Failed to load challenges");
      setIsLoading(false);
      return;
    }

    const { data } = res;
    // Sort challenges and initialize state
    const sortedChallenges = data.challenges
      .sort((a, b) => a.id - b.id)
      .map((challenge) => ({
        ...challenge,
        selectedA: challenge.selectedA || false,
        selectedB: challenge.selectedB || false,
        selectedC: challenge.selectedC || false,
        submitted:
          challenge.selectedA ||
          challenge.selectedB ||
          challenge.selectedC ||
          false,
        showOptions: false,
      }));

    // Initialize metrics with 0 values
    const globalTeamMetrics = (team.data.globalTeamMetrics || []).map(metric => ({
      ...metric,
      value: 0
    }));

    // Calculate metrics based on selected options
    sortedChallenges.forEach(challenge => {
      ['A', 'B', 'C'].forEach(option => {
        if (challenge[`selected${option}`]) {
          globalTeamMetrics.forEach(metric => {
            const [, metricIndex] = metric.alias.split(" ");
            const alias = `optionMetric${metricIndex}${option.toUpperCase()}`;
            metric.value = (metric.value || 0) + (challenge[alias] || 0);
          });
        }
      });
    });

    setChallengeData({
      ...data,
      challenges: sortedChallenges,
      currentChallengeId: sortedChallenges[0].id,
      globalTeamMetrics
    });
    setIsLoading(false);
  };

  const handleOptionChange = async (option, challengeIndex, checked) => {
    const updatedChallenges = [...challengeData.challenges];
    const challenge = updatedChallenges[challengeIndex];
    const updatedMetrics = [...challengeData.globalTeamMetrics];

    if (!checked || challenge.submitted) return;

    // Reset all options for this challenge
    challenge.selectedA = false;
    challenge.selectedB = false;
    challenge.selectedC = false;
    challenge[`selected${option}`] = checked;
    challenge.submitted = true;

    // Recalculate all metrics from scratch
    updatedMetrics.forEach((metric) => {
      const [, metricIndex] = metric.alias.split(" ");
      metric.value = 0;
      updatedChallenges.forEach((ch) => {
        ['A', 'B', 'C'].forEach((opt) => {
          if (ch[`selected${opt}`]) {
            const alias = `optionMetric${metricIndex}${opt}`;
            metric.value += (ch[alias] || 0);
          }
        });
      });
    });

    // Optimistically update the state
    setChallengeData((prev) => ({
      ...prev,
      challenges: updatedChallenges,
      globalTeamMetrics: updatedMetrics,
    }));

    // Save changes
    try {
      await saveChallenges({
        ...challengeData,
        challenges: updatedChallenges,
        globalTeamMetrics: updatedMetrics,
      }).payload;
      toast.success("Response saved successfully", {
        duration: 3000,
      });
      
      // Move to next challenge with animation
      if (activeChallenge < challengeData.challenges.length - 1) {
        setAnimating(true);
        setTimeout(() => {
          setActiveChallenge(activeChallenge + 1);
          setAnimating(false);
        }, 500);
      }
    } catch (err) {
      toast.error("Failed to save challenge response");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) return <Loader />;

  // Get only the current active challenge
  const currentChallenge = challengeData.challenges[activeChallenge];
  
  // Get completed challenges
  const completedChallenges = challengeData.challenges.slice(0, activeChallenge);

  return (
    <div className={styles.container}>
      <div className={styles.metricsSection}>
        <h2 className={`${styles.metricsTitle} ${theme === 'dark' ? styles.darkText : styles.lightText}`}>
          Metrics
        </h2>
        <Metrics
          location={location}
          client={client}
          metrics={challengeData.globalTeamMetrics}
          setGlobalMetrics={(metrics) =>
            setChallengeData((prev) => ({ ...prev, globalTeamMetrics: metrics }))
          }
        />
      </div>
      <div className={styles.mainContent}>
        <Card className="bg-transparent border-transparent">
          <h2 className={`${styles.challengesTitle} ${theme === 'dark' ? styles.darkText : styles.lightText}`}>
            {challengesTabName || "Challenges"}
          </h2>
          
          {/* Completed challenges */}
          <AnimatePresence>
            {completedChallenges.map((challenge, index) => (
              <motion.div
                key={challenge.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className={`${styles.challengeCard} ${
                  theme === 'dark' 
                    ? `${styles.darkCard} ${styles.darkCardHover}` 
                    : `${styles.lightCard} ${styles.lightCardHover}`
                }`}
              >
                <div className={styles.challengeHeader}>
                  <div>
                    <h3 className={`${styles.challengeTitle} ${theme === 'dark' ? styles.darkText : styles.lightText}`}>
                      Challenge #{index + 1}
                    </h3>
                    <p className={`${styles.challengeDescription} ${theme === 'dark' ? styles.darkGrayText : styles.lightGrayText}`}>
                      {challenge.description}
                    </p>
                    <p className={`${styles.selectedOptionText} ${theme === 'dark' ? styles.darkBlueText : styles.lightBlueText}`}>
                      You selected option {
                        challenge.selectedA ? 'A' : challenge.selectedB ? 'B' : 'C'
                      }
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {/* Current active challenge */}
          {currentChallenge && (
            <AnimatePresence>
              <motion.div
                key={currentChallenge.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className={`${styles.challengeCard} ${
                  theme === 'dark' 
                    ? `${styles.darkCard} ${styles.darkCardHover}` 
                    : `${styles.lightCard} ${styles.lightCardHover}`
                }`}
              >
                <div className={styles.challengeHeader}>
                  <div>
                    <h3 className={`${styles.challengeTitle} ${theme === 'dark' ? styles.darkText : styles.lightText}`}>
                      Challenge #{activeChallenge + 1}
                    </h3>
                    <p className={`${styles.challengeDescription} ${theme === 'dark' ? styles.darkGrayText : styles.lightGrayText}`}>
                      {currentChallenge.description}
                    </p>
                  </div>
                </div>
                
                <div className={styles.optionsContainer}>
                  {["A", "B", "C"].map((option) => (
                    <div
                      key={option}
                      className={`${styles.optionCard} ${
                        currentChallenge[`selected${option}`]
                          ? styles.darkSelectedOption
                          : theme === 'dark'
                            ? styles.darkOptionCard
                            : styles.lightOptionCard
                      }`}
                    >
                      <div className={styles.optionContent}>
                        <Checkbox
                          id={`challenge-${activeChallenge}-option${option}`}
                          checked={currentChallenge[`selected${option}`]}
                          onCheckedChange={(checked) =>
                            handleOptionChange(option, activeChallenge, checked)
                          }
                          disabled={currentChallenge.submitted || animating}
                          className={`${styles.checkbox} ${theme === 'dark' ? styles.darkCheckbox : styles.lightCheckbox}`}
                        />
                        <div className="flex-1">
                          <label
                            htmlFor={`challenge-${activeChallenge}-option${option}`}
                            className={`${styles.optionLabel} ${theme === 'dark' ? styles.darkGrayText : styles.lightGrayText}`}
                          >
                            {currentChallenge[`option${option}`]}
                          </label>
                          {currentChallenge[`selected${option}`] && (
                            <div className={`${styles.consequenceBox} ${
                              theme === 'dark'
                                ? styles.darkConsequenceBox
                                : styles.lightConsequenceBox
                            }`}>
                              <p className={`${styles.consequenceText} ${
                                theme === 'dark' ? styles.darkConsequenceText : styles.lightConsequenceText
                              }`}>
                                Consequence: {currentChallenge[`consequence${option}`]}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            </AnimatePresence>
          )}
          
          {/* Completion message */}
          {activeChallenge >= challengeData.challenges.length && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className={`${styles.completionMessage} ${
                theme === 'dark' ? `${styles.darkCompletionCard} ${styles.darkText}` : `${styles.lightCompletionCard} ${styles.lightText}`
              }`}
            >
              <h3 className={styles.completionTitle}>All Challenges Completed!</h3>
              <p>You have completed all available challenges.</p>
            </motion.div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default Challenges;
