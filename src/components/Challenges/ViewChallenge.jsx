import React, { Component } from 'react';
import { PageHeader, ControlLabel, Row, Col, FormControl, FormGroup, Button, Tabs, Tab } from 'react-bootstrap';
import 'react-rangeslider/lib/index.css';
import BlockUi from 'react-block-ui';
import { reduxForm, Field } from 'redux-form';

import { renderField } from '../App/RenderField';
import { getChallenge, updateChallenge } from '../../actions/challenge';

const validate = (values) => {
  const errors = {};
  let hasErrors = false;
  if (!values.name || values.name.trim() === '') {
    console.log('name validation error');
    errors.name = 'Enter a Scheme name';
    hasErrors = true;
  }

  return hasErrors && errors;
};

class ViewChallenge extends Component {
  constructor(props) {
    super(props);
    this.state = {
      name: '',
      blocking: false,
      teamId: '',
      challenges: [
        {
          imageUrl: '',
          description: '',
          optionA: '',
          consequenceA: '',
          optionMetric1A: 0,
          optionMetric2A: 0,
          optionMetric3A: 0,
          optionB: '',
          consequenceB: '',
          optionMetric1B: 0,
          optionMetric2B: 0,
          optionMetric3B: 0,
          optionC: '',
          consequenceC: '',
          optionMetric1C: 0,
          optionMetric2C: 0,
          optionMetric3C: 0,
        },
      ],
    };
    this.handleInputChange = this.handleInputChange.bind(this);
    this.handleFormSubmit = this.handleFormSubmit.bind(this);
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillMount() {
    const { params, destroy, initialize } = this.props;

    this.blockUi();
    const { challengeId } = params;
    getChallenge(challengeId).payload.then((result) => {
      this.unBlockUi();
      destroy();
      initialize(result.data);
      console.log('got challenge: ', result);
      const { data } = result;
      this.setState({
        ...data,
      });
    });
  }

  handleFormSubmit(event) {
    event.preventDefault();
    this.blockUi();
    const { id, name, challenges } = this.state;
    const { alert, navigate } = this.props;
    const body = {
      id,
      name,
      challenges,
    };
    console.log('submitted', body);
    updateChallenge(body).payload.then(
      (response) => {
        this.unBlockUi();
        alert('success', 'Success', 'Challenge Scheme updated successfully');
        navigate('/challenges');
        console.log('api response:', response);
      },
      (error) => {
        console.log(error);
        alert('danger', 'Error', `Challenge Scheme not updated: ${error.toString()}`);
        this.unBlockUi();
      }
    );
  }

  handleInputChange(event) {
    const { target } = event;
    const value = target.type === 'checkbox' ? target.checked : target.value;
    const { name } = target;
    console.log('setting: ', [name], value, this.state);
    this.setState({
      [name]: value,
    });
  }

  handleChallengeChange(index, key, event) {
    const { challenges } = this.state;
    challenges[index][key] = event.target.value;
    this.setState({
      challenges,
    });
    console.log(this.state);
  }

  addChallenge() {
    console.log('adding challenge');
    const { challenges } = this.state;
    if (challenges.length < 25) {
      challenges.push({
        imageUrl: '',
        description: '',
        optionA: '',
        consequenceA: '',
        optionMetric1A: 0,
        optionMetric2A: 0,
        optionMetric3A: 0,
        optionB: '',
        consequenceB: '',
        optionMetric1B: 0,
        optionMetric2B: 0,
        optionMetric3B: 0,
        optionC: '',
        consequenceC: '',
        optionMetric1C: 0,
        optionMetric2C: 0,
        optionMetric3C: 0,
      });
      this.setState(
        {
          challenges,
        },
        () => {
          console.log(this.state);
        }
      );
    } else {
      alert('Only 25 challenges can be added');
    }
  }

  removeChallenge(index) {
    if (window.confirm('Are you sure that you want to delete this challenge ?')) {
      console.log('removing: ', index);
      const { challenges } = this.state;
      challenges.splice(index, 1);
      this.setState(
        {
          challenges,
        },
        () => {
          console.log(this.state);
        }
      );
    }
  }

  blockUi() {
    this.setState({
      blocking: true,
    });
  }

  unBlockUi() {
    this.setState({
      blocking: false,
    });
  }

  renderChallengesForm() {
    const { challenges } = this.state;

    return (
      <Tabs defaultActiveKey={0} id="tabs-container">
        {challenges.map((challenge, i) => (
          // eslint-disable-next-line react/no-array-index-key
          <Tab eventKey={i} title={`Challenge #${i + 1}`} key={i}>
            <Row>
              <Col md={12}>
                <FormControl
                  componentClass="textarea"
                  value={challenges[i].description}
                  rows={5}
                  onChange={(e) => this.handleChallengeChange(i, 'description', e)}
                  placeholder="Enter description text"
                />
                <hr />
              </Col>
              <Col md={12}>
                <FormGroup>
                  <ControlLabel>Option A)</ControlLabel>
                  <FormControl
                    type="text"
                    className="margin-bottom-20"
                    placeholder="Enter option name"
                    value={challenges[i].optionA}
                    name="name"
                    onChange={(e) => this.handleChallengeChange(i, 'optionA', e)}
                  />
                  <ControlLabel>Consequence A)</ControlLabel>
                  <FormControl
                    componentClass="textarea"
                    name="description"
                    placeholder="Enter description text"
                    value={challenges[i].consequenceA}
                    onChange={(e) => this.handleChallengeChange(i, 'consequenceA', e)}
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 1 A</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric1A"
                    value={challenges[i].optionMetric1A}
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric1A', e)}
                    placeholder="Enter metric 1 A value"
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 2 A</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric2A"
                    value={challenges[i].optionMetric2A}
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric2A', e)}
                    placeholder="Enter metric 2 A value"
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 3 A</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric3A"
                    value={challenges[i].optionMetric3A}
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric3A', e)}
                    placeholder="Enter metric 3 A value"
                  />
                </FormGroup>
                <hr />
              </Col>
              <Col md={12}>
                <FormGroup>
                  <ControlLabel>Option B)</ControlLabel>
                  <FormControl
                    type="text"
                    className="margin-bottom-20"
                    placeholder="Enter option name"
                    value={challenges[i].optionB}
                    onChange={(e) => this.handleChallengeChange(i, 'optionB', e)}
                  />
                  <ControlLabel>Consequence B)</ControlLabel>
                  <FormControl
                    componentClass="textarea"
                    placeholder="Enter description text"
                    value={challenges[i].consequenceB}
                    onChange={(e) => this.handleChallengeChange(i, 'consequenceB', e)}
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 1 B</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric1B"
                    value={challenges[i].optionMetric1B}
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric1B', e)}
                    placeholder="Enter metric 1 B value"
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 2 B</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric2B"
                    value={challenges[i].optionMetric2B}
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric2B', e)}
                    placeholder="Enter metric 2 B value"
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 3 B</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric3B"
                    value={challenges[i].optionMetric3B}
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric3B', e)}
                    placeholder="Enter metric 3 B value"
                  />
                </FormGroup>
                <hr />
              </Col>
              <Col md={12}>
                <FormGroup>
                  <ControlLabel>Option C)</ControlLabel>
                  <FormControl
                    type="text"
                    className="margin-bottom-20"
                    placeholder="Enter option name"
                    value={challenges[i].optionC}
                    onChange={(e) => this.handleChallengeChange(i, 'optionC', e)}
                  />
                  <ControlLabel>Consequence C)</ControlLabel>
                  <FormControl
                    componentClass="textarea"
                    placeholder="Enter description text"
                    value={challenges[i].consequenceC}
                    onChange={(e) => this.handleChallengeChange(i, 'consequenceC', e)}
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 1 C</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric1C"
                    value={challenges[i].optionMetric1C}
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric1C', e)}
                    placeholder="Enter metric 1 C value"
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 2 C</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric2C"
                    value={challenges[i].optionMetric2C}
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric2C', e)}
                    placeholder="Enter metric 2 C value"
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 3 C</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric3C"
                    value={challenges[i].optionMetric3C}
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric3C', e)}
                    placeholder="Enter metric 3 C value"
                  />
                </FormGroup>
                <hr />
              </Col>
            </Row>
            <Row>
              <Col md={12} onClick={() => this.removeChallenge(i)}>
                <Button>Delete This Challenge</Button>
              </Col>
            </Row>
          </Tab>
        ))}
      </Tabs>
    );
  }

  render() {
    const horizontalLabels = {};
    const { blocking, name } = this.state;
    for (let i = 1; i < 16; i++) horizontalLabels[i] = i;

    return (
      <div className="container root-container">
        <BlockUi tag="div" blocking={blocking}>
          <Row>
            <Col md={10}>
              <PageHeader>
                View Challenge:
                {name}
              </PageHeader>
            </Col>
            <Col md={2}>
              <Button
                className="pull-right btn-info save-button"
                onClick={(e) => this.handleFormSubmit(e)}
                disabled={!!validate(this.state)}
              >
                Save
              </Button>
            </Col>
          </Row>
          <form>
            <Row>
              <Col md={12}>
                <FormGroup>
                  <ControlLabel>Name</ControlLabel>
                  <Field
                    name="name"
                    type="text"
                    component={renderField}
                    onChange={this.handleInputChange}
                    label="Enter Challenge Scheme Name*"
                  />
                </FormGroup>
              </Col>
            </Row>
            <hr />
            <Col md={12}>
              <Button bsStyle="success" className="pull-right" onClick={() => this.addChallenge()}>
                Add Challenge
              </Button>
            </Col>
            {this.renderChallengesForm()}
          </form>
        </BlockUi>
      </div>
    );
  }
}

export default reduxForm({
  form: 'ViewChallenge',
  validate,
})(ViewChallenge);
