.container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem 1rem 1.5rem 1rem;
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .container {
    flex-direction: row;
  }
}

.metricsSection {
  width: 100%;
}

@media (min-width: 768px) {
  .metricsSection {
    width: 25%;
  }
}

.mainContent {
  width: 100%;
}

@media (min-width: 768px) {
  .mainContent {
    width: 75%;
  }
}

.metricsTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  padding-left: 0.75rem;
}

.challengesTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
}

.challengeCard {
  backdrop-filter: blur(24px);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.challengeHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.challengeTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.challengeDescription {
  font-size: 0.875rem;
  line-height: 1.5;
}

.optionsContainer {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding-top: 1rem;
}

.optionCard {
  border-radius: 0.5rem;
  border-width: 1px;
  padding: 1rem;
}

.optionContent {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.optionLabel {
  font-size: 0.875rem;
  line-height: 1.5;
  cursor: pointer;
}

.consequenceBox {
  margin-top: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.375rem;
  border-width: 1px;
}

.consequenceText {
  font-size: 0.875rem;
}

.selectedOptionText {
  margin-top: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.completionMessage {
  text-align: center;
  padding: 1.5rem;
  border-radius: 0.5rem;
}

.completionTitle {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

/* Dark theme styles */
.darkText {
  color: white;
}

.darkGrayText {
  color: #d1d5db;
}

.darkBlueText {
  color: #93c5fd;
}

.darkCard {
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.1);
}

.darkCardHover:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.darkOptionCard {
  background-color: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.05);
}

.darkSelectedOption {
  border-color: rgba(59, 130, 246, 0.5);
  background-color: rgba(59, 130, 246, 0.1);
}

.darkConsequenceBox {
  background-color: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
}

.darkConsequenceText {
  color: #bfdbfe;
}

.darkCompletionCard {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Light theme styles */
.lightText {
  color: #1f2937;
}

.lightGrayText {
  color: #4b5563;
}

.lightBlueText {
  color: #2563eb;
}

.lightCard {
  background-color: rgba(255, 255, 255, 0.7);
  border-color: rgba(209, 213, 219, 0.3);
}

.lightCardHover:hover {
  background-color: rgba(255, 255, 255, 0.8);
}

.lightOptionCard {
  background-color: rgba(255, 255, 255, 0.6);
  border-color: rgba(209, 213, 219, 0.3);
}

.lightSelectedOption {
  border-color: rgba(59, 130, 246, 0.5);
  background-color: rgba(59, 130, 246, 0.1);
}

.lightConsequenceBox {
  background-color: #eff6ff;
  border-color: #bfdbfe;
}

.lightConsequenceText {
  color: #1d4ed8;
}

.lightCompletionCard {
  background-color: rgba(255, 255, 255, 0.7);
}

.checkbox {
  margin-top: 0.25rem;
}

.darkCheckbox {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.lightCheckbox {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}