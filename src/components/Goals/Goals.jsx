import React, { useState, useEffect } from "react";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import to from "await-to-js";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { getTeam, updateTeam } from "../../actions/user";
import Metrics from "../Metrics/Metrics";

const Goals = ({ user: { client = {} }, navigate }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    goal1: "",
    goal2: "",
    goal3: "",
  });

  const { lightHighlightColor, goalsTabName, goalsTabVisibility } = client;

  useEffect(() => {
    if (client?.hasOwnProperty("goalsTabVisibility") && !goalsTabVisibility) {
      navigate("/strategic-initiatives");
      return;
    }
    fetchTeamData();
  }, [client, goalsTabVisibility, navigate]);

  const fetchTeamData = async () => {
    setIsLoading(true);
    const [err, res] = await to(getTeam().payload);

    if (err) {
      toast.error("Failed to load team data");
      setIsLoading(false);
      return;
    }

    setFormData(res.data);
    setIsLoading(false);
  };

  const handleInputChange = (event) => {
    const { name, value, type, checked } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleFormSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);

    const [err] = await to(updateTeam(formData).payload);

    if (err) {
      console.error(err);
      toast.error("Failed to update goals");
      setIsLoading(false);
      return;
    }

    toast.success("Goals saved successfully", {
      duration: 3000,
    });

    setIsLoading(false);
  };

  return (
    <section>
      <Metrics />

      <div>
        <Card className="bg-transparent border-transparent">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-7">
            <CardTitle className="text-2xl font-bold text-white">
              {goalsTabName || "Goals"}
            </CardTitle>
            <Button
              onClick={handleFormSubmit}
              disabled={isLoading}
              style={{
                background: lightHighlightColor,
              }}
              className="font-semibold hover:opacity-90 transition-opacity"
            >
              {isLoading ? (
                <>
                  <Loader className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Goals"
              )}
            </Button>
          </CardHeader>

          <CardContent className="space-y-8">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label
                  htmlFor="teamName"
                  className="text-sm font-medium text-gray-200"
                >
                  Team Name
                </Label>
                <Input
                  id="teamName"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter team name"
                  className="bg-white/5 border-white/10 text-white placeholder:text-gray-400"
                  disabled={isLoading}
                />
              </div>

              <div className="h-px bg-white/10 my-6" />

              {[1, 2, 3].map((num) => (
                <div key={num} className="space-y-2">
                  <Label
                    htmlFor={`goal${num}`}
                    className="text-sm font-medium text-gray-200"
                  >
                    #{num}
                  </Label>
                  <Textarea
                    id={`goal${num}`}
                    name={`goal${num}`}
                    value={formData[`goal${num}`]}
                    onChange={handleInputChange}
                    placeholder="Write your goal here..."
                    className="min-h-[120px] bg-white/5 border-white/10 text-white 
                             placeholder:text-gray-400 resize-none"
                    disabled={isLoading}
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default Goals;
