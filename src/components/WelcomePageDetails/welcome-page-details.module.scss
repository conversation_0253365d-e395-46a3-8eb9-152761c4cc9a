.preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-height: 250px;

  .button {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

.img {
  max-height: 150px;
}

.flexRow {
  display: flex;
  align-items: center;
  justify-content: space-between;

  :global(.checkbox) {
    width: 100%;
    margin-top: 10px !important;
  }

  :global(.form-group) {
    width: 100%;

    &:not(:last-child) {
      margin-right: 10px;
    }
  }
}

.editor {
  background-color: white;
  padding: 15px;
}
