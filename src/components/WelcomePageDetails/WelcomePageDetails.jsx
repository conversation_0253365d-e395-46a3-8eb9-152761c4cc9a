import React, { useState, useEffect } from 'react';
import BlockUi from 'react-block-ui';
import to from 'await-to-js';
import cn from 'classnames';
import { EditorState, ContentState, convertToRaw } from 'draft-js';
import { Editor } from 'react-draft-wysiwyg';
import { Button, Col, ControlLabel, FormGroup, PageHeader, Row } from 'react-bootstrap';
import draftToHtml from 'draftjs-to-html';
import htmlToDraft from 'html-to-draftjs';

import { uploadImage, createWelcomePage, getWelcomePage, updateWelcomePage } from '../../actions/welcomePages';

import styles from './welcome-page-details.module.scss';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';

export default function WelcomePageDetails(props) {
  const [name, setName] = useState('');
  const [editorState, setEditorState] = useState(EditorState.createEmpty());
  const [image, setImage] = useState('');
  const [isLoading, setLoading] = useState(false);
  const { params, alert, navigate } = props;
  const [isNewWelcomePage, setIsNewWelcomePage] = useState(params.welcomePageId === 'new');

  useEffect(() => {
    const isNew = params.welcomePageId === 'new';

    if (isNew) return;

    setIsNewWelcomePage(false);

    proceedWelcomePageDetails(params.welcomePageId);
  }, [isNewWelcomePage, params.welcomePageId]);

  async function setWelcomePageImage() {
    setLoading(true);

    const file = document.getElementById('image').files[0];
    const [err, res] = await to(uploadImage(file));

    setLoading(false);

    if (err) return alert('danger', 'Error', 'Image was not uploaded.');

    const { url } = res.data;

    alert('success', 'Success', 'Image was successfully uploaded');

    setImage(url);
  }

  function removeImage() {
    document.getElementById('image').value = '';

    setImage('');
  }

  async function proceedWelcomePageDetails(welcomePageId) {
    setLoading(true);

    const [err, res] = await to(getWelcomePage(welcomePageId).payload);

    setLoading(false);

    if (err) return alert('danger', 'Error', 'Oops, something went wrong with fetching welcome page details');

    const { name, image, text } = res.data;

    setName(name);
    setImage(image);

    const contentBlock = htmlToDraft(text);
    if (contentBlock) {
      const contentState = ContentState.createFromBlockArray(contentBlock.contentBlocks);
      const editorState = EditorState.createWithContent(contentState);

      setEditorState(editorState);
    }
  }

  async function saveWelcomePageDetails() {
    if (!name) return alert('danger', 'Error', 'Name is required field');

    const state = editorState.getCurrentContent();
    const enteredTextLength = state.getPlainText().length;
    const payload = {
      name,
      text: draftToHtml(convertToRaw(state)),
      image,
    };

    if (enteredTextLength >= 4096)
      return alert('danger', 'Error', `Entered text is too long. Length: ${enteredTextLength}`);

    setLoading(true);

    let err;
    let res;

    if (!isNewWelcomePage) [err, res] = await to(updateWelcomePage({ ...payload, id: params.welcomePageId }).payload);
    else [err, res] = await to(createWelcomePage(payload).payload);

    setLoading(false);

    if (err) return alert('danger', 'Error', 'Oops, something went wrong with saving welcome page details');

    alert('success', 'Success', `Welcome page was successfully ${isNewWelcomePage ? 'created' : 'updated'}`);

    if (isNewWelcomePage) {
      const { id } = res.data;

      navigate(`/welcome-pages/${id}`, { replace: true });
    }
  }

  return (
    <div className="container root-container">
      <BlockUi tag="div" blocking={isLoading}>
        <Row>
          <Col md={10}>
            <PageHeader>{isNewWelcomePage ? 'Create New Welcome Page' : 'Update welcome page'}</PageHeader>
          </Col>
          <Col md={2}>
            <Button className="btn-info save-button pull-right" onClick={saveWelcomePageDetails} disabled={!name}>
              {isNewWelcomePage ? 'Create' : 'Save'}
            </Button>
          </Col>
        </Row>

        <form>
          <Row>
            <Col md={6}>
              <FormGroup>
                <ControlLabel>Welcome Page Name*</ControlLabel>
                <input
                  className="form-control"
                  type="text"
                  name="welcome-page-name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter welcome page's name"
                />
              </FormGroup>
            </Col>
          </Row>

          <hr className="margin-top-5 margin-bottom-20" />
          <h3>Text Input</h3>
          <Editor
            editorState={editorState}
            onEditorStateChange={setEditorState}
            editorClassName={styles.editor}
            toolbar={{
              options: [
                'inline',
                'blockType',
                'fontSize',
                'list',
                'textAlign',
                'colorPicker',
                'link',
                'emoji',
                'remove',
                'history',
              ],
            }}
          />

          <hr className="margin-top-5 margin-bottom-20" />
          <h3>Image Upload</h3>
          <Row>
            <Col md={12}>
              <FormGroup>
                {image ? (
                  <div className={styles.preview}>
                    <img src={image} alt="background" className={cn('img-responsive', styles.img)} />

                    <Button bsStyle="danger" className={styles.button} onClick={() => removeImage()}>
                      Remove image
                    </Button>
                  </div>
                ) : null}

                <input
                  id="image"
                  className="form-control"
                  type="file"
                  name="image"
                  onChange={() => setWelcomePageImage()}
                />
              </FormGroup>
            </Col>
          </Row>
        </form>
      </BlockUi>
    </div>
  );
}
