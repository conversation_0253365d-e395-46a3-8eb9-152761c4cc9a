import React, { useRef, useLayoutEffect, useState, useCallback } from "react";

const FiltersGroup = ({
  filters,
  currentFilter,
  className = "",
  backgroundColor,
  onClick = () => {},
}) => {
  const wrapperRef = useRef(null);
  const [activeFilterStyles, setActiveFilterStyles] = useState({});

  useLayoutEffect(() => {
    if (!wrapperRef?.current) return;

    const currentFilterIndex = filters.findIndex(
      (f) => f.value === currentFilter
    );
    const currentElement = wrapperRef.current.childNodes[currentFilterIndex];

    if (!currentElement) return;

    requestAnimationFrame(() => {
      setActiveFilterStyles({
        transform: `translateX(${currentElement.offsetLeft}px)`,
        width: `${currentElement.offsetWidth}px`,
        backgroundColor,
      });
    });
  }, [filters, currentFilter, backgroundColor]);

  const handleFilterClick = useCallback(
    (filter) => {
      if (currentFilter === filter) return;
      onClick(filter);
    },
    [currentFilter, onClick]
  );

  return (
    <div className="relative w-fit bg-blue-400/10 backdrop-blur-xl rounded-lg border border-white/10 shadow-lg">
      <div className={`relative flex rounded-lg ${className}`} ref={wrapperRef}>
        {/* Sliding Selection Indicator */}
        <div
          className="absolute inset-y-0 rounded-lg transition-all duration-300 ease-out shadow-lg"
          style={{
            ...activeFilterStyles,
            zIndex: 0,
          }}
        />

        {/* Filter Options */}
        {filters.map((filter) => {
          const { value, label } = filter;
          const isActive = currentFilter === value;

          return (
            <button
              key={value}
              onClick={() => handleFilterClick(value)}
              className={`
                relative flex-1 px-6 py-3
                text-sm font-medium rounded-lg
                transition-colors duration-200 z-10
                ${
                  isActive
                    ? "text-white"
                    : "text-white/70 hover:text-white hover:bg-white/5"
                }
              `}
            >
              {label}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default FiltersGroup;
