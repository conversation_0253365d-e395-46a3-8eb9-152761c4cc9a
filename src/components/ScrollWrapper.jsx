import React, { useState, lazy, Suspense, useMemo } from 'react';
import { useTheme } from './ThemeProvider/ThemeProvider';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import NavbarContainer from '../containers/NavbarContainer';
import Loader from './common/Loader';

// Lazy load container components
const WelcomePage = lazy(() => import('../containers/WelcomePageContainer'));
const Goals = lazy(() => import('../containers/GoalsContainer'));
const StrategicInitiatives = lazy(() => import('../containers/StrategicInitiativesContainer'));
const Challenges = lazy(() => import('../containers/ChallengesContainer'));
const Leaderboard = lazy(() => import('../containers/LeaderboardContainer'));
import OrgChart from '../containers/OrgChartContainer';
const SelfAssessment = lazy(() => import('../containers/SelfAssessmentContainer'));
const Decisions = lazy(() => import('../containers/DecisionsContainer'));

const ScrollWrapper = ({ client }) => {
  const [activeSection, setActiveSection] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [highlightedButton, setHighlightedButton] = useState(null);
  const { theme } = useTheme();

  // Dynamically create sections based on client configuration
  const availableSections = useMemo(() => {
    if (!client) return [];

    // Create mapping between scheme names and their corresponding sections
    const schemeToSectionMap = {
      welcomePages: 'welcome',
      challenges: 'challenges',
      initiatives: 'initiatives',
      leaderboards: 'leaderboard',
      orgCharts: 'orgChart',
      selfAssessments: 'assessment',
      decisions: 'decisions',
      goals: 'goals', // Note: goals seems to be missing from schemeOrder
    };

    const allSections = [
      {
        id: 'welcome',
        Component: WelcomePage,
        title: client.homeTabName || 'Welcome',
        isVisible: client.homeTabVisibility,
      },
      {
        id: 'goals',
        Component: Goals,
        title: client.goalsTabName || 'Goals',
        isVisible: client.goalsTabVisibility,
      },
      {
        id: 'initiatives',
        Component: StrategicInitiatives,
        title: client.strategicInitiativesTabName || 'Strategic Initiatives',
        isVisible: client.initiativesTabVisibility,
      },
      {
        id: 'challenges',
        Component: Challenges,
        title: client.challengesTabName || 'Challenges',
        isVisible: client.challengesTabVisibility,
      },
      {
        id: 'leaderboard',
        Component: Leaderboard,
        title: client.leaderboardTabName || 'Leaderboard',
        isVisible: client.leaderboardTabVisibility,
      },
      {
        id: 'orgChart',
        Component: OrgChart,
        title: client.orgChartTabName || 'Org Chart',
        isVisible: client.orgChartTabVisibility,
      },
      {
        id: 'assessment',
        Component: SelfAssessment,
        title: client.selfAssessmentTabName || 'Self Assessment',
        isVisible: client.selfAssessmentTabVisibility,
      },
      {
        id: 'decisions',
        Component: Decisions,
        title: client.decisionsTabName || 'Decision With PNL',
        isVisible: client.decisionsTabVisibility,
      },
    ];

    // Filter visible sections
    const visibleSections = allSections.filter((section) => section.isVisible === true);

    // If no scheme order is provided, return visible sections in default order
    if (!client.schemeOrder || !client.schemeOrder.length) {
      return visibleSections;
    }

    // Sort sections based on scheme order
    const orderedSections = [...visibleSections].sort((a, b) => {
      const aIndex = client.schemeOrder.findIndex((scheme) => schemeToSectionMap[scheme] === a.id);
      const bIndex = client.schemeOrder.findIndex((scheme) => schemeToSectionMap[scheme] === b.id);

      // Handle cases where a section might not be in the scheme order
      if (aIndex === -1) return 1; // Put unordered items at the end
      if (bIndex === -1) return -1;

      return aIndex - bIndex;
    });

    return orderedSections;
  }, [client]);

  const changeSection = (index) => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setActiveSection(index);

    setTimeout(() => setIsTransitioning(false), 500);
  };

  // Update the keyboard navigation handler
  React.useEffect(() => {
    const handleKeyPress = (event) => {
      if (isTransitioning) return;

      if ((event.key === 'ArrowLeft' || event.key === 'ArrowUp') && activeSection > 0) {
        setHighlightedButton('left');
        changeSection(activeSection - 1);
        setTimeout(() => setHighlightedButton(null), 200);
      } else if (
        (event.key === 'ArrowRight' || event.key === 'ArrowDown') &&
        activeSection < availableSections.length - 1
      ) {
        setHighlightedButton('right');
        changeSection(activeSection + 1);
        setTimeout(() => setHighlightedButton(null), 200);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [activeSection, isTransitioning, availableSections.length]);

  // If client data isn't loaded yet
  if (!client) {
    return <Loader fullScreen />;
  }

  // If no sections are available
  if (!availableSections.length) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className={`text-xl ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
          No sections available for this workshop.
        </p>
      </div>
    );
  }

  const { Component, title, id } = availableSections[activeSection];

  return (
    <div>
      <NavbarContainer />

      <div
        className={
          `relative mx-auto py-1 mt-[100px] w-[90vw] backdrop-blur-xl rounded-lg ` +
          `border shadow-xl overflow-auto shadow-xl ` +
          `max-w-[1200px] min-h-[600px] ` +
          `${theme === 'dark' ? 'border-white/40 bg-black/20' : 'border-gray-300/30 bg-transparent'}`
        }
      >
        {/* <Component /> */}
        <Suspense fallback={<Loader />}>
          <Component />
        </Suspense>

        {/* Glass effect container */}
        <div className={`absolute inset-0 z-[-5] backdrop-blur-sm border rounded-lg ${theme === 'dark' ? 'bg-white/5 border-white/10' : 'bg-white/30 border-gray-300/20'}`}></div>
      </div>

      <div className="mt-10 flex justify-center gap-4">
        {activeSection > 0 && (
          <button
            onClick={() => changeSection(activeSection - 1)}
            disabled={isTransitioning}
            className={`flex items-center gap-2 px-6 py-3
                     backdrop-blur-xl rounded-lg transition-all duration-200 shadow-lg
                     disabled:opacity-50 disabled:cursor-not-allowed
                     ${theme === 'dark'
                       ? `bg-white/5 border border-white/10 hover:bg-white/10 text-white ${highlightedButton === 'left' ? 'bg-white/20' : ''}`
                       : `bg-white/50 border border-gray-300/30 hover:bg-white/70 text-gray-800 ${highlightedButton === 'left' ? 'bg-white/70' : ''}`
                     }`}
          >
            <ChevronLeft className="w-5 h-5" />
            <span className="text-sm font-medium">Back to {availableSections[activeSection - 1].title}</span>
          </button>
        )}

        {activeSection < availableSections.length - 1 && (
          <button
            onClick={() => changeSection(activeSection + 1)}
            disabled={isTransitioning}
            className={`flex items-center gap-2 px-6 py-3
                     backdrop-blur-xl rounded-lg transition-all duration-200 shadow-lg
                     disabled:opacity-50 disabled:cursor-not-allowed
                     ${theme === 'dark'
                       ? `bg-white/5 border border-white/10 hover:bg-white/10 text-white ${highlightedButton === 'right' ? 'bg-white/20' : ''}`
                       : `bg-white/50 border border-gray-300/30 hover:bg-white/70 text-gray-800 ${highlightedButton === 'right' ? 'bg-white/70' : ''}`
                     }`}
          >
            <span className="text-sm font-medium">Next to {availableSections[activeSection + 1].title}</span>
            <ChevronRight className="w-5 h-5" />
          </button>
        )}
      </div>
    </div>
  );
};

export default ScrollWrapper;
