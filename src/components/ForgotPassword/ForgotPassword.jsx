import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Mail, Loader } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import to from "await-to-js";
import { forgotPassword } from "../../actions/user";

const ForgotPassword = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [emailInput, setEmailInput] = useState("");

  // Remove clientId from localStorage on component mount
  useEffect(() => {
    localStorage.removeItem("clientId");
  }, []);

  const handleInputChange = (event) => {
    setEmailInput(event.target.value);
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);
    setError("");

    const body = {
      email: emailInput.toLowerCase(),
    };

    const [err] = await to(forgotPassword(body).payload);

    if (err) {
      const message = `Forgot password error. ${err.message.toString()}`;
      setError(message);
      toast.error(message);
      setIsLoading(false);
      return;
    }

    toast.success("Forgot password instruction was sent to your email!");
    navigate("/");
    setIsLoading(false);
  };

  return (
    <div className="flex justify-center">
      <div className="w-[90vw] md:w-[30rem]">
        <div className="backdrop-blur-xl bg-transparent p-8 rounded-2xl shadow-xl border border-gray-800">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-2xl font-bold text-white mb-2">
              Forgot Password
            </h1>
            <p className="text-gray-300">Enter your email to reset password</p>
          </div>

          {error && (
            <Alert
              variant="destructive"
              className="mb-6 bg-red-500/10 text-red-300 border-red-500/20"
            >
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="email"
                  name="emailInput"
                  value={emailInput}
                  onChange={handleInputChange}
                  className="block w-full pl-10 h-12 bg-white/5 border border-white/10 rounded-lg 
                           text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 
                           focus:border-transparent"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-2 px-4 flex items-center justify-center
                bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg
                transition-colors duration-200
                ${isLoading ? "opacity-70 cursor-not-allowed" : ""}`}
            >
              {isLoading ? (
                <>
                  <Loader className="animate-spin -ml-1 mr-2 h-5 w-5" />
                  Sending...
                </>
              ) : (
                "Reset Password"
              )}
            </button>
          </form>

          <div className="mt-6 text-center text-gray-300">
            Remember your password?{" "}
            <Link
              to="/"
              className="text-blue-400 hover:text-blue-300 font-medium"
            >
              Sign In
            </Link>
            {" or "}
            <Link
              to="/sign-up"
              className="text-blue-400 hover:text-blue-300 font-medium"
            >
              Sign Up
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
