import React, { useState, useMemo } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";

const OrgChartMeetModal = ({
  id,
  name,
  title,
  meetingPoints,
  meet_1: isMeeting1Finished,
  meet_2: isMeeting2Finished,
  meet_3: isMeeting3Finished,
  meet_1_text: meeting1,
  meet_1_points: meeting1Points,
  meet_2_text: meeting2,
  meet_2_points: meeting2Points,
  meet_3_text: meeting3,
  meet_3_points: meeting3Points,
  onUpdate = () => {},
  onClose = () => {},
}) => {
  const [step, setStep] = useState(0);
  const hasMeetings = useMemo(() => meeting1, [meeting1]);

  const MeetingCost = ({ cost }) => (
    <div className="text-center space-y-3 py-4">
      <p className="text-gray-300">This meeting will cost</p>
      <p className="text-3xl font-semibold text-white">{cost} time units</p>
      <p className="text-gray-300">Would you like to proceed?</p>
    </div>
  );

  const MeetingContent = ({ label, text }) => (
    <div className="space-y-2">
      {label && (
        <Label className="text-sm font-medium text-gray-300">{label}</Label>
      )}
      <div className="p-4 bg-white/5 rounded-lg border border-white/10">
        <p className="text-gray-200 leading-relaxed whitespace-pre-wrap">
          {text}
        </p>
      </div>
    </div>
  );

  const renderEmptyState = () => (
    <>
      <div className="text-center py-6 text-gray-300">
        Sorry, <span className="text-white font-medium">{name}</span> does not
        have any meetings.
      </div>
      <DialogFooter>
        <Button onClick={onClose}>Got it</Button>
      </DialogFooter>
    </>
  );

  const renderFirstMeeting = () => {
    if (step === 0) {
      return (
        <>
          <MeetingCost cost={meeting1Points} />
          <DialogFooter className="sm:justify-between">
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                onUpdate({ userId: id, key: "meet_1", value: true });
                setStep(1);
              }}
              disabled={meeting1Points > meetingPoints}
            >
              Proceed
            </Button>
          </DialogFooter>
        </>
      );
    }
    return (
      <>
        <MeetingContent text={meeting1} />
        <DialogFooter>
          <Button onClick={onClose}>Done</Button>
        </DialogFooter>
      </>
    );
  };

  const renderSecondMeeting = () => {
    if (step === 0) {
      return (
        <>
          <ScrollArea className="max-h-[60vh]">
            <div className="space-y-4 py-4">
              <MeetingContent label="1st Meeting" text={meeting1} />
              <MeetingCost cost={meeting2Points} />
            </div>
          </ScrollArea>
          <DialogFooter className="sm:justify-between">
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                onUpdate({ userId: id, key: "meet_2", value: true });
                setStep(1);
              }}
              disabled={meeting2Points > meetingPoints}
            >
              Proceed
            </Button>
          </DialogFooter>
        </>
      );
    }
    return (
      <>
        <MeetingContent text={meeting2} />
        <DialogFooter>
          <Button onClick={onClose}>Done</Button>
        </DialogFooter>
      </>
    );
  };

  const renderThirdMeeting = () => {
    if (step === 0) {
      return (
        <>
          <ScrollArea className="max-h-[60vh]">
            <div className="space-y-4 py-4">
              <MeetingContent label="1st Meeting" text={meeting1} />
              <MeetingContent label="2nd Meeting" text={meeting2} />
              <MeetingCost cost={meeting3Points} />
            </div>
          </ScrollArea>
          <DialogFooter className="sm:justify-between">
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                onUpdate({ userId: id, key: "meet_3", value: true });
                setStep(1);
              }}
              disabled={meeting3Points > meetingPoints}
            >
              Proceed
            </Button>
          </DialogFooter>
        </>
      );
    }
    return (
      <>
        <MeetingContent text={meeting3} />
        <DialogFooter>
          <Button onClick={onClose}>Done</Button>
        </DialogFooter>
      </>
    );
  };

  const renderAllMeetings = () => (
    <>
      <ScrollArea className="max-h-[60vh]">
        <div className="space-y-4 py-4">
          <MeetingContent label="1st Meeting" text={meeting1} />
          {meeting2 && <MeetingContent label="2nd Meeting" text={meeting2} />}
          {meeting3 && <MeetingContent label="3rd Meeting" text={meeting3} />}
        </div>
      </ScrollArea>
      <DialogFooter>
        <Button onClick={onClose}>Done</Button>
      </DialogFooter>
    </>
  );

  const renderModalContent = () => {
    if (!hasMeetings) return renderEmptyState();

    if (
      !isMeeting1Finished ||
      (isMeeting1Finished && !isMeeting2Finished && step === 1)
    ) {
      return renderFirstMeeting();
    }

    if (
      (meeting2 && !isMeeting2Finished) ||
      (meeting2 && isMeeting2Finished && !isMeeting3Finished && step === 1)
    ) {
      return renderSecondMeeting();
    }

    if (
      (meeting2 && meeting3 && !isMeeting3Finished) ||
      (meeting2 && meeting3 && isMeeting3Finished && step === 1)
    ) {
      return renderThirdMeeting();
    }

    return renderAllMeetings();
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <DialogTitle className="text-center space-y-2">
            <div className="text-xl">Meet with {name}</div>
            {title && (
              <div className="text-sm font-normal text-gray-400">{title}</div>
            )}
          </DialogTitle>
        </DialogHeader>
        {renderModalContent()}
      </DialogContent>
    </Dialog>
  );
};

export default OrgChartMeetModal;
