import { useTheme } from "@/components/ThemeProvider/ThemeProvider";

const OrgChartLegend = () => {
  const { theme } = useTheme();
  const legendItems = [
    { id: 1, name: "Supporter", color: "#00CC66" },
    { id: 2, name: "Neutral", color: "#FFCB06" },
    { id: 3, name: "Non-Supporter", color: "#FA582D" },
    { id: 4, name: "Unknown", color: "grey" },
    { id: 5, name: "Locked", color: "black" },
  ];

  return (
    <div className={`flex flex-wrap gap-4 p-4 backdrop-blur-xl rounded-xl border ${theme === 'dark' ? 'bg-white/5 border-white/10' : 'bg-white/30 border-gray-300/20'}`}>
      {legendItems.map((item) => (
        <div key={item.id} className="flex items-center gap-2">
          <span
            className="w-4 h-4 rounded-full"
            style={{ backgroundColor: item.color }}
          />
          <span className={`text-sm ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}`}>{item.name}</span>
        </div>
      ))}
    </div>
  );
};

export default OrgChartLegend;
