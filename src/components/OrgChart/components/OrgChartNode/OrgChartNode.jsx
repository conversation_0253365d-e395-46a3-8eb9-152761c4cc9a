import React, { useState, useMemo } from "react";
import OrgChartMeetModal from "../OrgChartMeetModal/OrgChartMeetModal";

const OrgChartNode = ({
  type = "global",
  nodeData,
  meetingPoints,
  users = [],
  onUpdate = () => {},
}) => {
  const [showMeetingModal, setShowMeetingModal] = useState(false);
  const { name, title, status, locked_by_id: lockedById, photo } = nodeData;

  const isLockedUser = useMemo(() => {
    if (!lockedById) return false;
    return users.some((u) => u.id === lockedById && !u.meet_1);
  }, [name, title, users, lockedById]);

  const getStatusClasses = () => {
    if (isLockedUser) return "bg-black/50";
    if (status <= -1) return "bg-[#FA582D]/50";
    if (status === 1) return "bg-[#FFCB06]/50";
    if (status >= 2) return "bg-[#00CC66]/50";
    return "bg-gray-500/50";
  };

  return (
    <>
      <div
        onClick={() => !isLockedUser && setShowMeetingModal(true)}
        className={`
          group relative flex items-center
          backdrop-blur-xl rounded-xl border border-white/10
          transition-all duration-200 cursor-pointer
          hover:scale-105 hover:shadow-lg
          ${getStatusClasses()}
          ${type === "internal" ? "w-64" : "w-full"}
        `}
      >
        {isLockedUser && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-xl">
            <Lock className="w-6 h-6 text-white/50" />
          </div>
        )}

        {photo ? (
          <div
            className="w-16 h-16 rounded-l-xl bg-cover bg-center bg-no-repeat"
            style={{ backgroundImage: `url(${photo})` }}
          />
        ) : (
          <div className="w-16 h-16 rounded-l-xl bg-white/10 flex items-center justify-center text-white/50">
            Photo
          </div>
        )}

        <div className="p-3 flex-1 min-w-0">
          <h5 className="text-sm font-medium text-white truncate" title={name}>
            {name}
          </h5>
          <h4 className="text-xs text-gray-300 truncate" title={title}>
            {title}
          </h4>
        </div>
      </div>

      {showMeetingModal && (
        <OrgChartMeetModal
          {...nodeData}
          meetingPoints={meetingPoints}
          onUpdate={onUpdate}
          onClose={() => setShowMeetingModal(false)}
        />
      )}
    </>
  );
};

export default OrgChartNode;
