/**
 * This function builds a tree from the flat array
 * [{
 *   ...user,
 *   children: [
 *   {
 *     ...user1,
 *     children: [
 *       {
 *         ...user2,
 *         children: [
 *           {
 *             ...user3,
 *             children: [
 *
 *             ]
 *           }
 *         ]
 *       }
 *     ]
 *   }
 */
export function createTreeFromArray(dataset) {
  const result = [];
  const hashTable = {};

  for (let i = 0, n = dataset.length; i < n; ++i) hashTable[dataset[i].id] = { ...dataset[i] };

  for (let i = 0, n = dataset.length; i < n; ++i) {
    const currentNode = dataset[i];
    const { parent_id: parentId, id } = currentNode;

    if (parentId) {
      if (!hashTable[parentId].hasOwnProperty('children')) hashTable[parentId].children = [];

      hashTable[parentId].children.push(hashTable[id]);
    } else {
      result.push(hashTable[id]);
    }
  }

  return result;
}
