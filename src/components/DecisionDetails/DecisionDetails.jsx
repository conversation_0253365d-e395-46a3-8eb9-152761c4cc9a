import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Row, Col, Button, Alert, Nav, NavItem, FormGroup, ControlLabel } from 'react-bootstrap';
import BlockUi from 'react-block-ui';
import { getDecision, updateDecision } from '../../actions/decisions';
import { INITIAL_PAGE_STATE, MAX_PAGES } from '../Decisions/constants';
import DecisionForm from '../Decisions/components/DecisionForm';
import PNLUpload from '../Decisions/components/PNLUpload/PNLUpload';

const DecisionDetails = ({ params, alert, navigate }) => {
  const [decision, setDecision] = useState(null);
  const [blocking, setBlocking] = useState(false);
  const [alertInfo, setAlertInfo] = useState(null);
  const [pages, setPages] = useState([]);
  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [decisionName, setDecisionName] = useState('');
  const [pnlData, setPnlData] = useState({ data: null, charts: [] });

  // AI Prompt Template state
  const [useCustomGlobalTemplate, setUseCustomGlobalTemplate] = useState(false);
  const [globalTemplate, setGlobalTemplate] = useState('');

  console.log('DecisionDetails state:', { useCustomGlobalTemplate, globalTemplate, blocking });

  useEffect(() => {
    if (params.decisionId) {
      loadDecision(params.decisionId);
    }
  }, [params.decisionId]);
  // console.log({ decision, pages });

  const loadDecision = (id) => {
    setBlocking(true);

    getDecision(id).payload.then(
      (result) => {
        const { data } = result;
        setDecision(data);
        setDecisionName(data.name);
        setPnlData(data.pnl || { data: null, charts: [] });
        setUseCustomGlobalTemplate(data.use_custom_global_template || false);
        setGlobalTemplate(data.global_pnl_prompt_template || '');
        setPages(
          data.pages.map((page) => ({
            page_name: page.page_name || `Page ${page.page_number}`,
            page_image: page.page_image || '',
            sliders: page.sliders,
            incentives: page.incentives,
            pnl: page.page_pnl || { data: null, charts: [] },
            useCustomPageTemplate: page.use_custom_page_template || false,
            pageTemplate: page.page_pnl_prompt_template || '',
          }))
        );
        setBlocking(false);
      },
      (error) => {
        showAlert('danger', `Error loading decision: ${error.toString()}`);
        setBlocking(false);
      }
    );
  };

  const showAlert = (type, message) => {
    setAlertInfo({ type, message });
    setTimeout(() => setAlertInfo(null), 3000);
  };

  const handleAddPage = () => {
    if (pages.length >= MAX_PAGES) {
      showAlert('danger', `Maximum ${MAX_PAGES} pages allowed`);
      return;
    }
    setPages([...pages, { ...INITIAL_PAGE_STATE }]);
    setCurrentPageIndex(pages.length);
  };

  const handleDeletePage = (indexToDelete) => {
    if (pages.length <= 1) {
      showAlert('danger', 'Cannot delete the last page');
      return;
    }

    const newPages = pages.filter((_, index) => index !== indexToDelete);
    setPages(newPages);

    if (currentPageIndex >= indexToDelete) {
      setCurrentPageIndex(Math.max(0, currentPageIndex - 1));
    }
  };

  const handleFormSubmit = async (event) => {
    event.preventDefault();

    if (!decisionName.trim()) {
      showAlert('danger', 'Please enter a decision name');
      return;
    }

    // setBlocking(true);

    const payload = {
      id: decision.id,
      name: decisionName.trim(),
      pnl: pnlData, // Global PNL data
      globalTemplate: useCustomGlobalTemplate ? globalTemplate : null,
      useCustomGlobalTemplate: useCustomGlobalTemplate,
      pages: pages.map((page) => ({
        page_name: page.page_name || `Page ${page.page_number || 1}`,
        page_image: page.page_image || '',
        sliders: page.sliders,
        incentives: page.incentives,
        page_pnl: page.pnl, // Include page-specific PNL data
        page_pnl_prompt_template: page.useCustomPageTemplate ? page.pageTemplate : null,
        use_custom_page_template: page.useCustomPageTemplate || false,
      })),
    };
    // console.log({ payload, decision, pages, pnlData });

    try {
      const result = await updateDecision(payload).payload;
      showAlert('success', 'Decision updated successfully');
      setDecision(result.data);
    } catch (error) {
      console.error('Error updating decision:', error);
      showAlert('danger', 'Failed to update decision');
    } finally {
      setBlocking(false);
    }
  };

  console.log('DecisionDetails render - blocking state:', blocking);

  return (
    <div className="container root-container">
      <BlockUi tag="div" blocking={blocking}>
        {alertInfo && (
          <Alert bsStyle={alertInfo.type} onDismiss={() => setAlertInfo(null)} style={{ marginBottom: '20px' }}>
            {alertInfo.message}
          </Alert>
        )}
        <Row>
          <Col md={10}>
            <PageHeader>Edit Decision</PageHeader>
          </Col>
          <Col md={2}>
            <Button className="pull-right btn-info save-button" onClick={handleFormSubmit}>
              Save
            </Button>
          </Col>
        </Row>

        <Row style={{ marginBottom: '20px' }}>
          <Col md={12}>
            <FormGroup>
              <ControlLabel>Decision Name</ControlLabel>
              <input
                type="text"
                className="form-control"
                value={decisionName}
                onChange={(e) => setDecisionName(e.target.value)}
                placeholder="Enter decision name"
              />
            </FormGroup>
          </Col>
        </Row>

        {/* AI Prompt Template Configuration */}
        <Row style={{ marginBottom: '20px' }}>
          <Col md={12}>
            <FormGroup>
              <div className="checkbox">
                <label>
                  <input
                    type="checkbox"
                    checked={useCustomGlobalTemplate}
                    onChange={(e) => {
                      console.log('Checkbox clicked:', e.target.checked);
                      setUseCustomGlobalTemplate(e.target.checked);
                    }}
                  />
                  Use Custom AI Prompt Template (Global)
                </label>
              </div>
              {useCustomGlobalTemplate && (
                <div style={{ marginTop: '10px' }}>
                  <ControlLabel>Custom AI Prompt Template</ControlLabel>
                  <textarea
                    className="form-control"
                    rows="6"
                    value={globalTemplate}
                    onChange={(e) => setGlobalTemplate(e.target.value)}
                    placeholder="Enter your custom AI prompt template. Use {{VARIABLE_NAME}} for dynamic content substitution."
                  />
                  <small className="help-block">
                    Available variables: {`{{USER_SELECTIONS}}, {{SLIDER_DETAILS}}, {{INCENTIVE_DETAILS}}, {{PAGE_NAME}}, {{SCHEME_NAME}}, {{TOTAL_FTE}}, {{TOTAL_INVESTMENT}}`}
                  </small>
                </div>
              )}
            </FormGroup>
          </Col>
        </Row>

        {decision && (
          <>
            <Button className="pull-right btn-primary" onClick={handleAddPage} disabled={pages.length >= MAX_PAGES}>
              Add Page
            </Button>
            <Nav bsStyle="tabs" activeKey={currentPageIndex} onSelect={setCurrentPageIndex} style={{ marginBottom: 8 }}>
              {pages.map((page, index) => (
                <NavItem key={index} eventKey={index}>
                  {page.page_name || `Page ${index + 1}`}
                  {pages.length > 1 && (
                    <Button
                      bsStyle="link"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleDeletePage(index);
                      }}
                      style={{ padding: '0 5px', color: 'red' }}
                    >
                      ✕
                    </Button>
                  )}
                </NavItem>
              ))}
            </Nav>
            <DecisionForm
              key={currentPageIndex}
              state={pages[currentPageIndex]}
              setState={(updater) => {
                setPages((prevPages) => {
                  const newPages = [...prevPages];
                  if (typeof updater === 'function') {
                    newPages[currentPageIndex] = updater(prevPages[currentPageIndex]);
                  } else {
                    newPages[currentPageIndex] = updater;
                  }
                  return newPages;
                });
              }}
              showAlert={showAlert}
            />
            <PNLUpload pnlData={pnlData} onChange={setPnlData} label={'Global P&L Data '} />
          </>
        )}
      </BlockUi>
    </div>
  );
};

export default DecisionDetails;
