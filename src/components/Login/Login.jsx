import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Lock, Mail, Loader } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import to from "await-to-js";
import { signIn, signInUserSuccess } from "../../actions/user";

const Login = ({ dispatch }) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [formData, setFormData] = useState({
    emailInput: "",
    passwordInput: "",
  });

  // Remove clientId from localStorage on component mount
  React.useEffect(() => {
    localStorage.removeItem("clientId");
  }, []);

  const handleInputChange = (event) => {
    const { name, value, type, checked } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);
    setError("");

    const body = {
      email: formData.emailInput.toLowerCase(),
      password: formData.passwordInput,
    };

    const [err, res] = await to(signIn(body).payload);

    if (err) {
      const message =
        err.message.toString() === "Request failed with status code 400"
          ? "Sign In error. Please re-enter your email and try again."
          : "Sign In error.";

      setError(message);
      toast.error(message);
      setIsLoading(false);
      return;
    }

    const { token, user } = res.data;
    sessionStorage.setItem("jwtToken", token);
    dispatch(signInUserSuccess({ user }));
    toast.success("You're in!");
    navigate("/workshops");
    setIsLoading(false);
  };

  return (
    <div className="flex justify-center">
      <div className="w-[90vw] md:w-[30rem]">
        <div className="backdrop-blur-xl bg-transparent p-8 rounded-2xl shadow-xl border border-gray-800">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-2xl font-bold text-white mb-2">Welcome Back</h1>
            <p className="text-gray-300">Sign in to continue</p>
          </div>

          {error && (
            <Alert
              variant="destructive"
              className="mb-6 bg-red-500/10 text-red-300 border-red-500/20"
            >
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="email"
                  name="emailInput"
                  value={formData.emailInput}
                  onChange={handleInputChange}
                  className="block w-full pl-10 h-12 bg-white/5 border border-white/10 rounded-lg 
                           text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 
                           focus:border-transparent"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="password"
                  name="passwordInput"
                  value={formData.passwordInput}
                  onChange={handleInputChange}
                  className="block w-full pl-10 h-12 bg-white/5 border border-white/10 rounded-lg 
                           text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 
                           focus:border-transparent"
                  placeholder="Enter your password"
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="rounded bg-white/10 border-white/10 text-blue-500 focus:ring-blue-500"
                />
                <span className="ml-2 text-gray-300">Remember me</span>
              </label>

              <Link
                to="/forgot-password"
                className="text-blue-400 hover:text-blue-300"
              >
                Forgot password?
              </Link>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-2 px-4 flex items-center justify-center
                bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg
                transition-colors duration-200
                ${isLoading ? "opacity-70 cursor-not-allowed" : ""}`}
            >
              {isLoading ? (
                <>
                  <Loader className="animate-spin -ml-1 mr-2 h-5 w-5" />
                  Signing in...
                </>
              ) : (
                "Sign In"
              )}
            </button>
          </form>

          <div className="mt-6 text-center text-gray-300">
            Don't have an account?{" "}
            <Link
              to="/sign-up"
              className="text-blue-400 hover:text-blue-300 font-medium"
            >
              Create one
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
