import React, { Component } from 'react';
import { Row, Col, InputGroup, Form, FormGroup, Button, Panel } from 'react-bootstrap';
import BlockUi from 'react-block-ui';
import { reduxForm, Field } from 'redux-form';

import { renderFieldWithoutMessage } from '../App/RenderField';
import { signIn, signInUserSuccess } from '../../actions/user';

const validate = (values) => {
  const errors = {};
  let hasErrors = false;
  if (!values.emailInput || values.emailInput.trim() === '') {
    errors.emailInput = 'Enter email address';
    hasErrors = true;
  }
  if (!values.passwordInput || values.passwordInput.trim() === '') {
    errors.passwordInput = 'Enter password';
    hasErrors = true;
  }
  return hasErrors && errors;
};

class Login extends Component {
  constructor(props) {
    super(props);
    this.state = {
      emailInput: '',
      passwordInput: '',
      signInError: '',
      blocking: false,
    };

    this.handleInputChange = this.handleInputChange.bind(this);
    this.handleFormSubmit = this.handleFormSubmit.bind(this);
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(nextProps) {
    // console.log('Login received props: ', nextProps);
  }

  handleFormSubmit(event) {
    event.preventDefault();
    this.blockUi();
    const { emailInput, passwordInput } = this.state;
    const { navigate, alert } = this.props;
    const body = {
      email: emailInput.toLowerCase(),
      password: passwordInput,
    };
    const { dispatch } = this.props;
    signIn(body).payload.then(
      (result) => {
        console.log('user signin result: ', result);
        this.unBlockUi();
        sessionStorage.setItem('jwtToken', result.data.token);
        alert('success', 'Success', "You're in!");
        dispatch(signInUserSuccess(result.data));
        navigate('/teams', { replace: true });
      },
      (error) => {
        alert('danger', 'Error', `Sign In error: ${error.toString()}`);
        this.unBlockUi();
        console.log('sign-in error: ', error);
      }
    );
  }

  handleInputChange(event) {
    const { target } = event;
    const value = target.type === 'checkbox' ? target.checked : target.value;
    const { name } = target;
    this.setState({
      [name]: value,
    });
  }

  blockUi() {
    this.setState({
      blocking: true,
    });
  }

  unBlockUi() {
    this.setState({
      blocking: false,
    });
  }

  render() {
    const { blocking } = this.state;

    return (
      <Row>
        <div className="absolute-center is-responsive">
          <BlockUi tag="div" blocking={blocking}>
            <Panel header="Login">
              <Col>
                <Form onSubmit={this.handleFormSubmit}>
                  <FormGroup>
                    <InputGroup>
                      <InputGroup.Addon>@</InputGroup.Addon>
                      <Field
                        name="emailInput"
                        type="text"
                        noMessage
                        component={renderFieldWithoutMessage}
                        onChange={this.handleInputChange}
                        label="Email*"
                      />
                    </InputGroup>
                  </FormGroup>

                  <FormGroup>
                    <InputGroup>
                      <InputGroup.Addon>
                        <i className="glyphicon glyphicon-lock" />
                      </InputGroup.Addon>
                      <Field
                        name="passwordInput"
                        type="password"
                        component={renderFieldWithoutMessage}
                        onChange={this.handleInputChange}
                        label="Password*"
                      />
                      <InputGroup.Button>
                        <Button type="submit" className="pull-right" disabled={!!validate(this.state)}>
                          Go
                        </Button>
                      </InputGroup.Button>
                    </InputGroup>
                  </FormGroup>
                </Form>
              </Col>
            </Panel>
          </BlockUi>
        </div>
      </Row>
    );
  }
}

export default reduxForm({
  form: 'Login',
  validate,
})(Login);
