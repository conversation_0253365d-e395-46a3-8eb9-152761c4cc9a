import React, { Component } from 'react';
import { PageHeader, ControlLabel, Row, Col, FormControl, FormGroup, InputGroup, Button } from 'react-bootstrap';
import Slider from 'react-rangeslider';
import 'react-rangeslider/lib/index.css';
import BlockUi from 'react-block-ui';
import { reduxForm, Field } from 'redux-form';

import { renderField } from '../App/RenderField';
import { getInitiative, updateInitiative } from '../../actions/initiative';

const validate = (values) => {
  const errors = {};

  let hasErrors = false;

  if (!values.name || values.name.trim() === '') {
    console.log('name validation error');
    errors.name = 'Enter a Scheme name';
    hasErrors = true;
  }

  return hasErrors && errors;
};

class ViewInitiative extends Component {
  constructor(props) {
    super(props);
    this.state = {
      initiativeNumber: 1,
      name: '',
      blocking: false,
      initiatives: [],
    };
    this.handleInputChange = this.handleInputChange.bind(this);
    this.handleFormSubmit = this.handleFormSubmit.bind(this);
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillMount() {
    this.blockUi();
    const { params, destroy, initialize } = this.props;
    const { initiativeId } = params;
    getInitiative(initiativeId).payload.then((result) => {
      this.unBlockUi();
      console.log('got initiative: ', result);
      destroy();
      initialize(result.data);
      const { data } = result;
      this.setState({
        ...data,
        ...data.scheme,
        name: data.scheme.name,
      });
    });
  }

  handleFormSubmit(event) {
    event.preventDefault();
    this.blockUi();
    console.log('submitted', this.state);
    const { id, name, initiativeNumber, initiatives } = this.state;
    const { navigate, alert } = this.props;
    const body = {
      id,
      name,
      initiativeNumber,
      initiatives,
    };
    updateInitiative(body).payload.then(
      (response) => {
        this.unBlockUi();
        navigate('/strategic-initiatives');
        alert('success', 'Success', 'Strategic Initiative Scheme updated successfully');
        console.log('api response:', response);
      },
      (error) => {
        console.log(error);
        alert('danger', 'Error', `Strategic Initiative Scheme not updated: ${error.toString()}`);
        this.unBlockUi();
      }
    );
  }

  handleInputChange(event) {
    const { target } = event;
    const value = target.type === 'checkbox' ? target.checked : target.value;
    const { name } = target;
    console.log('setting: ', [name], value, this.state);
    this.setState({
      [name]: value,
    });
  }

  handleInitiativeChange(index, key, event) {
    const { initiatives } = this.state;
    initiatives[index][key] = event.target.value;
    this.setState({
      initiatives,
    });
    console.log(this.state);
  }

  sliderChange(e) {
    this.setState({
      initiativeNumber: e,
    });
    console.log('slider chaged: ', e);
  }

  blockUi() {
    this.setState({
      blocking: true,
    });
  }

  unBlockUi() {
    this.setState({
      blocking: false,
    });
  }

  addInitiative() {
    const { initiatives } = this.state;
    if (initiatives.length < 25) {
      initiatives.push({
        name: '',
        description: '',
        metric1: 0,
        metric2: 0,
        metric3: 0,
      });
      this.setState({
        initiatives,
      });
      console.log(this.state);
    } else {
      alert('Only 25 initiatives can be added');
    }
  }

  removeInitiative(index) {
    if (window.confirm('Are you sure that you want to delete this initiative ?')) {
      const { initiatives } = this.state;
      initiatives.splice(index, 1);
      this.setState({
        initiatives,
      });
    }
  }

  renderInitiativesForm() {
    const { initiatives } = this.state;
    return (
      <Row>
        {initiatives.map((initiative, i) => (
          <Col md={12} key={i}>
            <FormGroup>
              <ControlLabel>Initiative #{i + 1}:</ControlLabel>
              <InputGroup>
                <FormControl
                  type="text"
                  name="name"
                  value={initiative.name}
                  onChange={(e) => this.handleInitiativeChange(i, 'name', e)}
                  placeholder="Enter initiative name"
                />
                <InputGroup.Button>
                  <Button onClick={() => this.removeInitiative(i)}>
                    <i className="glyphicon glyphicon-remove" />
                  </Button>
                </InputGroup.Button>
              </InputGroup>
            </FormGroup>
            <FormGroup>
              <ControlLabel>Description</ControlLabel>
              <FormControl
                componentClass="textarea"
                name="description"
                value={initiative.description}
                onChange={(e) => this.handleInitiativeChange(i, 'description', e)}
                rows={10}
                placeholder="Enter description text"
              />
            </FormGroup>
            <FormGroup>
              <ControlLabel>Metric 1</ControlLabel>
              <FormControl
                type="number"
                name="metric1"
                value={initiative.metric1}
                onChange={(e) => this.handleInitiativeChange(i, 'metric1', e)}
                placeholder="Enter metric 1 value"
              />
            </FormGroup>
            <FormGroup>
              <ControlLabel>Metric 2</ControlLabel>
              <FormControl
                type="number"
                name="metric2"
                value={initiative.metric2}
                onChange={(e) => this.handleInitiativeChange(i, 'metric2', e)}
                placeholder="Enter metric 2 value"
              />
            </FormGroup>
            <FormGroup>
              <ControlLabel>Metric 3</ControlLabel>
              <FormControl
                type="number"
                name="metric3"
                value={initiative.metric3}
                onChange={(e) => this.handleInitiativeChange(i, 'metric3', e)}
                placeholder="Enter metric 3 value"
              />
            </FormGroup>
            <hr />
          </Col>
        ))}
      </Row>
    );
  }

  render() {
    const horizontalLabels = {
      1: '1',
      2: '2',
      3: '3',
      4: '4',
      5: '5',
    };
    const { blocking, name, initiativeNumber } = this.state;

    return (
      <div className="container root-container">
        <BlockUi tag="div" blocking={blocking}>
          <Row>
            <Col md={10}>
              <PageHeader>
                View Strategic Initiative:
                {' '}
                {name}
              </PageHeader>
            </Col>
            <Col md={2}>
              <Button
                className="pull-right btn-info save-button"
                onClick={(e) => this.handleFormSubmit(e)}
                disabled={!!validate(this.state)}
              >
                Save
              </Button>
            </Col>
          </Row>
          <form>
            <Row>
              <Col md={12}>
                <FormGroup>
                  <ControlLabel>Name</ControlLabel>
                  <Field
                    name="name"
                    type="text"
                    component={renderField}
                    onChange={this.handleInputChange}
                    label="Enter Initiative Scheme Name*"
                  />
                </FormGroup>
              </Col>
            </Row>
            <Row>
              <Col md={12}>
                <ControlLabel>Initiatives teams can choose</ControlLabel>
                <div className="slider custom-labels">
                  <Slider
                    value={initiativeNumber}
                    min={1}
                    max={5}
                    step={1}
                    labels={horizontalLabels}
                    onChange={(e) => this.sliderChange(e)}
                  />
                </div>
              </Col>
            </Row>
            <hr />
            {this.renderInitiativesForm()}
            <Row>
              <Col md={12}>
                <Button bsStyle="success" onClick={() => this.addInitiative()}>
                  Add Initiative
                </Button>
              </Col>
            </Row>
          </form>
        </BlockUi>
      </div>
    );
  }
}

export default reduxForm({
  form: 'ViewInitiative',
  validate,
})(ViewInitiative);
