import React, { Component } from 'react';
import { PageHeader, InputGroup, FormControl, Button, Row, Col, Table, Pagination } from 'react-bootstrap';

import BlockUi from 'react-block-ui';

import { listInitiatives, toggleDisabled } from '../../actions/initiative';
import ResultLimit from '../App/ResultLimit';

class StrategicInitiatives extends Component {
  constructor(props) {
    super(props);
    this.state = {
      query: '',
      showDisabled: false,
      currentPage: 1,
      totalItems: 0,
      limit: 20,
      offset: 0,
      sort: 'scheme_name',
      ascending: true,
      initiatives: [],
    };
    this.pageEvent = this.pageEvent.bind(this);
  }

  componentDidMount() {
    this.list();
  }

  handleLimitChange(e) {
    this.setState(
      {
        limit: e.target.value,
        offset: 0,
        currentPage: 1,
      },
      () => {
        this.list();
      }
    );
  }

  viewInitiative(id) {
    const { navigate } = this.props;

    navigate(`/strategic-initiatives/id/${id}`);
  }

  blockUi() {
    this.setState({
      blocking: true,
    });
  }

  unBlockUi() {
    this.setState({
      blocking: false,
    });
  }

  list() {
    const { query, limit, offset, sort, showDisabled, ascending } = this.state;
    const { alert } = this.props;
    const q = {
      query,
      limit,
      offset,
      sort,
      showDisabled,
      ascending,
    };
    console.log('querying :', q);
    this.blockUi();
    listInitiatives(q).payload.then(
      (results) => {
        console.log('got teams: ', results);
        this.setState({ initiatives: results.data });
        if (results.data.length)
          this.setState({
            totalItems: Math.ceil(results.data[0].total / limit),
          });

        this.unBlockUi();
      },
      (error) => {
        console.log(error);
        alert('danger', 'Error', `Error listing Initiatives: ${error.toString()}`);
        this.unBlockUi();
      }
    );
  }

  updateQuery(key, value) {
    this.setState({
      [key]: value,
      currentPage: 1,
      offset: 0,
    });
  }

  sort(column) {
    this.setState(
      (prevState) => ({
        ascending: !prevState.ascending,
        sort: column,
        currentPage: 1,
        offset: 0,
      }),
      () => {
        this.list();
      }
    );
  }

  pageEvent(e) {
    const { limit } = this.state;
    console.log('settig offset: ', (e - 1) * limit);
    this.setState(
      {
        currentPage: e,
        offset: (e - 1) * limit,
      },
      () => {
        this.list();
      }
    );
  }

  toggleInitiativeDisable(initiative) {
    this.blockUi();
    toggleDisabled(initiative.id).payload.then(() => {
      this.list();
    });
  }

  toggleShowDisabled() {
    const { showDisabled } = this.state;
    this.setState(
      {
        showDisabled: !showDisabled,
      },
      () => {
        this.list();
      }
    );
  }

  renderButtons(initiative) {
    let disable;
    if (initiative.disabled)
      disable = (
        <Button bsSize="xsmall" bsStyle="info" onClick={() => this.toggleInitiativeDisable(initiative)}>
          Enable
        </Button>
      );
    else
      disable = (
        <Button bsSize="xsmall" onClick={() => this.toggleInitiativeDisable(initiative)}>
          Disable
        </Button>
      );

    return (
      <td className="text-right">
        <Button bsSize="xsmall" onClick={() => this.viewInitiative(initiative.id)} style={{ marginRight: '10px' }}>
          View
        </Button>
        {disable}
      </td>
    );
  }

  renderInitiativesList() {
    const { initiatives } = this.state;
    return (
      <tbody>
        {initiatives.map((initiative, i) => (
          <tr key={i}>
            <td>{initiative.name}</td>
            <td className="text-right">{initiative.created}</td>
            {this.renderButtons(initiative)}
          </tr>
        ))}
      </tbody>
    );
  }

  render() {
    const { blocking, showDisabled, totalItems, currentPage } = this.state;
    const { navigate } = this.props;

    return (
      <div className="container root-container">
        <PageHeader>Strategic Initiatives</PageHeader>
        <Row>
          <Col lg={2} md={2} xs={6}>
            <ResultLimit onChange={(e) => this.handleLimitChange(e)} />
          </Col>
          <Col lg={4} md={4} xs={6}>
            <InputGroup>
              <FormControl
                type="text"
                placeholder="Search"
                name="query"
                onChange={(e) => this.updateQuery('query', e.target.value)}
              />
              <InputGroup.Button>
                <Button type="submit" className="pull-right" name="list" onClick={() => this.list()}>
                  Go
                </Button>
              </InputGroup.Button>
            </InputGroup>
          </Col>
          <Col lg={6} md={6} xs={12}>
            <Button className="pull-right" bsStyle="primary" onClick={() => navigate('/strategic-initiatives/add')}>
              Add Initiative
            </Button>
            <Button
              className="pull-right"
              bsStyle={showDisabled ? 'success' : 'default'}
              onClick={() => this.toggleShowDisabled()}
              style={{ marginRight: '10px' }}
            >
              Show Disabled
            </Button>
          </Col>
        </Row>
        <br />
        <Row>
          <Col md={12}>
            <BlockUi tag="div" blocking={blocking}>
              <Table striped bordered hover>
                <thead className="thead-light">
                  <tr>
                    <th role="button" onClick={() => this.sort('scheme_name')}>
                      Name
                      <i className="glyphicon glyphicon-sort" style={{ marginLeft: '5px' }} />
                    </th>
                    <th role="button" className="text-right" onClick={() => this.sort('scheme_created_at')}>
                      Created At
                      <i className="glyphicon glyphicon-sort" style={{ marginLeft: '5px' }} />
                    </th>
                    <th className="text-right">Actions</th>
                  </tr>
                </thead>
                {this.renderInitiativesList()}
              </Table>
            </BlockUi>
          </Col>
        </Row>
        <Row>
          <Col md={12}>
            <Pagination
              className="pull-right"
              prev
              next
              first
              last
              boundaryLinks
              items={totalItems}
              maxButtons={5}
              activePage={currentPage}
              onSelect={this.pageEvent}
            />
          </Col>
        </Row>
      </div>
    );
  }
}

export default StrategicInitiatives;
