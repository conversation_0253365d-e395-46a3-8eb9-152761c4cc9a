import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import to from "await-to-js";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import Loader from "../common/Loader";
import { getInitiatives, saveInitiatives, getTeam } from "../../actions/user";
import Metrics from "../Metrics/Metrics";

const StrategicInitiatives = ({ user, navigate, location }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [modalInfo, setModalInfo] = useState({ title: "", body: "" });
  const [formData, setFormData] = useState({
    initiativeNumber: 0,
    initiatives: [],
    globalTeamMetrics: [],
    selectedInitiatives: [],
  });

  const client = user?.client || {};
  const {
    lightHighlightColor,
    strategicInitiativesTabName,
    initiativesTabVisibility,
  } = client;

  useEffect(() => {
    if (
      client?.hasOwnProperty("initiativesTabVisibility") &&
      !initiativesTabVisibility
    ) {
      navigate("/challenges");
      return;
    }

    fetchInitiatives();
  }, [client, initiativesTabVisibility, navigate]);

  // useEffect(() => {
  //   // updateFormData();
  //   for (const index in formData.globalTeamMetrics) {
  //     updateFormData(index, false);
  //   }
  // }, []);

  const fetchInitiatives = async () => {
    setIsLoading(true);

    const [err, res] = await to(getInitiatives().payload);

    const team = await getTeam().payload;

    if (err) {
      toast.error("Failed to load initiatives");

      setIsLoading(false);

      return;
    }

    // Map selected initiatives to the initiatives array
    const initiatives = res.data.initiatives.map(initiative => ({
      ...initiative,
      selected: res.data.selectedInitiatives.some(
        selected => selected.id === initiative.id
      )
    }));

    // Initialize metrics with 0 values
    const globalTeamMetrics = (res.data.globalTeamMetrics || team.data.globalTeamMetrics || []).map(metric => ({
      ...metric,
      value: 0
    }));

    // Calculate metrics based on selected initiatives
    initiatives.forEach(initiative => {
      if (initiative.selected) {
        globalTeamMetrics.forEach(metric => {
          const alias = metric.alias ? metric.alias.split(" ").join("") : "";

          if (alias) {
            metric.value = (metric.value || 0) + (initiative[alias] || 0);
          }
        });
      }
    });

    // Set default values if no initiatives are selected
    // if (!initiatives.some(initiative => initiative.selected)) {
    //   globalTeamMetrics.forEach((metric, index) => {
    //     if (index === 0) metric.value = 0; // formData.globalTeamMetrics[0].defaultValue || 0;
    //     if (index === 1) metric.value = 0; // formData.globalTeamMetrics[1].defaultValue || 0;
    //     if (index === 2) metric.value = 0; // formData.globalTeamMetrics[2].defaultValue || 0;
    //   });
    // }

    // console.log("globalTeamMetrics", globalTeamMetrics);

    setFormData({
      ...res.data,
      initiatives,
      globalTeamMetrics
    });
    setIsLoading(false);
  };

  const updateFormData = (index, checked) => {
    const updatedInitiatives = [...formData.initiatives];
    const updatedMetrics = [...formData.globalTeamMetrics];
    updatedInitiatives[index].selected = checked;

    // Reset all metrics to 0
    updatedMetrics.forEach((metric) => {
      metric.value = 0;
    });

    // Recalculate metrics based on all selected initiatives
    updatedInitiatives.forEach((initiative) => {
      if (initiative && initiative.selected) {
        updatedMetrics.forEach((metric) => {
          const alias = metric.alias ? metric.alias.split(" ").join("") : "";
          if (alias) {
            metric.value = (metric.value || 0) + (initiative[alias] || 0);
          }
        });
      }
    });

    setFormData((prev) => ({
      ...prev,
      initiatives: updatedInitiatives,
      globalTeamMetrics: updatedMetrics,
    }));
  }

  const maxSelectionsReached = formData.initiatives.filter(
    initiative => initiative.selected
  ).length >= (formData.initiativeScheme?.initiativeNumber || 0);

  const handleCheckboxChange = (index, checked) => {
    const selectedCount = formData.initiatives.filter((i) => i.selected).length;
    const maxInitiatives = formData.initiativeScheme?.initiativeNumber || 0;

    if (checked && selectedCount >= maxInitiatives) {
      setModalInfo({
        title: "Maximum Initiatives Reached",
        body: `You can only select ${maxInitiatives} initiatives`,
      });
      setShowModal(true);
      return;
    }

    updateFormData(index, checked);
  };

  const handleFormSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);

    const [err] = await to(saveInitiatives(formData).payload);

    if (err) {
      toast.error("Failed to save initiatives");
      setIsLoading(false);
      return;
    }

    toast.success("Initiatives saved successfully");
    setIsLoading(false);
  };

  if (isLoading) return <Loader />;

  // console.log("globalTeamMetrics", globalTeamMetrics);

  return (
    <div className="flex flex-col md:flex-row gap-6m b-6 pt-6 px-4 mb-6">
      <div className="md:w-1/4">
        {/* Metrics Section */}
        <h2 className="text-2xl font-bold text-white mb-2 pl-4">Metrics</h2>

        <Metrics
          location={location}
          client={client}
          metrics={formData.globalTeamMetrics}
          setGlobalMetrics={(metrics) =>
            setFormData((prev) => ({ ...prev, globalTeamMetrics: metrics }))
          }
        />
      </div>
      <div className="md:w-3/4">
        {/* Main Content */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white">
              {strategicInitiativesTabName || "Initiatives"}
            </h2>

            <Button
              onClick={handleFormSubmit}
              disabled={isLoading}
              style={{ background: lightHighlightColor }}
              className="font-semibold hover:opacity-90 transition-opacity"
            >
              {isLoading ? (
                <>
                  <Loader className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Initiatives"
              )}
            </Button>
          </div>

          <div className="space-y-4 bg-transparent backdrop-blur-xl rounded-lg border border-white/10 p-6">
            {formData.initiatives.map((initiative, index) => (
              <div
                key={index}
                className="flex items-start space-x-3 pb-4 border-b border-white/10 last:border-0 last:pb-0"
              >
                <Checkbox
                  id={`initiative-${index}`}
                  checked={initiative.selected}
                  onCheckedChange={(checked) =>
                    handleCheckboxChange(index, checked)
                  }
                  disabled={!initiative.selected && maxSelectionsReached}
                  className="mt-1.5 bg-white/5 border-white/10"
                />
                <div className="flex-1">
                  <label
                    htmlFor={`initiative-${index}`}
                    className="block text-base font-medium text-white cursor-pointer"
                  >
                    {initiative.name}
                  </label>
                  <p className="mt-1 text-sm text-gray-400 leading-relaxed">
                    {initiative.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="bg-gray-900 text-white border border-white/10">
          <DialogHeader>
            <DialogTitle>{modalInfo.title}</DialogTitle>
          </DialogHeader>
          <p className="text-gray-300">{modalInfo.body}</p>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StrategicInitiatives;
