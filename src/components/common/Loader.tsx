import * as React from "react";
import { Loader2 } from "lucide-react";
import { useTheme } from "@/components/ThemeProvider/ThemeProvider";

interface LoaderProps {
  fullScreen?: boolean;
}

const Loader: React.FC<LoaderProps> = React.memo(({ fullScreen = false }) => {
  const [showLoader, setShowLoader] = React.useState(true);
  const { theme } = useTheme();

  // React.useEffect(() => {
  //   const timer = setTimeout(() => {
  //     setShowLoader(true);
  //   }, 250);

  //   return () => clearTimeout(timer);
  // }, []);

  const baseStyles = `fixed inset-0 backdrop-blur-sm z-50 flex items-center justify-center ${
    theme === 'dark'
      ? 'bg-gray-900/50'
      : 'bg-gray-200/50'
  }`;
  // const containerStyles = fullScreen
  //   ? `${baseStyles} fixed inset-0 bg-gray-900/50 backdrop-blur-sm z-50`
  //   : `${baseStyles} w-full h-full min-h-[200px]`;

  return (
    <div className={baseStyles}>
      <div className={`flex flex-col items-center gap-2 ${showLoader ? "opacity-100" : "opacity-0"} transition-opacity duration-300`}>
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>Loading...</p>
      </div>
    </div>
  );
});

export default Loader;
