import React, { Component } from 'react';
import { <PERSON>Header, InputGroup, FormControl, Button, Row, Col, Table, Pagination } from 'react-bootstrap';
import BlockUi from 'react-block-ui';
import fileDownload from 'js-file-download';

import { getClients, toggleDisabled, exportExcel } from '../../actions/clients';
import ResultLimit from '../App/ResultLimit';

class Clients extends Component {
  constructor(props) {
    super(props);
    this.state = {
      query: '',
      showDisabled: false,
      currentPage: 1,
      totalItems: 0,
      limit: 20,
      offset: 0,
      sort: 'name',
      ascending: true,
      clients: [],
    };

    this.pageEvent = this.pageEvent.bind(this);
  }

  componentDidMount() {
    this.proceedClients();
  }

  handleLimitChange(e) {
    this.setState(
      {
        limit: e.target.value,
        offset: 0,
        currentPage: 1,
      },
      () => {
        this.proceedClients();
      }
    );
  }

  updateQuery(key, value) {
    this.setState({
      [key]: value,
      currentPage: 1,
      offset: 0,
    });
  }

  sort(column) {
    this.setState(
      (prevState) => ({
        ascending: !prevState.ascending,
        sort: column,
        currentPage: 1,
        offset: 0,
      }),
      () => {
        this.proceedClients();
      }
    );
  }

  pageEvent(e) {
    const { limit } = this.state;
    console.log('setting offset: ', (e - 1) * limit);
    this.setState(
      {
        currentPage: e,
        offset: (e - 1) * limit,
      },
      () => {
        this.proceedClients();
      }
    );
  }

  proceedClients() {
    const { query, limit, offset, sort, ascending, showDisabled } = this.state;
    const { alert } = this.props;
    const q = {
      query,
      limit,
      offset,
      sort,
      ascending,
      showDisabled,
    };

    this.blockUi();
    getClients(q).payload.then(
      (results) => {
        console.log('got clients: ', results);
        this.setState({ clients: results.data });

        if (results.data.length)
          this.setState({
            totalItems: Math.ceil(results.data[0].total / limit),
          });

        this.unBlockUi();
      },
      (error) => {
        console.log(error);
        alert('danger', 'Error', `Error listing teams: ${error.toString()}`);
        this.unBlockUi();
      }
    );
  }

  viewClient(id) {
    const { navigate } = this.props;

    navigate(`/clients/${id}`);
  }

  blockUi() {
    this.setState({
      blocking: true,
    });
  }

  unBlockUi() {
    this.setState({
      blocking: false,
    });
  }

  exportClient(client) {
    exportExcel(client.id).then((response) => {
      fileDownload(response, `${client.name}.xlsx`);
    });
  }

  toggleClientDisable(client) {
    this.blockUi();
    toggleDisabled(client.id).payload.then(() => {
      this.proceedClients();
    });
  }

  toggleShowDisabled() {
    const { showDisabled } = this.state;

    this.setState(
      {
        showDisabled: !showDisabled,
      },
      () => {
        this.proceedClients();
      }
    );
  }

  renderButtons(client) {
    let disable;

    if (client.disabled)
      disable = (
        <Button
          bsSize="xsmall"
          bsStyle="info"
          onClick={() => this.toggleClientDisable(client)}
          style={{ marginRight: '10px' }}
        >
          Enable
        </Button>
      );
    else
      disable = (
        <Button bsSize="xsmall" onClick={() => this.toggleClientDisable(client)} style={{ marginRight: '10px' }}>
          Disable
        </Button>
      );

    return (
      <td className="text-right">
        <Button bsSize="xsmall" onClick={() => this.viewClient(client.id)} style={{ marginRight: '10px' }}>
          View
        </Button>
        {disable}
        <Button
          bsSize="xsmall"
          onClick={() => {
            this.exportClient(client);
          }}
        >
          Download
        </Button>
      </td>
    );
  }

  renderClientsList() {
    const { clients } = this.state;
    return (
      <tbody>
        {clients.map((client) => (
          <tr key={client.id}>
            <td>{client.name}</td>
            <td className="text-right">{client.created}</td>
            {this.renderButtons(client)}
          </tr>
        ))}
      </tbody>
    );
  }

  render() {
    const { showDisabled, blocking, totalItems, currentPage } = this.state;
    const { navigate } = this.props;
    return (
      <div className="container root-container">
        <PageHeader>Clients</PageHeader>
        <Row>
          <Col lg={2} md={2} xs={6}>
            <ResultLimit onChange={(e) => this.handleLimitChange(e)} />
          </Col>
          <Col lg={4} md={4} xs={6}>
            <InputGroup>
              <FormControl
                type="text"
                placeholder="Search"
                name="query"
                onChange={(e) => this.updateQuery('query', e.target.value)}
              />
              <InputGroup.Button>
                <Button type="submit" className="pull-right" name="list" onClick={() => this.proceedClients()}>
                  Go
                </Button>
              </InputGroup.Button>
            </InputGroup>
          </Col>
          <Col lg={6} md={6} xs={12}>
            <Button className="pull-right" bsStyle="primary" onClick={() => navigate('/clients/new')}>
              Add Client
            </Button>
            {/* <Button */}
            {/*  className="pull-right" */}
            {/*  bsStyle="success" */}
            {/*  onClick={() => { this.exportAllClients(); }} */}
            {/* > */}
            {/*  Download All Data */}
            {/* </Button> */}
            <Button
              className="pull-right"
              bsStyle={showDisabled ? 'success' : 'default'}
              onClick={() => this.toggleShowDisabled()}
              style={{ marginRight: '10px' }}
            >
              Show Disabled
            </Button>
          </Col>
        </Row>
        <br />
        <Row>
          <Col md={12}>
            <BlockUi tag="div" blocking={blocking}>
              <Table striped bordered hover>
                <thead className="thead-light">
                  <tr>
                    <th role="button" onClick={() => this.sort('name')}>
                      Client Name
                      <i className="glyphicon glyphicon-sort" style={{ marginLeft: '5px' }} />
                    </th>
                    <th className="text-right" role="button" onClick={() => this.sort('created_at')}>
                      Created At
                      <i className="glyphicon glyphicon-sort" style={{ marginLeft: '5px' }} />
                    </th>
                    <th className="text-right">Actions</th>
                  </tr>
                </thead>
                {this.renderClientsList()}
              </Table>
            </BlockUi>
          </Col>
        </Row>
        <Row>
          <Col md={12}>
            <Pagination
              className="pull-right"
              prev
              next
              first
              last
              boundaryLinks
              items={totalItems}
              maxButtons={5}
              activePage={currentPage}
              onSelect={this.pageEvent}
            />
          </Col>
        </Row>
      </div>
    );
  }
}

export default Clients;
