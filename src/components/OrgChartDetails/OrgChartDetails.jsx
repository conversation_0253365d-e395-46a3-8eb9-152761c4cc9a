import React, { useState, useEffect, useCallback, useMemo } from 'react';
import BlockUi from 'react-block-ui';
import cn from 'classnames';
import to from 'await-to-js';
import { <PERSON><PERSON>, Col, ControlLabel, FormGroup, PageHeader, Row, Tab, Tabs } from 'react-bootstrap';
import { nanoid } from 'nanoid';

import OrgChartTypeTabs from './components/OrgChartTypeTabs/OrgChartTypeTabs';
import OrgChartTable from './components/OrgChartTable/OrgChartTable';
import { createOrgChart, updateOrgChart, getOrgChart } from '../../actions/orgCharts';
import { createTreeFromArray } from './services/arrayToTree';
import { createArrayFromTree } from './services/treeToArray';

import styles from './org-chart-details.module.scss';

const flatArrayUser = {
  id: nanoid(10),
  name: '',
  title: '',
  photo: '',
  status: -1,
  meet_1_text: '',
  meet_1_points: 0,
  meet_2_text: '',
  meet_2_points: 0,
  meet_3_text: '',
  meet_3_points: 0,
  parent_id: null,
  locked_by_id: null,
};
const treeUser = {
  ...flatArrayUser,
  children: [],
};
const availableOrgChartTypes = {
  global: 'global',
  internal: 'internal',
};

const defaultOrgChartTypes = [
  {
    id: 1,
    name: '',
    is_visible: true,
    type: availableOrgChartTypes.global,
    orgChartUsersFlatArray: [flatArrayUser],
    orgChartUsersTree: [treeUser],
  },
  {
    id: 2,
    name: '',
    is_visible: true,
    type: availableOrgChartTypes.internal,
    orgChartUsersFlatArray: [flatArrayUser],
    orgChartUsersTree: [treeUser],
  },
];

const defaultOrgChart = [
  {
    id: nanoid(10),
    orgChartTypes: defaultOrgChartTypes,
  },
];

const mapDefaultOrgChartTypes = (orgCharts) =>
  orgCharts.reduce((acc, curr) => {
    const { id, orgChartTypes } = curr;

    acc[id] = orgChartTypes[0].id;

    return acc;
  }, {});

export default function OrgChartDetails(props) {
  const { params, alert, navigate } = props;
  const [name, setName] = useState('');
  const [isLoading, setLoading] = useState(false);
  const [isNewOrgChart, setIsNewOrgChart] = useState(params.orgChartId === 'new');
  const [orgCharts, setOrgCharts] = useState(defaultOrgChart);
  const [selectedOrgChartId, setSelectedOrgChartId] = useState(orgCharts[0].id);
  const [selectedOrgChartTypeIds, setSelectedOrgChartTypeIds] = useState(mapDefaultOrgChartTypes(orgCharts));
  const getCurrentLevelForOrgChartUser = useCallback((user, users) => {
    let level = 1;

    if (!user.parent_id) return level;

    let parentUser = users.find(({ id }) => id === user.parent_id);

    while (parentUser) {
      ++level;

      // eslint-disable-next-line no-loop-func
      parentUser = users.find(({ id }) => id === parentUser.parent_id);
    }

    return level;
  }, []);
  const mapOrgChartUsers = useCallback(
    (user, users) => ({
      ...user,
      level: getCurrentLevelForOrgChartUser(user, users),
    }),
    []
  );
  const selectedOrgChart = useMemo(
    () =>
      orgCharts.find((orgChart) => {
        const { id } = orgChart;

        return id === selectedOrgChartId;
      }),
    [orgCharts, selectedOrgChartId]
  );
  const selectedOrgChartType = useMemo(() => {
    if (!selectedOrgChart) return {};

    const { orgChartTypes } = selectedOrgChart;

    return orgChartTypes.find(({ id }) => id === selectedOrgChartTypeIds[selectedOrgChart.id]);
  }, [selectedOrgChartTypeIds, selectedOrgChart]);

  useEffect(() => {
    const isNew = params.orgChartId === 'new';

    if (isNew) {
      setSelectedOrgChartId(orgCharts[0].id);
      setSelectedOrgChartTypeIds(mapDefaultOrgChartTypes(orgCharts));
      return;
    }

    setIsNewOrgChart(false);
    proceedOrgChartDetails(params.orgChartId);
  }, [isNewOrgChart, params.orgChartId]);

  async function proceedOrgChartDetails(orgChartId) {
    setLoading(true);

    const [err, res] = await to(getOrgChart(orgChartId).payload);

    setLoading(false);

    if (err) return alert('danger', 'Error', 'Oops, something went wrong with fetching org chart details');

    const { name, orgCharts } = res.data;

    setName(name);

    if (!orgCharts) return;

    setSelectedOrgChartId(orgCharts[0].id);
    setSelectedOrgChartTypeIds(mapDefaultOrgChartTypes(orgCharts));

    const mappedOrgCharts = orgCharts.map((orgChart) => {
      const { orgChartTypes } = orgChart;
      const mappedOrgChartTypes = orgChartTypes.map((orgChartType) => {
        const { users } = orgChartType;
        let mappedUsers = users;

        if (orgChartType.type !== availableOrgChartTypes.internal)
          mappedUsers = users.map((user) => mapOrgChartUsers(user, users));

        return {
          ...orgChartType,
          orgChartUsersFlatArray: mappedUsers,
          orgChartUsersTree: createTreeFromArray(mappedUsers),
        };
      });

      return {
        ...orgChart,
        orgChartTypes: mappedOrgChartTypes,
      };
    });

    setOrgCharts(mappedOrgCharts);
  }

  async function saveOrgChart() {
    if (!validate()) return;

    setLoading(true);

    const modifiedOrgCharts = orgCharts.map((orgChart) => {
      const { orgChartTypes } = orgChart;
      const modifiedOrgChartTypes = orgChartTypes.map((orgChartType) => {
        const modifiedUsers = orgChartType.orgChartUsersFlatArray.map((u) => {
          const { level, ...rest } = u;

          return {
            ...rest,
          };
        });

        const { orgChartUsersFlatArray, orgChartUsersTree, ...rest } = orgChartType;

        return {
          ...rest,
          users: createTreeFromArray(modifiedUsers),
        };
      });

      return {
        ...orgChart,
        orgChartTypes: modifiedOrgChartTypes,
      };
    });

    const payload = {
      name,
      orgCharts: modifiedOrgCharts,
    };

    let err;
    let res;

    if (!isNewOrgChart) [err, res] = await to(updateOrgChart({ ...payload, id: params.orgChartId }).payload);
    else [err, res] = await to(createOrgChart(payload).payload);

    setLoading(false);

    if (err) return alert('danger', 'Error', 'Oops, something went wrong with saving org chart details');

    alert('success', 'Success', `Org Chart was successfully ${isNewOrgChart ? 'created' : 'updated'}`);

    if (isNewOrgChart) {
      const { id } = res.data;

      navigate(`/org-charts/${id}`, { replace: true });
    } else {
      proceedOrgChartDetails(res.data.id);
    }
  }

  function validate() {
    if (!name) {
      alert('danger', 'Error', 'Name is required field');
      return false;
    }

    for (let i = 0, n = orgCharts.length; i < n; ++i) {
      const { orgChartTypes } = orgCharts[i];
      const prefix = `[Org Chart #${i + 1}]`;

      for (let j = 0, k = orgChartTypes.length; j < k; ++j) {
        const { name, type, orgChartUsersFlatArray } = orgChartTypes[j];

        if (!name) {
          alert('danger', 'Error', `${prefix} Name is required field for Org Chart Type: ${type}`);
          return false;
        }

        for (let l = 0, m = orgChartUsersFlatArray.length; l < m; ++l) {
          const { name, title, status } = orgChartUsersFlatArray[l];

          if (!name) {
            alert(
              'danger',
              'Error',
              `${prefix} [Type: ${type}] Name is required field. It's empty for the user with the title: ${title}`
            );
            return false;
          }

          if (!title) {
            alert(
              'danger',
              'Error',
              `${prefix} [Type: ${type}] Title is required field. It's empty for the user with the name: ${name}`
            );
            return false;
          }

          if (typeof status !== 'number') {
            alert(
              'danger',
              'Error',
              `${prefix} [Type: ${type}] Status is required field. It's empty for the user with the name: ${name}`
            );
            return false;
          }
        }
      }
    }

    return true;
  }

  function updateOrgChartTypes(key, value, orgChartType) {
    return setOrgCharts((prev) =>
      prev.map((e) => {
        if (e.id !== selectedOrgChartId) return e;

        const mappedOrgChartTypes = e.orgChartTypes.map((e) => {
          if (e.id !== orgChartType.id) return e;

          return {
            ...e,
            [key]: value,
          };
        });

        return {
          ...e,
          orgChartTypes: mappedOrgChartTypes,
        };
      })
    );
  }

  function updateOrgChartUsers(orgChartUser) {
    return setOrgCharts((prev) =>
      prev.map((orgChart) => {
        if (orgChart.id !== selectedOrgChartId) return orgChart;

        const mappedOrgChartTypes = orgChart.orgChartTypes.map((orgChartType) => {
          if (orgChartType.id !== selectedOrgChartTypeIds[selectedOrgChartId]) return orgChartType;

          const updatedUsers = orgChartType.orgChartUsersFlatArray.map((e) => {
            if (e.id !== orgChartUser.id) return e;

            delete orgChartUser.id;

            return {
              ...e,
              ...orgChartUser,
            };
          });

          return {
            ...orgChartType,
            orgChartUsersFlatArray: updatedUsers,
            orgChartUsersTree: createTreeFromArray(updatedUsers),
          };
        });

        return {
          ...orgChart,
          orgChartTypes: mappedOrgChartTypes,
        };
      })
    );
  }

  function addOrgChartUser(parentUser) {
    return setOrgCharts((prev) =>
      prev.map((orgChart) => {
        if (orgChart.id !== selectedOrgChartId) return orgChart;

        const mappedOrgChartTypes = orgChart.orgChartTypes.map((orgChartType) => {
          if (orgChartType.id !== selectedOrgChartTypeIds[selectedOrgChartId]) return orgChartType;

          let updatedUsers = [];
          const newUser = {
            ...flatArrayUser,
            id: nanoid(10),
          };

          if (parentUser) newUser.parent_id = parentUser.id;

          if (orgChartType.type !== availableOrgChartTypes.internal) {
            newUser.level = getCurrentLevelForOrgChartUser(newUser, orgChartType.orgChartUsersFlatArray);
            updatedUsers = [newUser, ...orgChartType.orgChartUsersFlatArray];
          } else {
            updatedUsers = [...orgChartType.orgChartUsersFlatArray, newUser];
          }

          return {
            ...orgChartType,
            orgChartUsersFlatArray: updatedUsers,
            orgChartUsersTree: createTreeFromArray(updatedUsers),
          };
        });

        return {
          ...orgChart,
          orgChartTypes: mappedOrgChartTypes,
        };
      })
    );
  }

  function removeOrgChartUser(user) {
    return setOrgCharts((prev) =>
      prev.map((orgChart) => {
        if (orgChart.id !== selectedOrgChartId) return orgChart;

        const mappedOrgChartTypes = orgChart.orgChartTypes.map((orgChartType) => {
          if (orgChartType.id !== selectedOrgChartTypeIds[selectedOrgChartId]) return orgChartType;

          // Make flat array from the tree node
          const userIds = new Set();
          const treeNodeFlatArray = createArrayFromTree([user]);

          treeNodeFlatArray.forEach(({ id }) => {
            userIds.add(id.toString());
          });

          let updatedUsers = orgChartType.orgChartUsersFlatArray.filter(({ id }) => !userIds.has(id.toString()));

          updatedUsers = updatedUsers.map((u) => {
            if (!u.locked_by_id) return u;

            if (userIds.has(u.locked_by_id.toString()))
              return {
                ...u,
                locked_by_id: null,
              };

            return u;
          });

          return {
            ...orgChartType,
            orgChartUsersFlatArray: updatedUsers,
            orgChartUsersTree: createTreeFromArray(updatedUsers),
          };
        });

        return {
          ...orgChart,
          orgChartTypes: mappedOrgChartTypes,
        };
      })
    );
  }

  function addOrgChart() {
    if (orgCharts.length >= 3) return;

    setOrgCharts((prev) => {
      const newOrgChartId = nanoid(10);
      const newOrgChart = { ...defaultOrgChart[0], id: newOrgChartId };
      const updatedOrgCharts = [...prev, newOrgChart];

      setSelectedOrgChartTypeIds((prev) => ({ ...prev, [newOrgChartId]: newOrgChart.orgChartTypes[0].id }));

      return updatedOrgCharts;
    });
  }

  function deleteOrgChart() {
    const removalIndex = orgCharts.findIndex(({ id }) => id === selectedOrgChartId);

    if (removalIndex === 0) setSelectedOrgChartId(orgCharts[1].id);
    else setSelectedOrgChartId(orgCharts[removalIndex - 1].id);

    setOrgCharts((prev) => prev.filter(({ id }) => id !== selectedOrgChartId));
  }

  return (
    <div className="container root-container">
      <BlockUi tag="div" blocking={isLoading}>
        <Row>
          <Col md={10}>
            <PageHeader>{isNewOrgChart ? 'Create New Org Chart Scheme' : 'Update Org Chart Scheme'}</PageHeader>
          </Col>
          <Col md={2}>
            <Button className="btn-info save-button pull-right" onClick={saveOrgChart}>
              {isNewOrgChart ? 'Create' : 'Save'}
            </Button>
          </Col>
        </Row>

        <form>
          <Row>
            <Col md={6}>
              <FormGroup>
                <ControlLabel>Org Chart Scheme Name*</ControlLabel>
                <input
                  className="form-control"
                  type="text"
                  name="org-chart-name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter org chart scheme's name"
                />
              </FormGroup>
            </Col>
          </Row>

          <hr className="margin-top-5 margin-bottom-20" />
          <div className={styles.flexRow}>
            <h3 className={styles.header}>Org Charts</h3>
            <div>
              {orgCharts.length > 1 ? (
                <Button className="btn-danger" onClick={deleteOrgChart} style={{ marginRight: '10px' }}>
                  Delete Org Chart
                </Button>
              ) : null}

              <Button className="btn-primary" onClick={addOrgChart} disabled={orgCharts.length >= 3}>
                + Add Org Chart
              </Button>
            </div>
          </div>
          <Row>
            <Col md={12}>
              <Tabs
                onSelect={(e) => setSelectedOrgChartId(e)}
                activeKey={selectedOrgChartId}
                id="org-chart-tabs-container"
                bsStyle="pills"
              >
                {orgCharts.map((orgChart, index) => (
                  <Tab eventKey={orgChart.id} title={`Org Chart #${index + 1}`} key={orgChart.id}>
                    <Row>
                      <Col md={12}>
                        <OrgChartTypeTabs
                          tabs={orgChart.orgChartTypes}
                          onUpdate={updateOrgChartTypes}
                          selectedOrgChartTypeId={selectedOrgChartTypeIds[orgChart.id]}
                          setSelectedOrgChartTypeId={(e) =>
                            setSelectedOrgChartTypeIds((prev) => ({ ...prev, [orgChart.id]: e }))
                          }
                        />
                      </Col>
                      <Col md={12}>
                        {selectedOrgChartType?.type === availableOrgChartTypes.internal ? (
                          <>
                            <Button className="btn-primary pull-right" onClick={() => addOrgChartUser()}>
                              + Add Internal Stakeholder
                            </Button>
                            <Button
                              className={cn('pull-right', {
                                'btn-success ': !selectedOrgChartType.is_visible,
                                'btn-danger ': selectedOrgChartType.is_visible,
                              })}
                              onClick={() =>
                                updateOrgChartTypes(
                                  'is_visible',
                                  !selectedOrgChartType.is_visible,
                                  selectedOrgChartType
                                )
                              }
                              style={{ marginRight: '10px', marginBottom: '10px' }}
                            >
                              {selectedOrgChartType.is_visible ? 'Hide internal chart' : 'Show internal chart'}
                            </Button>
                          </>
                        ) : null}

                        <OrgChartTable
                          alert={alert}
                          selectedOrgChartType={selectedOrgChartType}
                          flatUsersArray={selectedOrgChartType?.orgChartUsersFlatArray || []}
                          users={selectedOrgChartType?.orgChartUsersTree || []}
                          onUpdate={updateOrgChartUsers}
                          onAdd={addOrgChartUser}
                          onRemove={removeOrgChartUser}
                        />
                      </Col>
                    </Row>
                  </Tab>
                ))}
              </Tabs>
            </Col>
          </Row>
        </form>
      </BlockUi>
    </div>
  );
}
