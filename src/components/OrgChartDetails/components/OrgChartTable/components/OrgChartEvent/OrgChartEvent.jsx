import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalTitle,
  ModalBody,
  ModalFooter,
  ControlLabel,
  FormControl,
  FormGroup,
  Button,
} from 'react-bootstrap';

export default function OrgChartEvent(props) {
  const {
    alert,
    name,
    meetingIndex = 1,
    pointsValue,
    descriptionValue,
    onClose = () => {},
    onChange = () => {},
  } = props;
  const [points, setPoints] = useState(pointsValue);
  const [description, setDescription] = useState(descriptionValue);

  function onSave() {
    if (!points) return alert('danger', 'Error', 'Points Cost should not be empty');

    if (parseInt(points, 10) < 0) return alert('danger', 'Error', 'Points Cost should not be negative');

    if (!description) return alert('danger', 'Error', 'Description should not be empty');

    onChange({ points: parseInt(points, 10), description });
  }

  return (
    <Modal show bsSize="small" onHide={onClose}>
      <ModalHeader closeButton>
        <ModalTitle>
          {name}: Meeting&nbsp;
          {meetingIndex}
        </ModalTitle>
      </ModalHeader>
      <ModalBody>
        <FormGroup>
          <ControlLabel>Points Cost</ControlLabel>
          <FormControl
            type="number"
            name="points"
            value={points}
            min={0}
            onChange={(e) => setPoints(e.target.value)}
            placeholder="Enter points cost"
          />
        </FormGroup>
        <FormGroup>
          <ControlLabel>Description</ControlLabel>
          <FormControl
            componentClass="textarea"
            name="description"
            placeholder="Enter description text"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
          />
        </FormGroup>
      </ModalBody>
      <ModalFooter>
        <Button className="pull-right" bsStyle="success" onClick={onSave}>
          Save
        </Button>
      </ModalFooter>
    </Modal>
  );
}
