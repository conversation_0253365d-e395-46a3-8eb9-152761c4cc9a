import React, { useState } from 'react';
import to from 'await-to-js';
import cn from 'classnames';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>ead<PERSON>,
  <PERSON>dal<PERSON><PERSON>le,
  ModalBody,
  Modal<PERSON>ooter,
  ControlLabel,
  FormGroup,
  Button,
} from 'react-bootstrap';

import { uploadImage } from '../../../../../../actions/clients';

import styles from '../../../../../ClientDetails/client-details.module.scss';

export default function OrgChartPhoto(props) {
  const { alert, name, photo, onChange, onClose = () => {} } = props;
  const [isLoading, setLoading] = useState(false);
  const [url, setUrl] = useState(photo);

  function onSave() {
    if (isLoading) return;

    onChange({ photo: url });
    onClose();
  }

  async function setImage() {
    setLoading(true);

    const file = document.getElementById('photo').files[0];
    const [err, res] = await to(uploadImage(file));

    setLoading(false);

    if (err) return alert('danger', 'Error', 'Photo was not uploaded.');

    const { url } = res.data;

    alert('success', 'Success', 'Image was successfully uploaded');

    setUrl(url);
  }

  function removeImage() {
    document.getElementById('photo').value = '';

    setUrl(null);
  }

  return (
    <Modal show bsSize="small" onHide={onClose}>
      <ModalHeader closeButton>
        <ModalTitle>{name}: Photo</ModalTitle>
      </ModalHeader>
      <ModalBody>
        <FormGroup>
          <ControlLabel>{"User's Photo"}</ControlLabel>

          {url ? (
            <div className={styles.preview}>
              <img src={url} alt="users-photo" className={cn('img-responsive', styles.img)} />

              <Button bsStyle="danger" className={styles.button} onClick={removeImage}>
                Remove photo
              </Button>
            </div>
          ) : null}

          <input
            id="photo"
            className="form-control"
            type="file"
            name="photo"
            accept=".png,.jpg,.jpeg,.svg"
            onChange={setImage}
          />
        </FormGroup>
      </ModalBody>
      <ModalFooter>
        <Button className="pull-right" bsStyle="success" onClick={onSave} disabled={isLoading}>
          Save
        </Button>
      </ModalFooter>
    </Modal>
  );
}
