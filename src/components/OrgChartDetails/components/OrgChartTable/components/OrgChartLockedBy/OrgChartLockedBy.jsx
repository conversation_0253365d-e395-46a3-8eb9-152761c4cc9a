import React, { useCallback, useMemo, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalTitle,
  ModalBody,
  ModalFooter,
  ControlLabel,
  FormControl,
  FormGroup,
  Button,
} from 'react-bootstrap';

export default function OrgChartLockedBy(props) {
  const { id: currentUserId, name, users, lockedById, onClose = () => {}, onChange = () => {} } = props;
  const [selectedUser, setSelectedUser] = useState(lockedById || 0);
  const buildOptionLabel = useCallback(
    (user) => {
      const { name, title } = user;

      if (!title) return name || 'N/A';

      return `${name || 'N/A'} (${title})`;
    },
    [users]
  );
  /**
   * Don't show current user
   * Don't show users with name
   * Don't show user, if this user is related on currentUser
   */
  const filteredUsers = useMemo(() => {
    if (!currentUserId) return users.filter((u) => u.name);

    return users
      .filter((u) => u.id.toString() !== currentUserId.toString())
      .filter((u) => u.name)
      .filter((u) => !u.locked_by_id || u.locked_by_id.toString() !== currentUserId.toString());
  }, [currentUserId, users]);

  function onSave() {
    onChange({ lockedById: selectedUser });
    onClose();
  }

  return (
    <Modal show bsSize="small" onHide={onClose}>
      <ModalHeader closeButton>
        <ModalTitle>
          Lock&nbsp;
          {name}
        </ModalTitle>
      </ModalHeader>
      <ModalBody>
        <FormGroup>
          <ControlLabel>Unlock by meeting with:</ControlLabel>
          <FormControl
            componentClass="select"
            name="lockedById"
            value={selectedUser}
            onChange={(e) => setSelectedUser(e.target.value)}
          >
            <option value={0}>Select user...</option>
            {filteredUsers.map((user) => (
              <option key={user.id} value={user.id}>
                {buildOptionLabel(user)}
              </option>
            ))}
          </FormControl>
        </FormGroup>
      </ModalBody>
      <ModalFooter>
        {selectedUser ? (
          <Button
            className="pull-left"
            bsStyle="primary"
            onClick={() => {
              onChange({ lockedById: 0 });
              onClose();
            }}
          >
            Unlock
          </Button>
        ) : null}

        <Button className="pull-right" bsStyle="success" onClick={onSave}>
          Save
        </Button>
      </ModalFooter>
    </Modal>
  );
}
