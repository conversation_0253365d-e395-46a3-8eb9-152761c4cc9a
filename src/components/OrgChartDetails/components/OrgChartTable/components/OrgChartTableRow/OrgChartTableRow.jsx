import React from 'react';

import { Button } from 'react-bootstrap';

import styles from './org-chart-table-row.module.scss';

import Icon from '../../images/new-line.svg';

export default function OrgChartTableRow(props) {
  const {
    user,
    index = 0,
    selectedOrgChartType = {},
    setSelectedUser = () => {},
    onAdd = () => {},
    onRemove = () => {},
    onUpdate = () => {},
  } = props;

  function renderLevelSpace(user) {
    switch (user.level) {
      case 1:
        return null;
      case 2:
        return <img src={Icon} />;
      case 3:
        return (
          <>
            <img src={Icon} style={{ visibility: 'hidden' }} />
            <img src={Icon} />
          </>
        );
      case 4:
        return (
          <>
            <img src={Icon} style={{ visibility: 'hidden' }} />
            <img src={Icon} style={{ visibility: 'hidden' }} />
            <img src={Icon} />
          </>
        );
    }
  }

  return (
    <tr>
      <td>
        <div className={styles.flexRow}>
          {renderLevelSpace(user)}
          <input
            className="form-control"
            type="text"
            name="user-name"
            value={user.name}
            onChange={(e) => onUpdate({ id: user.id, name: e.target.value })}
            placeholder="Enter user's name"
          />
        </div>
      </td>
      <td>
        <input
          className="form-control"
          type="text"
          name="user-title"
          value={user.title}
          onChange={(e) => onUpdate({ id: user.id, title: e.target.value })}
          placeholder="Enter user's title"
        />
      </td>
      <td>
        <input
          className="form-control"
          type="number"
          name="user-status"
          value={user.status}
          onChange={(e) => onUpdate({ id: user.id, status: parseInt(e.target.value, 10) })}
          placeholder="Enter user's status"
        />
      </td>
      <td className="text-center">
        <Button
          bsStyle={user.meet_1_text ? 'primary' : 'default'}
          onClick={() => setSelectedUser({ user, meta: { type: 'meeting', meetingIndex: 1 } })}
        >
          Edit
        </Button>
      </td>
      <td className="text-center">
        <Button
          bsStyle={user.meet_2_text ? 'primary' : 'default'}
          onClick={() => setSelectedUser({ user, meta: { type: 'meeting', meetingIndex: 2 } })}
          disabled={!user.meet_1_text}
        >
          Edit
        </Button>
      </td>
      <td className="text-center">
        <Button
          bsStyle={user.meet_3_text ? 'primary' : 'default'}
          onClick={() => setSelectedUser({ user, meta: { type: 'meeting', meetingIndex: 3 } })}
          disabled={!user.meet_2_text}
        >
          Edit
        </Button>
      </td>
      <td className="text-right">
        {index !== 0 ? (
          <Button bsStyle="danger" style={{ marginRight: '10px' }} onClick={() => onRemove(user)}>
            Delete
          </Button>
        ) : null}

        <Button
          style={{ marginRight: '10px' }}
          bsStyle={user.locked_by_id ? 'primary' : 'warning'}
          onClick={() => setSelectedUser({ user, meta: { type: 'locked_by' } })}
        >
          Lock
        </Button>

        <Button
          style={{ marginRight: '10px' }}
          bsStyle={user.photo ? 'primary' : 'default'}
          onClick={() => setSelectedUser({ user, meta: { type: 'photo' } })}
        >
          + Photo
        </Button>

        {selectedOrgChartType?.type !== 'internal' ? (
          <Button
            style={{ marginRight: '10px', visibility: user.level === 4 ? 'hidden' : 'visible' }}
            bsStyle="default"
            onClick={() => onAdd(user)}
          >
            + Stakeholder
          </Button>
        ) : null}
      </td>
    </tr>
  );
}
