import React, { useState } from 'react';
import { Table } from 'react-bootstrap';

import OrgChartEvent from './components/OrgChartEvent/OrgChartEvent';

import OrgChartTableRow from './components/OrgChartTableRow/OrgChartTableRow';
import OrgChartPhoto from './components/OrgChartPhoto/OrgChartPhoto';
import OrgChartLockedBy from './components/OrgChartLockedBy/OrgChartLockedBy';

export default function OrgChartTable(props) {
  const {
    alert,
    users,
    flatUsersArray,
    selectedOrgChartType = {},
    onUpdate = () => {},
    onAdd = () => {},
    onRemove = () => {},
  } = props;
  const [selectedUser, setSelectedUser] = useState({
    user: null,
    meta: null,
  });

  /**
   * Recursively render tree
   * @param users
   * @returns {JSX.Element}
   */
  function renderUsers(users, currentIndex = -1) {
    return users.map((user) => {
      ++currentIndex;

      return (
        <>
          <OrgChartTableRow
            key={user.id}
            user={user}
            index={currentIndex}
            selectedOrgChartType={selectedOrgChartType}
            setSelectedUser={setSelectedUser}
            onAdd={onAdd}
            onUpdate={onUpdate}
            onRemove={onRemove}
          />

          {user.children && user.children.length ? renderUsers(user.children, currentIndex) : null}
        </>
      );
    });
  }

  function updateSelectedUser({ photo = '', points, description, lockedById = null }) {
    const {
      meta: { meetingIndex },
      user,
    } = selectedUser;

    if (points && description) {
      const pointsKey = `meet_${meetingIndex}_points`;
      const descriptionKey = `meet_${meetingIndex}_text`;

      onUpdate({ id: user.id, [pointsKey]: points, [descriptionKey]: description });
    } else if (photo || photo === null) {
      onUpdate({ id: user.id, photo });
    } else if (lockedById === 0 || lockedById === '0') {
      onUpdate({ id: user.id, locked_by_id: null });
    } else if (lockedById) {
      onUpdate({ id: user.id, locked_by_id: lockedById });
    }

    setSelectedUser({ user: null, meet: null });
  }

  return (
    <>
      <Table striped bordered hover>
        <thead>
          <tr>
            <th className="text-center">Name</th>
            <th className="text-center">Title</th>
            <th className="text-center" style={{ width: '80px' }}>
              Status
            </th>
            <th className="text-center">Meet 1</th>
            <th className="text-center">Meet 2</th>
            <th className="text-center">Meet 3</th>
            <th className="text-center" style={{ width: '370px' }}>
              Actions
            </th>
          </tr>
        </thead>
        <tbody>{users && users.length ? renderUsers(users) : null}</tbody>
      </Table>

      {selectedUser.user && selectedUser.meta.type === 'meeting' ? (
        <OrgChartEvent
          alert={alert}
          name={selectedUser.user.name || 'N/A'}
          meetingIndex={selectedUser.meta.meetingIndex}
          pointsValue={selectedUser.user[`meet_${selectedUser.meta.meetingIndex}_points`]}
          descriptionValue={selectedUser.user[`meet_${selectedUser.meta.meetingIndex}_text`]}
          onClose={() => setSelectedUser({ user: null, meta: null })}
          onChange={updateSelectedUser}
        />
      ) : null}

      {selectedUser.user && selectedUser.meta.type === 'photo' ? (
        <OrgChartPhoto
          alert={alert}
          name={selectedUser.user.name || 'N/A'}
          photo={selectedUser.user.photo}
          onClose={() => setSelectedUser({ user: null, meta: null })}
          onChange={updateSelectedUser}
        />
      ) : null}

      {selectedUser.user && selectedUser.meta.type === 'locked_by' ? (
        <OrgChartLockedBy
          id={selectedUser.user.id}
          name={selectedUser.user.name || 'N/A'}
          users={flatUsersArray}
          lockedById={selectedUser.user.locked_by_id}
          onClose={() => setSelectedUser({ user: null, meta: null })}
          onChange={updateSelectedUser}
        />
      ) : null}
    </>
  );
}
