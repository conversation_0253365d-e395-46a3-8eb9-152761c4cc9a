import React from 'react';
import { Col, FormGroup, Row, Tab, Tabs } from 'react-bootstrap';

const tabTitles = {
  global: 'Main Chart',
  internal: 'Internal Chart',
};

export default function OrgChartTypeTabs(props) {
  const { tabs, onUpdate = () => {}, selectedOrgChartTypeId, setSelectedOrgChartTypeId } = props;

  return (
    <Tabs onSelect={(e) => setSelectedOrgChartTypeId(e)} activeKey={selectedOrgChartTypeId} id="tabs-container">
      {tabs.map((tab) => (
        <Tab eventKey={tab.id} title={tabTitles[tab.type]} key={tab.id}>
          <Row>
            <Col md={12}>
              <FormGroup>
                <input
                  className="form-control"
                  type="text"
                  name="org-chart-type-name"
                  value={tab.name}
                  onChange={(e) => onUpdate('name', e.target.value, tab)}
                  placeholder={'Header Tab Name & Title (e.g. "Organization")'}
                />
              </FormGroup>
            </Col>
          </Row>
        </Tab>
      ))}
    </Tabs>
  );
}
