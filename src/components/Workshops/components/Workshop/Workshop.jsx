import React from "react";
import { useTheme } from "@/components/ThemeProvider/ThemeProvider";

const Workshop = ({ id, name, workshopImage, onUpdate }) => {
  const { theme } = useTheme();

  return (
    <div
      onClick={() => onUpdate(id)}
      className="cursor-pointer transform transition-all duration-300 hover:scale-105 w-full"
    >
      <div className={`backdrop-blur-xl rounded-2xl overflow-hidden shadow-xl ${
        theme === 'dark'
          ? 'bg-white/5 border border-white/10'
          : 'bg-white/80 border border-gray-300/30'
      }`}>
        <div className="p-4">
          <h3 className={`text-lg font-semibold mb-4 ${
            theme === 'dark' ? 'text-white' : 'text-gray-800'
          }`}>{name}</h3>
          <div
            className={`w-full h-36 sm:h-40 md:h-48 rounded-lg bg-cover bg-center ${
              !workshopImage
                ? `flex items-center justify-center ${
                    theme === 'dark'
                      ? 'bg-gray-800 text-gray-400'
                      : 'bg-gray-200 text-gray-600'
                  }`
                : ""
            }`}
            style={
              workshopImage
                ? {
                    backgroundImage: `url(${workshopImage})`,
                  }
                : undefined
            }
          >
            {!workshopImage && (
              <span className="text-sm">No image available</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Workshop;
