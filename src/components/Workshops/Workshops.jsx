import React, { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import to from "await-to-js";
import { toast } from "sonner";
import { Loader } from "lucide-react";
import { getWorkshops, SET_CLIENT } from "../../actions/user";
import Workshop from "./components/Workshop/Workshop";
import { snakeToCamel } from "@/lib/utils";

const Workshops = ({ dispatch }) => {
  const navigate = useNavigate();
  const [isLoading, setLoading] = useState(false);
  const [workshops, setWorkshops] = useState([]);

  const isActive = useCallback(
    (id) => id === parseInt(localStorage.getItem("clientId"), 10),
    []
  );

  useEffect(() => {
    proceedWorkshops();
  }, []);

  useEffect(() => {
    // Auto-select workshop if there's only one
    if (workshops.length === 1) {
      setCurrentWorkshop(workshops[0].id);
    }
  }, [workshops]);

  async function proceedWorkshops() {
    setLoading(true);
    const [err, res] = await to(getWorkshops().payload);

    if (err) {
      setLoading(false);
      return toast.error("Something went wrong.");
    }

    const workshops = res.data.map((item) => item.clients);
    const formattedWorkshops = snakeToCamel(workshops);

    setLoading(false);
    setWorkshops(formattedWorkshops);
  }

  function setCurrentWorkshop(id) {
    console.log({ workshops });
    const relatedClient = workshops.find(
      ({ id: workshopId }) => workshopId === id
    );

    if (!relatedClient) return;

    localStorage.setItem("clientId", id);
    const { name, backgroundImage } = relatedClient;

    toast.success(`You have selected ${name} workshop!`);
    dispatch({ type: SET_CLIENT, payload: relatedClient });

    navigate("/home");
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
      <div className="backdrop-blur-xl bg-transparent p-4 sm:p-6 lg:p-8 rounded-xl sm:rounded-2xl shadow-xl border border-gray-800">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2">
            Select a Workshop
          </h1>
          <p className="text-gray-300">
            Choose a workshop to begin your session
          </p>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center min-h-[200px]">
            <Loader className="w-8 h-8 animate-spin text-blue-500" />
          </div>
        ) : (
          <>
            {workshops?.length ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {workshops.map((workshop) => (
                  <div key={workshop.id} className="relative">
                    {isActive(workshop.id) && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                        <span className="bg-blue-500 text-white text-sm px-4 py-px rounded-full">
                          Active
                        </span>
                      </div>
                    )}
                    <Workshop {...workshop} onUpdate={setCurrentWorkshop} />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 text-gray-300">
                <p className="text-lg">
                  You don't have any available workshops.
                  <br />
                  Please contact your manager.
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default Workshops;
