import React, { useEffect, useState } from 'react';
import { useTheme } from '@/components/ThemeProvider/ThemeProvider';
import { useNavigate } from 'react-router-dom';
import { Loader } from 'lucide-react';
import { toast } from 'sonner';
import to from 'await-to-js';
import { getWelcomePage } from '../../actions/user';

const WelcomePage = ({ user: { client = {} } }) => {
  const navigate = useNavigate();
  const [isLoading, setLoading] = useState(false);
  const [welcomePage, setWelcomePage] = useState({});
  const { theme } = useTheme();
  const { homeTabName, homeTabVisibility } = client;

  useEffect(() => {
    if (client?.hasOwnProperty('homeTabVisibility') && !homeTabVisibility) {
      navigate('/goals');
      return;
    }
    proceedWelcomePage();
  }, [client, homeTabVisibility, navigate]);

  async function proceedWelcomePage() {
    setLoading(true);
    const [err, res] = await to(getWelcomePage().payload);

    if (err) {
      setLoading(false);
      return toast.error('Something went wrong.');
    }

    setWelcomePage(res.data);
    setLoading(false);
  }

  if (isLoading) return <Loader />;

  return (
    <section>
      {/* Hero Image Section */}
      {welcomePage.image && (
        <div className="relative h-72 sm:h-96 w-full mb-8">
          <img src={welcomePage.image} alt="Welcome" className="absolute inset-0 w-full h-full object-cover" />
          <div
            className="absolute inset-0 bg-gradient-to-b from-transparent
                           via-black/30 to-black/70"
          />
        </div>
      )}

      {/* Content Section */}
      <div className="p-8">
        <div className={`prose max-w-none ${theme === 'dark' ? 'prose-invert' : ''}`}>
          {/* <p className="font-semibold mt-4 text-lg text-white">
            {homeTabName || "Welcome"}
          </p> */}
          {welcomePage?.text && (
            <div
              className={`text-base leading-snug ${theme === 'dark' ? 'text-gray-400' : 'text-gray-800'}`}
              dangerouslySetInnerHTML={{ __html: welcomePage.text }}
            />
          )}
        </div>

        {/* Cards Section with enhanced glass effect */}
        {welcomePage.cards && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-12">
            {welcomePage.cards.map((card, index) => (
              <div
                key={index}
                className={`backdrop-blur-xl p-8 rounded-xl shadow-lg
                             transform transition-all duration-300 hover:scale-102
                             ${theme === 'dark'
                               ? 'bg-white/5 border border-white/10 hover:bg-white/10'
                               : 'bg-white/60 border border-gray-300/30 hover:bg-white/80'}`}
              >
                {card.icon && <div className="text-blue-500 mb-4 text-3xl">{card.icon}</div>}
                <h3 className={`text-2xl font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>{card.title}</h3>
                <p className={`text-lg leading-relaxed ${theme === 'dark' ? 'text-gray-300' : 'text-gray-800'}`}>{card.content}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default WelcomePage;
