import { connect } from 'react-redux';

import Login from '../components/Login/Login';
import { createNotification } from '../actions/alert';
import { withReactRouterHooks } from '../components/HOC/withReactRouterHooks';

function mapStateToProps(state) {
  return {
    authenticatedUser: state.user.status === 'authenticated' ? state.user.user : null,
    user: state.user,
    state,
  };
}

const mapDispatchToProps = (dispatch) => ({
  dispatch,
  alert: (...notification) => dispatch(createNotification(...notification)),
});

export default withReactRouterHooks(connect(mapStateToProps, mapDispatchToProps)(Login));
