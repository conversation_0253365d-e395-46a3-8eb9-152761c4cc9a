import { connect } from "react-redux";

import Login from "../components/Login/Login";
import { withReactRouterHooks } from "../components/HOC/withReactRouterHooks";

function mapStateToProps(state) {
  return {
    authenticatedUser:
      state.user.status === "authenticated" ? state.user.user : null,
    user: state.user,
  };
}

const mapDispatchToProps = (dispatch) => ({
  dispatch,
});

export default withReactRouterHooks(
  connect(mapStateToProps, mapDispatchToProps)(Login)
);
