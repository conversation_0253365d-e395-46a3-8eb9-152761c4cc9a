import { connect } from 'react-redux';

import ViewChallenge from '../components/Challenges/ViewChallenge';
import { createNotification } from '../actions/alert';
import { withReactRouterHooks } from '../components/HOC/withReactRouterHooks';

function mapStateToProps() {
  return {};
}

const mapDispatchToProps = (dispatch) => ({
  dispatch,
  alert: (...props) => dispatch(createNotification(...props)),
});

export default withReactRouterHooks(connect(mapStateToProps, mapDispatchToProps)(ViewChallenge));
