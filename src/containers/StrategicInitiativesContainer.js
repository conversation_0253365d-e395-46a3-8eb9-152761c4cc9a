import { connect } from "react-redux";

import StrategicInitiatives from "../components/StrategicInitiatives/StrategicInitiatives";
import { withReactRouterHooks } from "../components/HOC/withReactRouterHooks";

function mapStateToProps(state) {
  return {
    user: state.user,
  };
}

const mapDispatchToProps = (dispatch) => ({
  dispatch,
});

export default withReactRouterHooks(
  connect(mapStateToProps, mapDispatchToProps)(StrategicInitiatives)
);
