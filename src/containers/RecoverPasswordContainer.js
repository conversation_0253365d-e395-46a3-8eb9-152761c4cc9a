import { connect } from "react-redux";

import RecoverPassword from "../components/RecoverPassword/RecoverPassword";
import { withReactRouterHooks } from "../components/HOC/withReactRouterHooks";

function mapStateToProps(state) {
  return {
    state,
  };
}

const mapDispatchToProps = (dispatch) => ({
  dispatch,
});

export default withReactRouterHooks(
  connect(mapStateToProps, mapDispatchToProps)(RecoverPassword)
);
