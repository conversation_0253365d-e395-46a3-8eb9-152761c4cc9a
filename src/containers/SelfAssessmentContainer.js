import { withReactRouterHooks } from "../components/HOC/withReactRouterHooks";
import { connect } from "react-redux";

import SelfAssessment from "../components/SelfAssessment/SelfAssessment";

function mapStateToProps(state) {
  return {
    user: state.user,
  };
}

const mapDispatchToProps = (dispatch) => ({
  dispatch,
});

export default withReactRouterHooks(
  connect(mapStateToProps, mapDispatchToProps)(SelfAssessment)
);
