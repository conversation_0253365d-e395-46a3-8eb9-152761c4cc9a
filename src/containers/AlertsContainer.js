import { connect } from 'react-redux';

import { removeNotification } from '../actions/alert';
import Alerts from '../components/Alerts/Alerts';
import { withReactRouterHooks } from '../components/HOC/withReactRouterHooks';

const mapDispatchToProps = (dispatch) => ({
  removeNotification: (notification) => dispatch(removeNotification(notification)),
});

const mapStateToProps = (state) => ({
  notifications: state.notifications,
});

export default withReactRouterHooks(connect(mapStateToProps, mapDispatchToProps)(Alerts));
