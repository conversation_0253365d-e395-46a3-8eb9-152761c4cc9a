import { connect } from "react-redux";

import ForgotPassword from "../components/ForgotPassword/ForgotPassword";
import { withReactRouterHooks } from "../components/HOC/withReactRouterHooks";

function mapStateToProps(state) {
  return {
    state,
  };
}

const mapDispatchToProps = (dispatch) => ({
  dispatch,
});

export default withReactRouterHooks(
  connect(mapStateToProps, mapDispatchToProps)(ForgotPassword)
);
