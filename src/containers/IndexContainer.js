import { connect } from 'react-redux';

import Index from '../components/Index/Index';
import { withReactRouterHooks } from '../components/HOC/withReactRouterHooks';

function mapStateToProps(state) {
  return {
    authenticatedUser: state.user.status === 'authenticated' ? state.user.user : null,
    user: state.user,
  };
}

const mapDispatchToProps = () => ({});

export default withReactRouterHooks(connect(mapStateToProps, mapDispatchToProps)(Index));
