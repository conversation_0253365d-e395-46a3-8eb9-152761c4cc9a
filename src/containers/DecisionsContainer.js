import { connect } from "react-redux";
import { withReactRouterHooks } from "../components/HOC/withReactRouterHooks";
import Decisions from "../components/Decisions/Decisions";

function mapStateToProps(state) {
  return {
    user: state.user,
  };
}

const mapDispatchToProps = (dispatch) => ({
  dispatch,
});

export default withReactRouterHooks(
  connect(mapStateToProps, mapDispatchToProps)(Decisions)
);