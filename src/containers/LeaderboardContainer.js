import { connect } from "react-redux";

import Leaderboard from "../components/Leaderboard/Leaderboard";
import { withReactRouterHooks } from "../components/HOC/withReactRouterHooks";

function mapStateToProps(state) {
  return {
    user: state.user,
  };
}

const mapDispatchToProps = (dispatch) => ({
  dispatch,
});

export default withReactRouterHooks(
  connect(mapStateToProps, mapDispatchToProps)(Leaderboard)
);
