import { connect } from 'react-redux';

import { resetToken, refreshToken, refreshTokenSuccess, refreshTokenFailure } from '../actions/user';
import App from '../components/App/App';
import { withReactRouterHooks } from '../components/HOC/withReactRouterHooks';

const mapDispatchToProps = (dispatch) => ({
  loadUserFromToken: () => {
    const token = sessionStorage.getItem('jwtToken');

    if (!token || token === '') {
      console.log('AppContainer dispaching token reset');
      dispatch(resetToken());
      return false;
    }

    console.log('sending token to refresh: ', token);
    dispatch(refreshToken(token)).then((response) => {
      if (!response.error) {
        // refresh the token
        sessionStorage.setItem('jwtToken', response.payload.data.token);
        dispatch(refreshTokenSuccess(response.payload));
      } else {
        sessionStorage.removeItem('jwtToken'); // remove token from storage
        dispatch(refreshTokenFailure(response.payload));
      }
    });
    return true;
  },
  resetUserToken: () => {
    sessionStorage.removeItem('jwtToken'); // remove token from storage
    // TODO: dispatch reset token event
  },
});

export default withReactRouterHooks(connect(null, mapDispatchToProps)(App));
