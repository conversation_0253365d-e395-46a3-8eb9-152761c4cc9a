import { connect } from 'react-redux';

import AddDecision from '../components/Decisions/AddDecision';
import { createNotification } from '../actions/alert';
import { withReactRouterHooks } from '../components/HOC/withReactRouterHooks';

function mapStateToProps() {
  return {};
}

const mapDispatchToProps = (dispatch) => ({
  dispatch,
  alert: (...props) => dispatch(createNotification(...props)),
});

export default withReactRouterHooks(connect(mapStateToProps, mapDispatchToProps)(AddDecision));