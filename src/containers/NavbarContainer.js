import { connect } from 'react-redux';

import { signOut } from '../actions/user';
import Navbar from '../components/Navbar/Navbar';
import { withReactRouterHooks } from '../components/HOC/withReactRouterHooks';

function mapStateToProps(state) {
  return {
    authenticatedUser: state.user.status === 'authenticated' ? state.user.user : null,
    user: state.user,
  };
}

const mapDispatchToProps = (dispatch) => ({
  dispatch,
  logout: () => {
    sessionStorage.removeItem('jwtToken');
    localStorage.removeItem('clientId');
    dispatch(signOut());
  },
});

export default withReactRouterHooks(connect(mapStateToProps, mapDispatchToProps)(Navbar));
