import joi from 'joi';

export class Metric {
  static get schema() {
    return joi.object().keys({
      id: joi.number().integer(),
      name: joi.string().min(1).max(127),
      fixed: joi.boolean(),
      stringValue: joi.string().max(127).allow(null),
      maximum: joi.number().min(0).allow(null),
      companyValue: joi.string().max(127),
      industryValue: joi.string().max(127),
    });
  }
}
