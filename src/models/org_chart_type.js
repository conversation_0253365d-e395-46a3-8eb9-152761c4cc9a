import joi from 'joi';
import { OrgChartUser } from './org_chart_user.js';

export class OrgChartType {
  static get schema() {
    return joi.object().keys({
      id: joi.number().integer(),
      name: joi.string().min(0).max(255),
      type: joi.string().min(0).max(255),
      is_visible: joi.boolean(),
      org_chart_id: joi.number().integer(),
      created_at: joi.date(),
      users: joi.array().items(OrgChartUser.schema),
    });
  }
}
