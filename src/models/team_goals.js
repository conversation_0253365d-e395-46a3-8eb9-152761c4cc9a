import joi from 'joi';

export class TeamGoals {
  static get schema() {
    return joi.object().keys({
      id: joi.number().integer(),
      team_id: joi.number().integer(),
      client_id: joi.number().integer(),
      goal_1: joi.string().max(1096).allow('').allow(null),
      goal_2: joi.string().max(1096).allow('').allow(null),
      goal_3: joi.string().max(1096).allow('').allow(null),
    });
  }
}
