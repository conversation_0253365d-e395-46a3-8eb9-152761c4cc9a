import joi from 'joi';

export class OrgChartUser {
  static get schema() {
    return joi.object().keys({
      id: [joi.number().integer(), joi.string()],
      name: joi.string().min(0).max(512),
      title: joi.string().min(0).max(255),
      status: joi.number().integer(),
      photo: joi.string().allow(null).allow('').min(0).max(1024),
      meet_1_text: joi.string().allow(null).allow('').min(0).max(1024),
      meet_1_points: joi.number().allow(null).allow('').integer(),
      meet_2_text: joi.string().allow(null).allow('').min(0).max(1024),
      meet_2_points: joi.number().allow(null).allow('').integer(),
      meet_3_text: joi.string().allow(null).allow('').min(0).max(1024),
      meet_3_points: joi.number().allow(null).allow('').integer(),
      parent_id: joi.number().allow(null).integer(),
      locked_by_id: [joi.number().integer(), joi.string().allow(null)],
      org_chart_type_id: joi.number().integer(),
      children: joi.array(),
      created_at: joi.date(),
    });
  }
}
