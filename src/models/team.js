import joi from 'joi';
import { Client } from './client.js';

export class Team {
  static get schema() {
    return joi.object().keys({
      id: joi.number().integer().min(1),
      email: joi.string().min(1).max(127),
      name: joi.string().min(1).max(255),
      password: joi.string().allow('').min(1).max(127),
      goal1: joi.string().allow('').min(0).max(64000).allow(null),
      goal2: joi.string().allow('').min(0).max(64000).allow(null),
      goal3: joi.string().allow('').min(0).max(64000).allow(null),
      client: joi.array().items(Client.schema),
      selectedClients: joi.array().items(joi.number()),
    });
  }

  static get validationSchema() {
    return {
      get: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1),
        }),
        query: joi.object().max(0),
      },
      create: {
        body: Team.schema,
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      update: {
        body: Team.schema,
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      list: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().keys({
          ascending: joi.boolean().required(),
          showDisabled: joi.boolean().required(),
          limit: joi.number().integer().min(1).max(200).required(),
          offset: joi.number().integer().min(0).required(),
          query: joi.string().allow('').min(0).max(127),
          sort: joi.string().min(0).max(127).required(),
          clientId: joi.number().integer(),
        }),
      },
      listAll: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      toggleDisabled: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1).required(),
        }),
        query: joi.object().max(0),
      },
      exportExcel: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1).required(),
        }),
        query: joi.object().max(0),
      },
      exportAll: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
    };
  }
}
