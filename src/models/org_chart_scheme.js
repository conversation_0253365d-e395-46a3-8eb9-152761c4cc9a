import joi from 'joi';
import { OrgChart } from './org_chart.js';

export class OrgChartScheme {
  static get schema() {
    return joi.object().keys({
      id: joi.number().integer(),
      name: joi.string().min(0).max(255).allow(''),
      disabled: joi.boolean(),
      created_at: joi.date(),
      orgCharts: joi.array().items(OrgChart.schema),
    });
  }

  static get validationSchema() {
    return {
      get: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1),
        }),
        query: joi.object().max(0),
      },
      create: {
        body: OrgChartScheme.schema,
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      update: {
        body: OrgChartScheme.schema,
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      list: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().keys({
          ascending: joi.boolean().required(),
          showDisabled: joi.boolean().required(),
          limit: joi.number().integer().min(1).max(200).required(),
          offset: joi.number().integer().min(0).required(),
          query: joi.string().allow('').min(0).max(127),
          sort: joi.string().min(0).max(127).required(),
        }),
      },
      listAll: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      toggleDisabled: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1).required(),
        }),
        query: joi.object().max(0),
      },
    };
  }
}
