import { eq, and, sql } from 'drizzle-orm';
import { db } from '../db/index.js';
import {
  teamClients,
  lumenTeam,
  teamGoals,
  globalTeamMetricsScheme,
  globalTeamMetric,
} from '../db/schema.js';

const options = {
  status: 422,
  statusText: 'Unprocessable Entity',
  allowUnknownBody: false,
  allowUnknownHeaders: true,
  allowUnknownQuery: false,
  allowUnknownParams: false,
  allowUnknownCookies: true,
};

export const TeamModel = {
  async findByEmail(email) {
    const [team] = await db
      .select()
      .from(lumenTeam)
      .where(sql`LOWER(${lumenTeam.team_email}) = LOWER(${email})`)
      .limit(1);
    return team;
  },

  async create({ name, email, password }) {
    const [team] = await db
      .insert(lumenTeam)
      .values({
        team_name: name || email,
        team_email: email,
        team_password: sql`MD5(${password})`,
        team_created_at: new Date(),
      })
      .returning();
    return team;
  },
};

export const TeamClientsModel = {
  async create({ teamId, clientId }) {
    return db.insert(teamClients).values({
      team_id: teamId,
      client_id: clientId,
    });
  },
};

export const TeamGoalsModel = {
  async create({ teamId, clientId }) {
    return db.insert(teamGoals).values({
      team_id: teamId,
      client_id: clientId,
      created_at: new Date(),
    });
  },
};

export const TeamMetricsModel = {
  async createTeamMetrics({ teamId, clientId }) {
    const schemes = await db
      .select()
      .from(globalTeamMetricsScheme)
      .where(eq(globalTeamMetricsScheme.client_id, clientId));

    return Promise.all(
      schemes.map((scheme) =>
        db.insert(globalTeamMetric).values({
          value: 0,
          global_team_metric_scheme_id: scheme.id,
          team_id: teamId,
          client_id: clientId,
          created_at: new Date(),
        })
      )
    );
  },
};

export * from './admin.js';
export * from './team.js';
export * from './client.js';
export * from './user.js';
export * from './initiative.js';
export * from './challenge.js';
export * from './leaderboard_scheme.js';
export * from './org_chart_scheme.js';
export * from './welcome_page.js';
export * from './self_assessment.js';
export * from './decision_groups.js';

export { options };
