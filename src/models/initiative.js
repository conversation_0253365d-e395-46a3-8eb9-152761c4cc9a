import joi from 'joi';

/**
 * Class representing an Initiative.
 */
export class Initiative {
  /**
   * Get the schema for an Initiative.
   * @returns {Object} The Joi schema object.
   */
  static get schema() {
    return joi.object().keys({
      id: joi.number().integer().min(1),
      name: joi.string().min(1).max(127),
      applyToAllTeams: joi.boolean().allow(null),
      teamId: joi.number().integer().allow(null),
      initiativeNumber: joi.number().integer().min(1).max(5),
      initiatives: joi.array().items(Initiative.item),
    });
  }

  /**
   * Get the schema for an item within an Initiative.
   * @returns {Object} The Joi schema object.
   */
  static get item() {
    return joi.object().keys({
      id: joi.number().integer().min(1),
      name: joi.string().allow('').min(0).max(127),
      description: joi.string().allow('').min(0).max(64000),
      metric1: joi.number().integer(),
      metric2: joi.number().integer(),
      metric3: joi.number().integer(),
    });
  }

  /**
   * Get the validation schema for various operations on an Initiative.
   * @returns {Object} The validation schema object.
   */
  static get validationSchema() {
    return {
      get: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1),
        }),
        query: joi.object().max(0),
      },
      create: {
        body: Initiative.schema,
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      update: {
        body: Initiative.schema,
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      list: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().keys({
          ascending: joi.boolean().required(),
          limit: joi.number().integer().min(1).max(200).required(),
          showDisabled: joi.boolean().required(),
          offset: joi.number().integer().min(0).required(),
          query: joi.string().allow('').min(0).max(127),
          sort: joi.string().min(0).max(127).required(),
        }),
      },
      listAll: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      toggleDisabled: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1).required(),
        }),
        query: joi.object().max(0),
      },
    };
  }
}
