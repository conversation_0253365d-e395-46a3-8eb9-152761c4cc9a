import joi from 'joi';
import { LeaderboardUser } from './leaderboard_user.js';

export class LeaderboardRegion {
  static get schema() {
    return joi.object().keys({
      id: joi.number().integer(),
      name: joi.string().min(0).max(255).allow(''),
      leaderboardSchemeId: joi.number().integer(),
      users: joi.array().items(LeaderboardUser.schema),
      createdAt: joi.date(),
    });
  }
}
