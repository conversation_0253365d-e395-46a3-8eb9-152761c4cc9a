import joi from 'joi';

export class SelfAssessment {
  static get schema() {
    return joi.object().keys({
      scheme_id: joi.number().integer().min(1),
      quadrants_config_id: joi.number().integer().min(1),
      pdf_pages_config_id: joi.number().integer().min(1),
      pdf_paragraphs_config_id: joi.number().integer().min(1),
      disabled: joi.boolean(),
      name: joi.string().min(1).max(255),
      cover_image: joi.string().max(255).allow(null).allow(''),
      page_2_quad_1_pdf: joi.string().max(255).allow(null).allow(''),
      page_2_quad_2_pdf: joi.string().max(255).allow(null).allow(''),
      page_2_quad_3_pdf: joi.string().max(255).allow(null).allow(''),
      page_2_quad_4_pdf: joi.string().max(255).allow(null).allow(''),
      page_3_quad_1_pdf: joi.string().max(255).allow(null).allow(''),
      page_3_quad_2_pdf: joi.string().max(255).allow(null).allow(''),
      page_3_quad_3_pdf: joi.string().max(255).allow(null).allow(''),
      page_3_quad_4_pdf: joi.string().max(255).allow(null).allow(''),
      normal_paragraphs: joi.string().allow(null).allow(''),
      stress_paragraphs: joi.string().allow(null).allow(''),
      quadrant_1_name: joi.string().max(255).allow(null).allow(''),
      quadrant_2_name: joi.string().max(255).allow(null).allow(''),
      quadrant_3_name: joi.string().max(255).allow(null).allow(''),
      quadrant_4_name: joi.string().max(255).allow(null).allow(''),
      normal_quadrant_1_questions: joi.string().allow(null).allow(''),
      normal_quadrant_2_questions: joi.string().allow(null).allow(''),
      normal_quadrant_3_questions: joi.string().allow(null).allow(''),
      normal_quadrant_4_questions: joi.string().allow(null).allow(''),
      stress_quadrant_1_questions: joi.string().allow(null).allow(''),
      stress_quadrant_2_questions: joi.string().allow(null).allow(''),
      stress_quadrant_3_questions: joi.string().allow(null).allow(''),
      stress_quadrant_4_questions: joi.string().allow(null).allow(''),
    });
  }

  static get validationSchema() {
    return {
      get: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1),
        }),
        query: joi.object().max(0),
      },
      create: {
        body: SelfAssessment.schema,
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      update: {
        body: SelfAssessment.schema,
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      list: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().keys({
          ascending: joi.boolean().required(),
          showDisabled: joi.boolean().required(),
          limit: joi.number().integer().min(1).max(200).required(),
          offset: joi.number().integer().min(0).required(),
          query: joi.string().allow('').min(0).max(127),
          sort: joi.string().min(0).max(127).required(),
        }),
      },
      listAll: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      toggleDisabled: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1).required(),
        }),
        query: joi.object().max(0),
      },
    };
  }
}
