# Models

This directory contains data model classes for the Lumen simulation platform.

## Overview

Models define the data structures and business logic for core entities in the simulation platform. They provide object-oriented interfaces for working with database records and encapsulate domain-specific behavior.

## Model Classes

### Core Business Entities
- **client.js** - Client organization data model
- **team.js** - Team structure and member management
- **user.js** - User profile and authentication data

### Simulation & Decision Making
- **challenge.js** - Business challenge scenarios
- **initiative.js** - Strategic initiatives and projects
- **decision_groups.js** - Decision categorization and grouping

### Assessment & Learning
- **self_assessment.js** - Self-assessment questionnaire model
- **welcome_page.js** - Welcome page content and configuration

### Organization Structure
- **org_chart.js** - Organizational chart hierarchy
- **org_chart_scheme.js** - Org chart template definitions
- **org_chart_type.js** - Chart type classifications
- **org_chart_user.js** - User positions within org charts

### Metrics & Analytics
- **metric.js** - Base metrics functionality
- **team_metric.js** - Team-specific performance metrics
- **global_team_metric.js** - Cross-team metrics
- **global_team_metric_scheme.js** - Global metric definitions
- **revenue_number.js** - Financial performance data

### Leaderboards & Ranking
- **leaderboard_region.js** - Regional leaderboard data
- **leaderboard_scheme.js** - Leaderboard configuration
- **leaderboard_user.js** - User ranking and scores

### Goals & Objectives
- **team_goals.js** - Team goal setting and tracking

### Administrative
- **admin.js** - Administrative functionality and management

## Base Model

- **model.js** - Base Model class providing common functionality
  - Shared methods and properties for all models
  - Common CRUD operations interface
  - Data validation patterns

## Architecture

Models follow object-oriented patterns:
- Extend from base Model class for consistency
- Encapsulate business logic related to specific entities
- Provide clean interfaces for controllers and services
- Handle data transformation and validation
- Support relationships between entities

## Usage

Models are imported and used by controllers and services:
```javascript
import { Client } from '../models/client.js';
import { Team } from '../models/team.js';
```

Models provide consistent APIs for data manipulation and business rule enforcement across the application.