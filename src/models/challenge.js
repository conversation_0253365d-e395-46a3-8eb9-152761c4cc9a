import joi from 'joi';

export class Challenge {
  static get schema() {
    return joi.object().keys({
      id: joi.number().integer().min(1),
      name: joi.string().min(1).max(127),
      applyToAllTeams: joi.boolean().allow(null),
      teamId: joi.number().integer().allow(null),
      challenges: joi.array().items(Challenge.item),
    });
  }

  static get item() {
    return joi.object().keys({
      id: joi.number().integer().min(1),
      imageUrl: joi.string().allow('').allow(null).min(0),
      description: joi.string().allow('').min(0).max(64000),
      optionA: joi.string().allow('').min(0).max(64000),
      consequenceA: joi.string().allow('').min(0).max(64000),
      optionMetric1A: joi.number().integer(),
      optionMetric2A: joi.number().integer(),
      optionMetric3A: joi.number().integer(),
      optionB: joi.string().allow('').min(0).max(64000),
      consequenceB: joi.string().allow('').min(0).max(64000),
      optionMetric1B: joi.number().integer(),
      optionMetric2B: joi.number().integer(),
      optionMetric3B: joi.number().integer(),
      optionC: joi.string().allow('').min(0).max(64000),
      consequenceC: joi.string().allow('').min(0).max(64000),
      optionMetric1C: joi.number().integer(),
      optionMetric2C: joi.number().integer(),
      optionMetric3C: joi.number().integer(),
    });
  }

  static get validationSchema() {
    return {
      get: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1),
        }),
        query: joi.object().max(0),
      },
      create: {
        body: Challenge.schema,
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      update: {
        body: Challenge.schema,
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      list: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().keys({
          ascending: joi.boolean().required(),
          limit: joi.number().integer().min(1).max(200).required(),
          showDisabled: joi.boolean().required(),
          offset: joi.number().integer().min(0).required(),
          query: joi.string().allow('').min(0).max(127),
          sort: joi.string().min(0).max(127).required(),
        }),
      },
      listAll: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      toggleDisabled: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1).required(),
        }),
        query: joi.object().max(0),
      },
    };
  }
}
