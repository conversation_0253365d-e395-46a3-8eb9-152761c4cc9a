import joi from 'joi';

export class Admin {
  static get validationSchema() {
    return {
      login: {
        body: joi.object().keys({
          email: joi.string().min(1).max(127),
          password: joi.string().min(1).max(127),
        }),
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      tokenRefresh: {
        body: joi.object().keys({
          token: joi.string().min(1).max(255),
        }),
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      getTeam: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1),
        }),
        query: joi.object().max(0),
      },
    };
  }
}
