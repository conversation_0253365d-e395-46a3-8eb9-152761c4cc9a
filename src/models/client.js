import joi from 'joi';
import { GlobalTeamMetricScheme } from './global_team_metric_scheme.js';

export class Client {
  static get schema() {
    return joi.object().keys({
      id: joi.number().integer().min(1),
      name: joi.string().min(1).max(255),
      logoImage: joi.string().max(255).allow(null).allow(''),
      backgroundImage: joi.string().max(255).allow(null).allow(''),
      homeTabName: joi.string().min(1).max(255),
      goalsTabName: joi.string().min(1).max(255),
      challengesTabName: joi.string().min(1).max(255),
      strategicInitiativesTabName: joi.string().min(1).max(255).optional(),
      strategicTabName: joi.string().min(1).max(255).optional(), // Allow both field names
      orgChartTabName: joi.string().min(1).max(255),
      selfAssessmentTabName: joi.string().min(1).max(255),
      leaderboardTabName: joi.string().min(1).max(255),
      homeTabVisibility: joi.boolean(),
      goalsTabVisibility: joi.boolean(),
      challengesTabVisibility: joi.boolean(),
      initiativesTabVisibility: joi.boolean(),
      orgChartTabVisibility: joi.boolean(),
      selfAssessmentTabVisibility: joi.boolean(),
      leaderboardTabVisibility: joi.boolean(),
      darkHighlightColor: joi.string().min(1).max(255),
      lightHighlightColor: joi.string().min(1).max(255),
      homeSchemeId: joi.number().integer().allow(null),
      initiativeSchemeId: joi.number().integer().allow(null),
      challengeSchemeId: joi.number().integer().allow(null),
      leaderboardSchemeId: joi.number().integer().allow(null),
      orgChartSchemeId: joi.number().integer().allow(null),
      selfAssessmentSchemeId: joi.number().integer().allow(null),
      globalTeamMetricsSchemes: joi
        .array()
        .items(GlobalTeamMetricScheme.schema),
      isSignUpEnabled: joi.boolean(),
      signUpEmailDomain: joi.string().max(255).allow(null).allow(''),
      workshopImage: joi.string().max(1096).allow(null).allow(''),
      decisionSchemeId: joi.number().integer().allow(null),
      decisionsTabName: joi.string().min(1).max(255),
      decisionsTabVisibility: joi.boolean(),
      schemeOrder: joi.array().items(joi.string()).default([]),
      fteMax: joi.number().allow(null),
      investmentMax: joi.number().allow(null),
      aiSummaryTitle: joi.string().max(255).allow(null).allow(''),
    });
  }

  static get validationSchema() {
    return {
      get: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1),
        }),
        query: joi.object().max(0),
      },
      create: {
        body: Client.schema,
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      update: {
        body: Client.schema,
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      list: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().keys({
          ascending: joi.boolean().required(),
          showDisabled: joi.boolean().required(),
          limit: joi.number().integer().min(1).max(200).required(),
          offset: joi.number().integer().min(0).required(),
          query: joi.string().allow('').min(0).max(127),
          sort: joi.string().min(0).max(127).required(),
        }),
      },
      listAll: {
        body: joi.object().max(0),
        params: joi.object().max(0),
        query: joi.object().max(0),
      },
      toggleDisabled: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1).required(),
        }),
        query: joi.object().max(0),
      },
      exportExcel: {
        body: joi.object().max(0),
        params: joi.object().keys({
          id: joi.number().integer().min(1).required(),
        }),
        query: joi.object().max(0),
      },
    };
  }
}
