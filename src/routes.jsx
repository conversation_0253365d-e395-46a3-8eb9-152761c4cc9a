import { BrowserRouter, Routes, Route } from "react-router-dom";

import AppContainer from "./containers/AppContainer";
import IndexContainer from "./containers/IndexContainer";
import NotFound from "./components/NotFound/NotFound";
import WorkshopsContainer from "./containers/WorkshopsContainer";
import SignUpContainer from "./containers/SignUpContainer";
import ForgotPasswordContainer from "./containers/ForgotPasswordContainer";
import RecoverPasswordContainer from "./containers/RecoverPasswordContainer";
import ScrollWrapperContainer from "./containers/ScrollWrapperContainer";
import { PageBackgroundProvider } from "./contexts/PageBackgroundContext";

export default (
  <BrowserRouter>
    <PageBackgroundProvider>
      <AppContainer>
        <Routes>
          <Route path="/workshops" element={<WorkshopsContainer />} exact />
          <Route path="/home" element={<ScrollWrapperContainer />} exact />
          <Route path="/" element={<IndexContainer />} exact />
          <Route path="/sign-up" element={<SignUpContainer />} exact />
          <Route
            path="/forgot-password"
            element={<ForgotPasswordContainer />}
            exact
          />
          <Route
            path="/recover-password/:teamId/:code"
            element={<RecoverPasswordContainer />}
            exact
          />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </AppContainer>
    </PageBackgroundProvider>
  </BrowserRouter>
);
