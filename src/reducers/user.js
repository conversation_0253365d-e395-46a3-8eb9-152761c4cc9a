import {
  SIGNIN_USER,
  SIGNIN_USER_SUCCESS,
  SIGNIN_USER_FAILURE,
  RESET_TOKEN,
  SIGNOUT_USER,
  REFRESH_TOKEN,
  TOKEN_REFRESH_FAILURE,
  TOKEN_REFRESH_SUCCESS,
} from '../actions/user';

const INITIAL_STATE = { user: null, status: null, error: null, loading: false };

export default function (state = INITIAL_STATE, action) {
  let error;
  // console.log('user action: ', action.type);
  switch (action.type) {
    case SIGNIN_USER:
      return { ...state, user: null, status: 'signin', error: null, loading: true };
    /* falls through */
    case SIGNIN_USER_SUCCESS:
      return { ...state, user: action.payload.user, status: 'authenticated', error: null, loading: false };
    /* falls through */
    case SIGNIN_USER_FAILURE:
      error = action.payload.data || { message: action.payload.message };
      return { ...state, user: null, status: 'signin', error, loading: false };
    /* falls through */
    case SIGNOUT_USER:
      return { ...state, user: null, status: 'signout', error: null, loading: false };
    /* falls through */
    case REFRESH_TOKEN:
      return { ...state, user: null, status: 'storage', error: null, loading: true };
    /* falls through */
    case TOKEN_REFRESH_SUCCESS:
      return { ...state, user: action.payload.data.user, status: 'authenticated', error: null, loading: false };
    /* falls through */
    case TOKEN_REFRESH_FAILURE:
      error = action.payload.data || { message: action.payload.message };
    /* falls through */
    case RESET_TOKEN:
      return { ...state, user: null, status: 'storage', error: null, loading: false };
    /* falls through */
    default:
      return state;
  }
}
