import { reject } from 'lodash';

import { CREATE_NOTIFICATION, REMOVE_NOTIFICATION } from '../actions/alert';

const INITIAL_STATE = [];

export default function (state = INITIAL_STATE, action) {
  // console.log('alert action: ', action.type);
  switch (action.type) {
    case CREATE_NOTIFICATION:
      console.log('pushing to state: ', action);
      state.push(action.payload);
      return [...state];

    case REMOVE_NOTIFICATION:
      state = reject(state, (n) => n.id === action.payload.id);
      return [...state];

    default:
      return state;
  }
}
