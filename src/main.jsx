import { Provider } from "react-redux";

import { createRoot } from "react-dom/client";
import configureStore from "./store/configureStore";
import routes from "./routes";
// import "bootstrap/dist/css/bootstrap.css";
// import "bootstrap/dist/css/bootstrap-theme.css";
import "./index.css";
import { ThemeProvider } from "./components/ThemeProvider/ThemeProvider";

const store = configureStore();

const container = document.getElementById("root");
const root = createRoot(container);

root.render(
  <Provider store={store}>
    <ThemeProvider>
      {routes}
    </ThemeProvider>
  </Provider>
);
