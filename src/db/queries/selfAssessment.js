import { db } from '../index.js';
import {
  clients,
  selfAssessmentScheme,
  selfAssessmentQuadrantsConfig,
  selfAssessmentPdfPagesConfig,
  selfAssessmentPdfParagraphsConfig,
  selfAssessmentAnswers,
} from '../schema.js';
import { eq, sql, asc, desc } from 'drizzle-orm';

export const findClientById = async (id) => {
  const [client] = await db
    .select({
      id: clients.id,
      selfAssessmentSchemeId: clients.self_assessment_scheme_id,
    })
    .from(clients)
    .where(eq(clients.id, id))
    .limit(1);

  return client;
};

export const findSelfAssessmentSchemeById = async (id) => {
  const [scheme] = await db
    .select({
      id: selfAssessmentScheme.id,
      name: selfAssessmentScheme.name,
      description: selfAssessmentScheme.description,
      createdAt: selfAssessmentScheme.created_at,
    })
    .from(selfAssessmentScheme)
    .where(eq(selfAssessmentScheme.id, id))
    .limit(1);

  return scheme;
};

export const findSelfAssessmentQuadrantsConfigBySchemeId = async (schemeId) => {
  const [config] = await db
    .select({
      normalQuadrant1Questions:
        selfAssessmentQuadrantsConfig.normal_quadrant_1_questions,
      normalQuadrant2Questions:
        selfAssessmentQuadrantsConfig.normal_quadrant_2_questions,
      normalQuadrant3Questions:
        selfAssessmentQuadrantsConfig.normal_quadrant_3_questions,
      normalQuadrant4Questions:
        selfAssessmentQuadrantsConfig.normal_quadrant_4_questions,
      stressQuadrant1Questions:
        selfAssessmentQuadrantsConfig.stress_quadrant_1_questions,
      stressQuadrant2Questions:
        selfAssessmentQuadrantsConfig.stress_quadrant_2_questions,
      stressQuadrant3Questions:
        selfAssessmentQuadrantsConfig.stress_quadrant_3_questions,
      stressQuadrant4Questions:
        selfAssessmentQuadrantsConfig.stress_quadrant_4_questions,
    })
    .from(selfAssessmentQuadrantsConfig)
    .where(eq(selfAssessmentQuadrantsConfig.scheme_id, schemeId))
    .orderBy(desc(selfAssessmentQuadrantsConfig.id))
    .limit(1);

  return config;
};

export const findSelfAssessmentPdfPagesConfigBySchemeId = async (schemeId) => {
  const [config] = await db
    .select({
      id: selfAssessmentPdfPagesConfig.id,
      schemeId: selfAssessmentPdfPagesConfig.scheme_id,
      pageTitle: selfAssessmentPdfPagesConfig.page_title,
      pageContent: selfAssessmentPdfPagesConfig.page_content,
    })
    .from(selfAssessmentPdfPagesConfig)
    .where(eq(selfAssessmentPdfPagesConfig.scheme_id, schemeId))
    .orderBy(desc(selfAssessmentPdfPagesConfig.id))
    .limit(1);

  return config;
};

export const findSelfAssessmentPdfParagraphsConfigBySchemeId = async (
  schemeId
) => {
  const [config] = await db
    .select({
      id: selfAssessmentPdfParagraphsConfig.id,
      schemeId: selfAssessmentPdfParagraphsConfig.scheme_id,
      paragraphTitle: selfAssessmentPdfParagraphsConfig.paragraph_title,
      paragraphContent: selfAssessmentPdfParagraphsConfig.paragraph_content,
    })
    .from(selfAssessmentPdfParagraphsConfig)
    .where(eq(selfAssessmentPdfParagraphsConfig.scheme_id, schemeId))
    .orderBy(desc(selfAssessmentPdfParagraphsConfig.id))
    .limit(1);

  return config;
};

export const createSelfAssessmentScheme = async ({ name, description }) => {
  const [scheme] = await db
    .insert(selfAssessmentScheme)
    .values({
      name,
      description,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return scheme;
};

export const createSelfAssessmentQuadrantsConfig = async ({
  schemeId,
  ...config
}) => {
  const [quadrantsConfig] = await db
    .insert(selfAssessmentQuadrantsConfig)
    .values({
      scheme_id: schemeId,
      ...config,
    })
    .returning();

  return quadrantsConfig;
};

export const createSelfAssessmentPdfPagesConfig = async ({
  schemeId,
  ...config
}) => {
  const [pdfPagesConfig] = await db
    .insert(selfAssessmentPdfPagesConfig)
    .values({
      scheme_id: schemeId,
      ...config,
    })
    .returning();

  return pdfPagesConfig;
};

export const createSelfAssessmentPdfParagraphsConfig = async ({
  schemeId,
  ...config
}) => {
  const [pdfParagraphsConfig] = await db
    .insert(selfAssessmentPdfParagraphsConfig)
    .values({
      scheme_id: schemeId,
      ...config,
    })
    .returning();

  return pdfParagraphsConfig;
};

export const createSelfAssessmentAnswers = async (answers) => {
  const [createdAnswers] = await db
    .insert(selfAssessmentAnswers)
    .values(answers)
    .returning();

  return createdAnswers;
};

export const updateSelfAssessmentScheme = async (id, { name, description }) => {
  const [scheme] = await db
    .update(selfAssessmentScheme)
    .set({
      name,
      description,
    })
    .where(eq(selfAssessmentScheme.id, id))
    .returning();

  return scheme;
};

export const listSelfAssessmentSchemes = async ({
  sort,
  ascending,
  showDisabled,
  query,
  offset,
  limit,
}) => {
  const schemes = await db
    .select({
      id: selfAssessmentScheme.id,
      name: selfAssessmentScheme.name,
      createdAt: selfAssessmentScheme.created_at,
      disabled: selfAssessmentScheme.disabled,
    })
    .from(selfAssessmentScheme)
    .where(
      sql`LOWER(name) LIKE ${`%${query.toLowerCase()}%`} AND disabled = ${
        showDisabled === 'true'
      }`
    )
    .orderBy(sort, ascending === 'true' ? 'asc' : 'desc')
    .offset(offset)
    .limit(limit);

  return schemes;
};

export const listAllSelfAssessmentSchemes = async () => {
  const schemes = await db
    .select({
      id: selfAssessmentScheme.id,
      name: selfAssessmentScheme.name,
      createdAt: selfAssessmentScheme.created_at,
      disabled: selfAssessmentScheme.disabled,
    })
    .from(selfAssessmentScheme)
    .orderBy(asc(selfAssessmentScheme.name));

  return schemes;
};

export const toggleSelfAssessmentSchemeDisabled = async (id) => {
  const [scheme] = await db
    .update(selfAssessmentScheme)
    .set({
      disabled: sql`NOT disabled`,
    })
    .where(eq(selfAssessmentScheme.id, id))
    .returning();

  return scheme;
};
