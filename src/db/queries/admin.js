import { eq, and } from 'drizzle-orm';
import { lumenAdmin } from '../schema.js';

export const findAdminByEmailAndPassword = async (db, email, password) => {
  const admins = await db
    .select({
      admin_id: lumenAdmin.admin_id,
      admin_email: lumenAdmin.admin_email,
    })
    .from(lumenAdmin)
    .where(
      and(
        eq(lumenAdmin.admin_email, email),
        eq(lumenAdmin.admin_password, password)
      )
    )
    .limit(1);

  return admins[0] || null;
};

export const findAdminById = async (db, id) => {
  const admins = await db
    .select({
      admin_id: lumenAdmin.admin_id,
      admin_email: lumenAdmin.admin_email,
    })
    .from(lumenAdmin)
    .where(eq(lumenAdmin.admin_id, id))
    .limit(1);

  return admins[0] || null;
};
