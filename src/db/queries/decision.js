import { db } from '../index.js';
import { lumenDecisionScheme, lumenDecisionPage } from '../schema.js';
import { eq, sql, and, asc, desc } from 'drizzle-orm';

export const findDecisionSchemeById = async (id) => {
  const [scheme] = await db
    .select({
      id: lumenDecisionScheme.scheme_id,
      name: lumenDecisionScheme.scheme_name,
      pnl: lumenDecisionScheme.scheme_pnl,
      created_at: lumenDecisionScheme.scheme_created_at,
      updated_at: lumenDecisionScheme.scheme_updated_at,
      disabled: lumenDecisionScheme.scheme_disabled,
      global_pnl_prompt_template: lumenDecisionScheme.global_pnl_prompt_template,
      use_custom_global_template: lumenDecisionScheme.use_custom_global_template,
    })
    .from(lumenDecisionScheme)
    .where(eq(lumenDecisionScheme.scheme_id, id))
    .limit(1);

  return scheme;
};

export const findDecisionPagesBySchemeId = async (schemeId) => {
  const pages = await db
    .select({
      id: lumenDecisionPage.page_id,
      page_number: lumenDecisionPage.page_number,
      page_name: lumenDecisionPage.page_name,
      page_image: lumenDecisionPage.page_image,
      sliders: lumenDecisionPage.sliders,
      incentives: lumenDecisionPage.incentives,
      page_pnl: lumenDecisionPage.page_pnl,
      page_pnl_prompt_template: lumenDecisionPage.page_pnl_prompt_template,
      use_custom_page_template: lumenDecisionPage.use_custom_page_template,
    })
    .from(lumenDecisionPage)
    .where(eq(lumenDecisionPage.scheme_id, schemeId))
    .orderBy(asc(lumenDecisionPage.page_number));

  return pages;
};

export const createDecisionScheme = async (name, pnl = null, globalTemplate = null, useCustomGlobalTemplate = false) => {
  const [scheme] = await db
    .insert(lumenDecisionScheme)
    .values({
      scheme_name: name,
      scheme_pnl: pnl,
      global_pnl_prompt_template: globalTemplate,
      use_custom_global_template: useCustomGlobalTemplate,
      scheme_created_at: sql`current_timestamp`,
      scheme_updated_at: sql`current_timestamp`,
    })
    .returning();

  return scheme;
};

export const createDecisionPage = async (
  schemeId,
  pageNumber,
  pageName,
  sliders,
  incentives,
  pnl,
  pageImage = null,
  pageTemplate = null,
  useCustomPageTemplate = false
) => {
  if (!schemeId) {
    throw new Error('schemeId is required for creating a decision page');
  }

  console.log('Creating decision page with:', {
    schemeId,
    pageNumber,
    pageName,
    sliders,
    incentives,
    pnl,
    pageImage,
  });

  const [page] = await db
    .insert(lumenDecisionPage)
    .values({
      scheme_id: schemeId,
      page_number: pageNumber,
      page_name: pageName,
      page_image: pageImage,
      sliders: sliders,
      incentives: incentives,
      page_pnl: pnl,
      page_pnl_prompt_template: pageTemplate,
      use_custom_page_template: useCustomPageTemplate,
    })
    .returning({
      id: lumenDecisionPage.page_id,
      scheme_id: lumenDecisionPage.scheme_id,
      page_number: lumenDecisionPage.page_number,
      page_name: lumenDecisionPage.page_name,
      page_image: lumenDecisionPage.page_image,
      sliders: lumenDecisionPage.sliders,
      incentives: lumenDecisionPage.incentives,
      page_pnl: lumenDecisionPage.page_pnl,
      page_pnl_prompt_template: lumenDecisionPage.page_pnl_prompt_template,
      use_custom_page_template: lumenDecisionPage.use_custom_page_template,
    });

  return page;
};

export const listDecisionSchemes = async (
  db,
  {
    query = '',
    offset = 0,
    limit = 10,
    sort = 'name',
    ascending = true,
    showDisabled = false,
  }
) => {
  const schemes = await db
    .select({
      id: lumenDecisionScheme.scheme_id,
      name: lumenDecisionScheme.scheme_name,
      created_at: lumenDecisionScheme.scheme_created_at,
      updated_at: lumenDecisionScheme.scheme_updated_at,
      disabled: lumenDecisionScheme.scheme_disabled,
    })
    .from(lumenDecisionScheme)
    .where(
      and(
        sql`LOWER(${
          lumenDecisionScheme.scheme_name
        }) LIKE ${`%${query.toLowerCase()}%`}`,
        eq(lumenDecisionScheme.scheme_disabled, showDisabled)
      )
    )
    .orderBy(
      ascending
        ? asc(
            lumenDecisionScheme[
              sort === 'name' ? 'scheme_name' : 'scheme_created_at'
            ]
          )
        : desc(
            lumenDecisionScheme[
              sort === 'name' ? 'scheme_name' : 'scheme_created_at'
            ]
          )
    )
    .limit(limit)
    .offset(offset);

  return schemes;
};

export const toggleDecisionSchemeDisabled = async (id) => {
  const [scheme] = await db
    .update(lumenDecisionScheme)
    .set({
      scheme_disabled: sql`NOT scheme_disabled`,
      scheme_updated_at: sql`current_timestamp`,
    })
    .where(eq(lumenDecisionScheme.scheme_id, id))
    .returning();

  return scheme;
};

export const updateDecisionSchemeWithPages = async (id, name, pnl, pages, globalTemplate = null, useCustomGlobalTemplate = false) => {
  return await db.transaction(async (tx) => {
    // Update the scheme
    const [updatedScheme] = await tx
      .update(lumenDecisionScheme)
      .set({
        scheme_name: name,
        scheme_pnl: pnl,
        global_pnl_prompt_template: globalTemplate,
        use_custom_global_template: useCustomGlobalTemplate,
        scheme_updated_at: sql`current_timestamp`,
      })
      .where(eq(lumenDecisionScheme.scheme_id, id))
      .returning();

    if (!updatedScheme) {
      throw new Error('Decision scheme not found');
    }

    // Delete existing pages
    await tx
      .delete(lumenDecisionPage)
      .where(eq(lumenDecisionPage.scheme_id, id));

    // Create new pages
    const updatedPages = [];
    for (let i = 0; i < pages.length; i++) {
      const page = pages[i];
      const [newPage] = await tx
        .insert(lumenDecisionPage)
        .values({
          scheme_id: id,
          page_number: i + 1,
          page_name: page.page_name || `Page ${i + 1}`,
          page_image: page.page_image || null,
          sliders: page.sliders,
          incentives: page.incentives,
          page_pnl: page.page_pnl,
          page_pnl_prompt_template: page.page_pnl_prompt_template || null,
          use_custom_page_template: page.use_custom_page_template || false,
        })
        .returning();
      updatedPages.push(newPage);
    }

    return {
      scheme: updatedScheme,
      pages: updatedPages,
    };
  });
};
