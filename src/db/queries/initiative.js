import { db } from '../index.js';
import { lumenInitiativeScheme, lumenInitiative } from '../schema.js';
import { eq, sql } from 'drizzle-orm';

export const findInitiativeSchemeById = async (id) => {
  const [scheme] = await db
    .select({
      id: lumenInitiativeScheme.scheme_id,
      name: lumenInitiativeScheme.scheme_name,
      initiativeNumber: lumenInitiativeScheme.scheme_initiative_number,
    })
    .from(lumenInitiativeScheme)
    .where(eq(lumenInitiativeScheme.scheme_id, id))
    .limit(1);

  return scheme;
};

export const findInitiativesBySchemeId = async (schemeId) => {
  const initiatives = await db
    .select({
      id: lumenInitiative.initiative_id,
      name: lumenInitiative.initiative_name,
      description: lumenInitiative.initiative_description,
      metric1: lumenInitiative.metric1,
      metric2: lumenInitiative.metric2,
      metric3: lumenInitiative.metric3,
    })
    .from(lumenInitiative)
    .where(eq(lumenInitiative.initiative_scheme_id, schemeId));

  return initiatives;
};

export const createInitiativeScheme = async (name, initiativeNumber) => {
  const [scheme] = await db
    .insert(lumenInitiativeScheme)
    .values({
      scheme_name: name,
      scheme_initiative_number: initiativeNumber,
      scheme_created_at: sql`current_timestamp`,
    })
    .returning();

  return scheme;
};

export const createInitiative = async (
  schemeId,
  name,
  description,
  metric1,
  metric2,
  metric3
) => {
  const [initiative] = await db
    .insert(lumenInitiative)
    .values({
      initiative_scheme_id: schemeId,
      initiative_name: name,
      initiative_description: description,
      metric1,
      metric2,
      metric3,
    })
    .returning();

  return initiative;
};

export const updateInitiativeScheme = async (id, name, initiativeNumber) => {
  const [scheme] = await db
    .update(lumenInitiativeScheme)
    .set({
      scheme_name: name,
      scheme_initiative_number: initiativeNumber,
    })
    .where(eq(lumenInitiativeScheme.scheme_id, id))
    .returning();

  return scheme;
};

export const updateInitiative = async (
  id,
  name,
  description,
  metric1,
  metric2,
  metric3
) => {
  const [initiative] = await db
    .update(lumenInitiative)
    .set({
      initiative_name: name,
      initiative_description: description,
      metric1,
      metric2,
      metric3,
    })
    .where(eq(lumenInitiative.initiative_id, id))
    .returning();

  return initiative;
};

export const listInitiativeSchemes = async ({
  sort,
  ascending,
  showDisabled,
}) => {
  const schemes = await db
    .select({
      id: lumenInitiativeScheme.scheme_id,
      name: lumenInitiativeScheme.scheme_name,
      initiativeNumber: lumenInitiativeScheme.scheme_initiative_number,
      createdAt: lumenInitiativeScheme.scheme_created_at,
      disabled: lumenInitiativeScheme.scheme_disabled,
    })
    .from(lumenInitiativeScheme)
    .where(eq(lumenInitiativeScheme.scheme_disabled, showDisabled === 'true'))
    .orderBy(sort, ascending === 'true' ? 'asc' : 'desc');

  return schemes;
};

export const listAllInitiativeSchemes = async () => {
  const schemes = await db
    .select({
      id: lumenInitiativeScheme.scheme_id,
      name: lumenInitiativeScheme.scheme_name,
      initiativeNumber: lumenInitiativeScheme.scheme_initiative_number,
      createdAt: lumenInitiativeScheme.scheme_created_at,
      disabled: lumenInitiativeScheme.scheme_disabled,
    })
    .from(lumenInitiativeScheme);

  return schemes;
};

export const toggleInitiativeSchemeDisabled = async (id) => {
  const [scheme] = await db
    .update(lumenInitiativeScheme)
    .set({
      scheme_disabled: sql`NOT scheme_disabled`,
    })
    .where(eq(lumenInitiativeScheme.scheme_id, id))
    .returning();

  return scheme;
};
