import { eq, sql, asc } from 'drizzle-orm';
import { db } from '../index.js';
import { welcomePage } from '../schema.js';

export const findWelcomePageById = async (id) => {
  const [page] = await db
    .select({
      id: welcomePage.id,
      name: welcomePage.name,
      text: welcomePage.text,
      image: welcomePage.image,
    })
    .from(welcomePage)
    .where(eq(welcomePage.id, id))
    .limit(1);

  return page;
};

export const createWelcomePage = async ({ name, text, image }) => {
  const [page] = await db
    .insert(welcomePage)
    .values({
      name,
      text,
      image,
      disabled: false,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return page;
};

export const updateWelcomePage = async (id, { name, text, image }) => {
  const [page] = await db
    .update(welcomePage)
    .set({
      name,
      text,
      image,
    })
    .where(eq(welcomePage.id, id))
    .returning();

  return page;
};

export const listWelcomePages = async ({ sort, ascending, showDisabled }) => {
  const pages = await db
    .select({
      id: welcomePage.id,
      name: welcomePage.name,
      text: welcomePage.text,
      image: welcomePage.image,
      createdAt: welcomePage.created_at,
      disabled: welcomePage.disabled,
    })
    .from(welcomePage)
    .where(eq(welcomePage.disabled, showDisabled === 'true'))
    .orderBy(sort, ascending === 'true' ? 'asc' : 'desc');

  return pages;
};

export const listAllWelcomePages = async () => {
  const pages = await db
    .select({
      id: welcomePage.id,
      name: welcomePage.name,
      text: welcomePage.text,
      image: welcomePage.image,
      createdAt: welcomePage.created_at,
      disabled: welcomePage.disabled,
    })
    .from(welcomePage)
    .where(eq(welcomePage.disabled, false))
    .orderBy(asc(welcomePage.name));

  return pages;
};

export const toggleWelcomePageDisabled = async (id) => {
  const [page] = await db
    .update(welcomePage)
    .set({
      disabled: sql`NOT disabled`,
    })
    .where(eq(welcomePage.id, id))
    .returning();

  return page;
};
