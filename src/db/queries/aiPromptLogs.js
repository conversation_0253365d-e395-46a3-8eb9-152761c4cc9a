import { db } from '../index.js';
import { aiPromptLogs } from '../schema.js';
import { eq, desc, and, gte, lte, sql } from 'drizzle-orm';

/**
 * Create a new AI prompt log entry
 * @param {Object} logData
 * @param {string} logData.finalSystemPrompt - The complete system prompt sent to AI
 * @param {string} logData.finalQuestionPrompt - The complete user prompt sent to AI
 * @param {number} logData.tokensUsed - Total tokens consumed
 * @param {Array} logData.inputSources - Array of data sources used in prompt generation
 * @returns {Promise<Object>} The created log entry
 */
export const createAiPromptLog = async ({
  finalSystemPrompt,
  finalQuestionPrompt,
  tokensUsed,
  inputSources
}) => {
  const [result] = await db
    .insert(aiPromptLogs)
    .values({
      final_system_prompt: finalSystemPrompt,
      final_question_prompt: finalQuestionPrompt,
      tokens_used: tokensUsed,
      input_sources: inputSources,
      created_at: new Date(),
    })
    .returning();

  return result;
};

/**
 * Get recent AI prompt logs with optional limit
 * @param {number} limit - Maximum number of logs to return (default: 100)
 * @returns {Promise<Array>} Array of log entries
 */
export const getRecentAiPromptLogs = async (limit = 100) => {
  const results = await db
    .select()
    .from(aiPromptLogs)
    .orderBy(desc(aiPromptLogs.created_at))
    .limit(limit);

  return results;
};

/**
 * Get AI prompt log by ID
 * @param {number} id - Log entry ID
 * @returns {Promise<Object|null>} Log entry or null if not found
 */
export const getAiPromptLogById = async (id) => {
  const [result] = await db
    .select()
    .from(aiPromptLogs)
    .where(eq(aiPromptLogs.id, id))
    .limit(1);

  return result || null;
};

/**
 * Get AI prompt logs within a date range
 * @param {Date} startDate - Start date for filtering
 * @param {Date} endDate - End date for filtering
 * @returns {Promise<Array>} Array of log entries
 */
export const getAiPromptLogsByDateRange = async (startDate, endDate) => {
  const results = await db
    .select()
    .from(aiPromptLogs)
    .where(
      and(
        gte(aiPromptLogs.created_at, startDate),
        lte(aiPromptLogs.created_at, endDate)
      )
    )
    .orderBy(desc(aiPromptLogs.created_at));

  return results;
};

/**
 * Get token usage statistics
 * @param {Date} startDate - Start date for analysis (optional)
 * @param {Date} endDate - End date for analysis (optional)
 * @returns {Promise<Object>} Token usage statistics
 */
export const getTokenUsageStats = async (startDate = null, endDate = null) => {
  let query = db
    .select({
      total_tokens: sql`SUM(${aiPromptLogs.tokens_used})`,
      avg_tokens: sql`AVG(${aiPromptLogs.tokens_used})`,
      max_tokens: sql`MAX(${aiPromptLogs.tokens_used})`,
      min_tokens: sql`MIN(${aiPromptLogs.tokens_used})`,
      total_calls: sql`COUNT(*)`,
    })
    .from(aiPromptLogs);

  if (startDate && endDate) {
    query = query.where(
      and(
        gte(aiPromptLogs.created_at, startDate),
        lte(aiPromptLogs.created_at, endDate)
      )
    );
  }

  const [result] = await query;
  return result;
};
