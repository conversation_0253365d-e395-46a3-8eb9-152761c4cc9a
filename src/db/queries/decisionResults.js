import { db } from '../index.js';
import { decisionResults } from '../schema.js';
import { eq, and } from 'drizzle-orm';

export const findExistingDecisionResult = async (
  userId,
  clientId,
  decisionSchemeId
) => {
  const [result] = await db
    .select()
    .from(decisionResults)
    .where(
      and(
        eq(decisionResults.user_id, userId),
        eq(decisionResults.client_id, clientId),
        eq(decisionResults.decision_scheme_id, decisionSchemeId)
      )
    )
    .limit(1);

  return result;
};

export const updateDecisionResult = async (
  id,
  selectedValues,
  totalFTE,
  totalInvestment,
  aiAnalysis
) => {
  const [result] = await db
    .update(decisionResults)
    .set({
      selected_values: selectedValues,
      total_fte: totalFTE,
      total_investment: totalInvestment,
      ai_analysis: aiAnalysis,
      created_at: new Date(), // Update timestamp on modification
    })
    .where(eq(decisionResults.id, id))
    .returning();

  return result;
};

export const createDecisionResult = async (
  userId,
  clientId,
  decisionSchemeId,
  selectedValues,
  totalFTE,
  totalInvestment,
  aiAnalysis
) => {
  const [result] = await db
    .insert(decisionResults)
    .values({
      user_id: userId,
      client_id: clientId,
      decision_scheme_id: decisionSchemeId,
      selected_values: selectedValues,
      total_fte: totalFTE,
      total_investment: totalInvestment,
      ai_analysis: aiAnalysis,
      created_at: new Date(),
    })
    .returning();

  return result;
};

export const findDecisionResultsByUserId = async (userId) => {
  const results = await db
    .select()
    .from(decisionResults)
    .where(eq(decisionResults.user_id, userId))
    .orderBy(decisionResults.created_at);

  return results;
};

export const findDecisionResultById = async (id) => {
  const [result] = await db
    .select()
    .from(decisionResults)
    .where(eq(decisionResults.id, id))
    .limit(1);

  return result;
};
