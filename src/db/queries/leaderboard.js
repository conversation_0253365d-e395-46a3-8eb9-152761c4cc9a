import { db } from '../index.js';
import {
  leaderboardScheme,
  leaderboardRegion,
  leaderboardUser,
} from '../schema.js';
import { eq, inArray, sql } from 'drizzle-orm';

export const findLeaderboardSchemeById = async (id) => {
  const [scheme] = await db
    .select({
      id: leaderboardScheme.id,
      name: leaderboardScheme.name,
      disabled: leaderboardScheme.disabled,
      createdAt: leaderboardScheme.created_at,
    })
    .from(leaderboardScheme)
    .where(eq(leaderboardScheme.id, id))
    .limit(1);

  return scheme;
};

export const findLeaderboardRegionsBySchemeId = async (schemeId) => {
  const regions = await db
    .select({
      id: leaderboardRegion.id,
      name: leaderboardRegion.name,
      leaderboardSchemeId: leaderboardRegion.leaderboard_scheme_id,
      createdAt: leaderboardRegion.created_at,
    })
    .from(leaderboardRegion)
    .where(eq(leaderboardRegion.leaderboard_scheme_id, schemeId));

  return regions;
};

export const findLeaderboardUsersByRegionIds = async (regionIds) => {
  const users = await db
    .select({
      id: leaderboardUser.id,
      name: leaderboardUser.name,
      points: leaderboardUser.points,
      leaderboardRegionId: leaderboardUser.leaderboard_region_id,
      createdAt: leaderboardUser.created_at,
    })
    .from(leaderboardUser)
    .where(inArray(leaderboardUser.leaderboard_region_id, regionIds));

  return users;
};

export const createLeaderboardScheme = async (name) => {
  const [scheme] = await db
    .insert(leaderboardScheme)
    .values({
      name,
      disabled: false,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return scheme;
};

export const createLeaderboardRegion = async (name, schemeId) => {
  const [region] = await db
    .insert(leaderboardRegion)
    .values({
      name,
      leaderboard_scheme_id: schemeId,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return region;
};

export const createLeaderboardUser = async (name, points, regionId) => {
  const [user] = await db
    .insert(leaderboardUser)
    .values({
      name,
      points,
      leaderboard_region_id: regionId,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return user;
};

export const updateLeaderboardScheme = async (id, name) => {
  const [scheme] = await db
    .update(leaderboardScheme)
    .set({
      name,
    })
    .where(eq(leaderboardScheme.id, id))
    .returning();

  return scheme;
};

export const deleteLeaderboardRegionsBySchemeId = async (schemeId) => {
  const regions = await db
    .delete(leaderboardRegion)
    .where(eq(leaderboardRegion.leaderboard_scheme_id, schemeId))
    .returning();

  return regions;
};

export const deleteLeaderboardUsersByRegionIds = async (regionIds) => {
  const users = await db
    .delete(leaderboardUser)
    .where(inArray(leaderboardUser.leaderboard_region_id, regionIds))
    .returning();

  return users;
};

export const listLeaderboardSchemes = async ({
  sort,
  ascending,
  showDisabled,
}) => {
  const schemes = await db
    .select({
      id: leaderboardScheme.id,
      name: leaderboardScheme.name,
      disabled: leaderboardScheme.disabled,
      createdAt: leaderboardScheme.created_at,
    })
    .from(leaderboardScheme)
    .where(eq(leaderboardScheme.disabled, showDisabled === 'true'))
    .orderBy(sort, ascending === 'true' ? 'asc' : 'desc');

  return schemes;
};

export const listAllLeaderboardSchemes = async () => {
  const schemes = await db
    .select({
      id: leaderboardScheme.id,
      name: leaderboardScheme.name,
      disabled: leaderboardScheme.disabled,
      createdAt: leaderboardScheme.created_at,
    })
    .from(leaderboardScheme);

  return schemes;
};

export const toggleLeaderboardSchemeDisabled = async (id) => {
  const [scheme] = await db
    .update(leaderboardScheme)
    .set({
      disabled: sql`NOT disabled`,
    })
    .where(eq(leaderboardScheme.id, id))
    .returning();

  return scheme;
};
