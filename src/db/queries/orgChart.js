import { db } from '../index.js';
import {
  orgChartScheme,
  orgChart,
  orgChartType,
  orgChartUser,
} from '../schema.js';
import { eq, inArray, sql, asc, and } from 'drizzle-orm';

export const findOrgChartSchemeById = async (id) => {
  const [scheme] = await db
    .select({
      id: orgChartScheme.id,
      name: orgChartScheme.name,
    })
    .from(orgChartScheme)
    .where(eq(orgChartScheme.id, id))
    .limit(1);

  return scheme;
};

export const findOrgChartsBySchemeId = async (schemeId) => {
  const charts = await db
    .select({
      id: orgChart.id,
      name: orgChart.name,
      orgChartSchemeId: orgChart.org_chart_scheme_id,
    })
    .from(orgChart)
    .where(eq(orgChart.org_chart_scheme_id, schemeId));

  return charts;
};

export const findOrgChartTypesByChartIds = async (chartIds) => {
  const types = await db
    .select({
      id: orgChartType.id,
      name: orgChartType.name,
      orgChartId: orgChartType.org_chart_id,
    })
    .from(orgChartType)
    .where(inArray(orgChartType.org_chart_id, chartIds));

  return types;
};

export const findOrgChartUsersByTypeIds = async (typeIds) => {
  const users = await db
    .select({
      id: orgChartUser.id,
      name: orgChartUser.name,
      orgChartTypeId: orgChartUser.org_chart_type_id,
    })
    .from(orgChartUser)
    .where(inArray(orgChartUser.org_chart_type_id, typeIds))
    .orderBy(asc(orgChartUser.id));

  return users;
};

export const createOrgChartScheme = async (name) => {
  const [scheme] = await db
    .insert(orgChartScheme)
    .values({
      name,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return scheme;
};

export const createOrgChart = async (name, schemeId) => {
  const [chart] = await db
    .insert(orgChart)
    .values({
      name,
      org_chart_scheme_id: schemeId,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return chart;
};

export const createOrgChartType = async (name, chartId) => {
  const [type] = await db
    .insert(orgChartType)
    .values({
      name,
      org_chart_id: chartId,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return type;
};

export const createOrgChartUser = async (name, typeId) => {
  const [user] = await db
    .insert(orgChartUser)
    .values({
      name,
      org_chart_type_id: typeId,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return user;
};

export const updateOrgChartScheme = async (id, name) => {
  const [scheme] = await db
    .update(orgChartScheme)
    .set({
      name,
    })
    .where(eq(orgChartScheme.id, id))
    .returning();

  return scheme;
};

export const deleteOrgChartsBySchemeId = async (schemeId) => {
  const charts = await db
    .delete(orgChart)
    .where(eq(orgChart.org_chart_scheme_id, schemeId))
    .returning();

  return charts;
};

export const deleteOrgChartTypesByChartIds = async (chartIds) => {
  const types = await db
    .delete(orgChartType)
    .where(inArray(orgChartType.org_chart_id, chartIds))
    .returning();

  return types;
};

export const deleteOrgChartUsersByTypeIds = async (typeIds) => {
  const users = await db
    .delete(orgChartUser)
    .where(inArray(orgChartUser.org_chart_type_id, typeIds))
    .returning();

  return users;
};

export const listOrgChartSchemes = async ({
  sort,
  ascending,
  showDisabled,
  query,
}) => {
  const schemes = await db
    .select({
      id: orgChartScheme.id,
      name: orgChartScheme.name,
      createdAt: orgChartScheme.created_at,
      disabled: orgChartScheme.disabled,
    })
    .from(orgChartScheme)
    .where(
      and(
        eq(orgChartScheme.disabled, showDisabled === 'true'),
        sql`LOWER(${orgChartScheme.name}) LIKE ${
          '%' + query.toLowerCase() + '%'
        }`
      )
    )
    .orderBy(sort, ascending === 'true' ? 'asc' : 'desc');

  return schemes;
};

export const listAllOrgChartSchemes = async () => {
  const schemes = await db
    .select({
      id: orgChartScheme.id,
      name: orgChartScheme.name,
      createdAt: orgChartScheme.created_at,
      disabled: orgChartScheme.disabled,
    })
    .from(orgChartScheme);

  return schemes;
};

export const toggleOrgChartSchemeDisabled = async (id) => {
  const [scheme] = await db
    .update(orgChartScheme)
    .set({
      disabled: sql`NOT disabled`,
    })
    .where(eq(orgChartScheme.id, id))
    .returning();

  return scheme;
};
