import { eq, and, sql } from 'drizzle-orm';
import { db } from '../index.js';
import { lumenTeam } from '../schema.js';

export const findUserByEmailAndPassword = async (email, password) => {
  const [user] = await db
    .select({
      team_id: lumenTeam.team_id,
      team_email: lumenTeam.team_email,
      client_id: lumenTeam.client_id,
    })
    .from(lumenTeam)
    .where(
      and(
        eq(lumenTeam.team_email, email),
        eq(lumenTeam.team_password, sql`MD5(${password})`),
        eq(lumenTeam.team_disabled, false)
      )
    )
    .limit(1);

  return user;
};

export const createUser = async ({ name, email, password, clientId }) => {
  const [user] = await db
    .insert(lumenTeam)
    .values({
      team_name: name,
      team_email: email,
      team_password: sql`MD5(${password})`,
      client_id: clientId,
      team_disabled: false,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return user;
};

export const findUserById = async (id) => {
  const [user] = await db
    .select({
      team_id: lumenTeam.team_id,
      team_name: lumenTeam.team_name,
      team_email: lumenTeam.team_email,
      client_id: lumenTeam.client_id,
    })
    .from(lumenTeam)
    .where(eq(lumenTeam.team_id, id))
    .limit(1);

  return user;
};

export const updateUserPassword = async (id, password) => {
  const [user] = await db
    .update(lumenTeam)
    .set({
      team_password: sql`MD5(${password})`,
    })
    .where(eq(lumenTeam.team_id, id))
    .returning();

  return user;
};

export const findUserByEmail = async (email) => {
  const [user] = await db
    .select({
      team_id: lumenTeam.team_id,
      team_email: lumenTeam.team_email,
      client_id: lumenTeam.client_id,
    })
    .from(lumenTeam)
    .where(eq(lumenTeam.team_email, email))
    .limit(1);

  return user;
};

export const updateTeamDetails = async (teamId, name) => {
  const [team] = await db
    .update(lumenTeam)
    .set({
      team_name: name,
    })
    .where(eq(lumenTeam.team_id, teamId))
    .returning();

  return team;
};
