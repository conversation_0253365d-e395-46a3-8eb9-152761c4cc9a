import { eq, and, sql, inArray } from 'drizzle-orm';
import {
  lumenTeam,
  teamClients,
  selfAssessmentAnswers,
  teamGoals,
  globalTeamMetric,
  globalTeamMetricsScheme,
  lumenTeamSelectedChallenge,
  lumenTeamSelectedInitiative,
  teamSelectedOrgChart,
  decisionResults,
} from '../schema.js';

export const findTeamById = async (db, teamId) => {
  const team = await db
    .select({
      id: lumenTeam.team_id,
      name: lumenTeam.team_name,
      email: lumenTeam.team_email,
      clientId: teamClients.client_id,
    })
    .from(lumenTeam)
    .leftJoin(teamClients, eq(teamClients.team_id, lumenTeam.team_id))
    .where(eq(lumenTeam.team_id, teamId));

  console.log('team', team);

  return team;
};

export const toggleTeamDisabled = async (db, teamId) => {
  return await db
    .update(lumenTeam)
    .set({
      team_disabled: sql`NOT team_disabled`,
    })
    .where(eq(lumenTeam.team_id, teamId));
};

export const resetTeamHistory = async (db, teamId) => {
  const promises = [
    // Reset global team metrics
    db
      .update(globalTeamMetric)
      .set({ value: 0 })
      .where(eq(globalTeamMetric.team_id, teamId)),

    // Reset team goals
    db
      .update(teamGoals)
      .set({ goal_1: '', goal_2: '', goal_3: '' })
      .where(eq(teamGoals.team_id, teamId)),

    // Clean selected initiatives
    db
      .delete(lumenTeamSelectedChallenge)
      .where(eq(lumenTeamSelectedChallenge.selected_team_id, teamId)),

    // Clean selected challenges
    db
      .delete(lumenTeamSelectedInitiative)
      .where(eq(lumenTeamSelectedInitiative.selected_team_id, teamId)),

    // Clean selected org chart
    db
      .delete(teamSelectedOrgChart)
      .where(eq(teamSelectedOrgChart.team_id, teamId)),

    // Clean self assessment
    db
      .delete(selfAssessmentAnswers)
      .where(eq(selfAssessmentAnswers.team_id, teamId)),

    // Clean decision results - this was missing and causing the issue
    db
      .delete(decisionResults)
      .where(eq(decisionResults.user_id, teamId)),
  ];

  return await Promise.all(promises);
};

export const findTeamClientRelations = async (db, clientId) => {
  return await db
    .select({
      team_id: teamClients.team_id,
    })
    .from(teamClients)
    .where(eq(teamClients.client_id, clientId));
};

export const findTeamsPaginated = async (db, params) => {
  const { query, offset, limit, teamIds, showDisabled, sort, sortOrder } =
    params;

  const baseQuery = db
    .select({
      id: lumenTeam.team_id,
      name: lumenTeam.team_name,
      disabled: lumenTeam.team_disabled,
      created: sql`TO_CHAR(${lumenTeam.team_created_at}, 'YYYY-MM-DD')`,
      total: sql`COUNT(*) OVER()`,
    })
    .from(lumenTeam)
    .where(
      and(
        sql`LOWER(${lumenTeam.team_name}) LIKE ${`%${query.toLowerCase()}%`}`,
        eq(lumenTeam.team_disabled, showDisabled)
      )
    );

  if (teamIds.length > 0) {
    baseQuery.where(inArray(lumenTeam.team_id, teamIds));
  }

  const result = await baseQuery
    .orderBy(
      sort === 'team_created_at'
        ? lumenTeam.team_created_at
        : lumenTeam.team_name,
      sortOrder === 'ASC' ? 'asc' : 'desc'
    )
    .offset(offset)
    .limit(parseInt(limit));

  return result;
};

export const findAllTeams = async (db) => {
  return await db
    .select({
      id: lumenTeam.team_id,
      name: lumenTeam.team_name,
      disabled: lumenTeam.team_disabled,
      clientId: teamClients.client_id,
    })
    .from(lumenTeam)
    .leftJoin(teamClients, eq(teamClients.team_id, lumenTeam.team_id))
    .orderBy(lumenTeam.team_name);
};

export const deleteTeamClients = async (db, teamId) => {
  return await db.delete(teamClients).where(eq(teamClients.team_id, teamId));
};

export const updateTeamDetails = async (db, teamId, data) => {
  return await db
    .update(lumenTeam)
    .set({
      team_name: data.name,
      team_email: data.email,
    })
    .where(eq(lumenTeam.team_id, teamId));
};

export const updateTeamPassword = async (db, teamId, password) => {
  return await db
    .update(lumenTeam)
    .set({
      team_password: sql`MD5(${password})`,
    })
    .where(eq(lumenTeam.team_id, teamId));
};

export const findTeamGoals = async (db, teamId, clientId) => {
  return await db
    .select()
    .from(teamGoals)
    .where(
      and(eq(teamGoals.team_id, teamId), eq(teamGoals.client_id, clientId))
    );
};

export const findGlobalTeamMetric = async (db, teamId, clientId) => {
  return await db
    .select()
    .from(globalTeamMetric)
    .where(
      and(
        eq(globalTeamMetric.team_id, teamId),
        eq(globalTeamMetric.client_id, clientId)
      )
    );
};

export const findSelfAssessmentAnswers = async (db, teamId, clientId) => {
  return await db
    .select()
    .from(selfAssessmentAnswers)
    .where(
      and(
        eq(selfAssessmentAnswers.team_id, teamId),
        eq(selfAssessmentAnswers.client_id, clientId)
      )
    );
};

export const insertTeam = async (db, teamData) => {
  const result = await db
    .insert(lumenTeam)
    .values({
      team_name: teamData.name,
      team_email: teamData.email,
      team_password: sql`MD5(${teamData.password})`,
      team_created_at: sql`current_timestamp`,
    })
    .returning({
      team_id: lumenTeam.team_id,
    });

  return result;
};

export const insertTeamClient = async (db, teamId, clientId) => {
  return await db.insert(teamClients).values({
    team_id: teamId,
    client_id: clientId,
  });
};

export const getGlobalTeamMetricsSchemes = async (db, clientId) => {
  return await db
    .select({
      id: globalTeamMetricsScheme.id,
    })
    .from(globalTeamMetricsScheme)
    .where(eq(globalTeamMetricsScheme.client_id, clientId));
};

export const insertGlobalTeamMetric = async (db, data) => {
  return await db.insert(globalTeamMetric).values({
    value: data.value,
    global_team_metric_scheme_id: data.schemeId,
    team_id: data.teamId,
    client_id: data.clientId,
    created_at: sql`current_timestamp`,
  });
};
