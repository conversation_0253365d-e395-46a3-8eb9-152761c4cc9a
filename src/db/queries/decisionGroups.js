import { eq, sql } from 'drizzle-orm';
import { db } from '../index.js';
import { decisionGroups } from '../schema.js';

export const findDecisionGroupById = async (id) => {
  const [group] = await db
    .select({
      id: decisionGroups.id,
      name: decisionGroups.name,
      created_at: decisionGroups.created_at,
    })
    .from(decisionGroups)
    .where(eq(decisionGroups.id, id))
    .limit(1);

  return group;
};

export const createDecisionGroup = async ({ name }) => {
  const [group] = await db
    .insert(decisionGroups)
    .values({
      name,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return group;
};

export const deleteDecisionGroup = async (id) => {
  const [group] = await db
    .delete(decisionGroups)
    .where(eq(decisionGroups.id, id))
    .returning();

  return group;
};

export const listAllDecisionGroups = async () => {
  const groups = await db
    .select({
      id: decisionGroups.id,
      name: decisionGroups.name,
      created_at: decisionGroups.created_at,
    })
    .from(decisionGroups)
    .orderBy(decisionGroups.name);

  return groups;
};
