# Database Queries

This directory contains database query functions that provide a data access layer for the Lumen simulation platform.

## Overview

Query modules encapsulate database operations using Drizzle ORM, providing clean interfaces for controllers to interact with the database. Each file corresponds to a specific domain or resource.

## Query Modules

### Core Business Logic
- **decision.js** - Decision scheme and page CRUD operations
- **decisionGroups.js** - Decision grouping and organization queries
- **decisionResults.js** - Simulation results storage and retrieval
- **client.js** - Client data management and configuration
- **team.js** - Team operations and member queries
- **user.js** - User authentication, profile, and preference queries

### Assessment & Learning
- **challenge.js** - Challenge data persistence and retrieval
- **initiative.js** - Initiative tracking and progress queries
- **selfAssessment.js** - Assessment questionnaire and scoring queries
- **leaderboard.js** - Ranking calculations and leaderboard data

### Organization & Structure
- **orgChart.js** - Organizational chart structure and hierarchy queries

### Content & Analytics
- **welcomePage.js** - Welcome page content queries
- **aiPromptLogs.js** - AI interaction logging and analytics

### Administration
- **admin.js** - Administrative queries and system management

## Architecture

Query modules follow consistent patterns:
- Import database connection and schema definitions
- Export named functions for specific operations
- Use Drizzle ORM for type-safe database operations
- Include proper error handling
- Support transactions where needed
- Follow naming conventions: `findX`, `createX`, `updateX`, `deleteX`

## Dependencies

- Database connection from `../index.js`
- Schema definitions from `../schema.js`
- Drizzle ORM operators (`eq`, `sql`, `and`, `asc`, `desc`)

## Usage

Controllers import specific query functions:
```javascript
import { findDecisionSchemeById, createDecisionScheme } from '../db/queries/decision.js';
```

Query functions return standardized data structures that controllers can directly use or transform as needed.