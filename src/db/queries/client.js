import { eq, and, desc, asc, like, sql } from 'drizzle-orm';
import {
  clients,
  globalTeamMetricsScheme,
  lumenInitiativeScheme,
  lumenChallengeScheme,
} from '../schema.js';

export const findClientById = async (db, id) => {
  const result = await db
    .select({
      id: clients.id,
      name: clients.name,
      backgroundImage: clients.background_image,
      logoImage: clients.logo_image,
      homeTabName: clients.home_tab_name,
      challengesTabName: clients.challenges_tab_name,
      goalsTabName: clients.goals_tab_name,
      strategicTabName: clients.strategic_tab_name,
      orgChartTabName: clients.org_chart_tab_name,
      selfAssessmentTabName: clients.self_assessment_tab_name,
      leaderboardTabName: clients.leaderboard_tab_name,
      darkHighlightColor: clients.dark_highlight_color,
      lightHighlightColor: clients.light_highlight_color,
      homeSchemeId: clients.home_scheme_id,
      initiativeSchemeId: clients.initiative_scheme_id,
      challengeSchemeId: clients.challenge_scheme_id,
      leaderboardSchemeId: clients.leaderboard_scheme_id,
      orgChartSchemeId: clients.org_chart_scheme_id,
      selfAssessmentSchemeId: clients.self_assessment_scheme_id,
      homeTabVisibility: clients.home_tab_visibility,
      goalsTabVisibility: clients.goals_tab_visibility,
      challengesTabVisibility: clients.challenges_tab_visibility,
      initiativesTabVisibility: clients.initiatives_tab_visibility,
      orgChartTabVisibility: clients.org_chart_tab_visibility,
      selfAssessmentTabVisibility: clients.self_assessment_tab_visibility,
      leaderboardTabVisibility: clients.leaderboard_tab_visibility,
      signUpEmailDomain: clients.sign_up_email_domain,
      workshopImage: clients.workshop_image,
      isSignUpEnabled: clients.is_sign_up_enabled,
      decisionSchemeId: clients.decision_scheme_id,
      decisionsTabName: clients.decision_tab_name,
      decisionsTabVisibility: clients.decision_tab_visibility,
      schemeOrder: clients.scheme_order,
      fteMax: clients.fte_max,
      investmentMax: clients.investment_max,
      aiSummaryTitle: clients.ai_summary_title,
      created: sql`TO_CHAR(${clients.created_at}, 'YYYY-MM-DD')`,
    })
    .from(clients)
    .where(eq(clients.id, id))
    .limit(1);

  return result[0];
};

export const findGlobalTeamMetricsSchemes = async (db, clientId) => {
  const result = await db
    .select()
    .from(globalTeamMetricsScheme)
    .where(eq(globalTeamMetricsScheme.client_id, clientId));

  return result;
};

export const findInitiativeScheme = async (db, schemeId) => {
  const result = await db
    .select()
    .from(lumenInitiativeScheme)
    .where(eq(lumenInitiativeScheme.scheme_id, schemeId))
    .limit(1);

  return result[0];
};

export const findChallengeScheme = async (db, schemeId) => {
  const result = await db
    .select()
    .from(lumenChallengeScheme)
    .where(eq(lumenChallengeScheme.scheme_id, schemeId))
    .limit(1);

  return result[0];
};

export const listClients = async (
  db,
  {
    query,
    offset,
    limit,
    sort = 'name',
    ascending = true,
    showDisabled = false,
  }
) => {
  const result = await db
    .select({
      id: clients.id,
      name: clients.name,
      backgroundImage: clients.background_image,
      logoImage: clients.logo_image,
      challengesTabName: clients.challenges_tab_name,
      homeTabName: clients.home_tab_name,
      goalsTabName: clients.goals_tab_name,
      orgChartTabName: clients.org_chart_tab_name,
      selfAssessmentTabName: clients.self_assessment_tab_name,
      leaderboardTabName: clients.leaderboard_tab_name,
      strategicInitiativesTabName: clients.strategic_tab_name,
      homeTabVisibility: clients.home_tab_visibility,
      goalsTabVisibility: clients.goals_tab_visibility,
      challengesTabVisibility: clients.challenges_tab_visibility,
      initiativesTabVisibility: clients.initiatives_tab_visibility,
      selfAssessmentTabVisibility: clients.self_assessment_tab_visibility,
      leaderboardTabVisibility: clients.leaderboard_tab_visibility,
      disabled: clients.disabled,
      schemeOrder: clients.scheme_order,
      fteMax: clients.fte_max,
      investmentMax: clients.investment_max,
      aiSummaryTitle: clients.ai_summary_title,
      created: sql`TO_CHAR(${clients.created_at}, 'YYYY-MM-DD')`,
      total: sql`COUNT(*) OVER()`,
    })
    .from(clients)
    .where(
      and(
        like(sql`LOWER(${clients.name})`, `%${query.toLowerCase()}%`),
        eq(clients.disabled, showDisabled)
      )
    )
    .orderBy(ascending ? asc(sort) : desc(sort))
    .offset(offset)
    .limit(limit);

  return result;
};

export const listAllClients = async (db) => {
  const result = await db
    .select()
    .from(clients)
    .where(eq(clients.disabled, false))
    .orderBy(asc(clients.name));

  return result;
};

export const insertGlobalTeamMetric = async (
  tx,
  { clientId, name, alias, defaultValue }
) => {
  const result = await tx
    .insert(globalTeamMetricsScheme)
    .values({
      client_id: clientId,
      name,
      alias,
      default_value: defaultValue,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return result[0];
};

export const updateGlobalTeamMetric = async (
  tx,
  { clientId, name, alias, defaultValue, id }
) => {
  const result = await tx
    .update(globalTeamMetricsScheme)
    .set({
      client_id: clientId,
      name,
      alias,
      default_value: defaultValue,
    })
    .where(eq(globalTeamMetricsScheme.id, id))
    .returning({
      id: globalTeamMetricsScheme.id,
      name: globalTeamMetricsScheme.name,
    });

  return result[0];
};
