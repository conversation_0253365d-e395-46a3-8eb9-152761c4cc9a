import { eq, and, desc, asc, like, sql } from 'drizzle-orm';
import { lumenChallengeScheme, lumenChallenge } from '../schema.js';

export const findChallengeSchemeById = async (db, id) => {
  const schemes = await db
    .select({
      id: lumenChallengeScheme.scheme_id,
      name: lumenChallengeScheme.scheme_name,
    })
    .from(lumenChallengeScheme)
    .where(eq(lumenChallengeScheme.scheme_id, id))
    .limit(1);

  return schemes[0];
};

export const findChallengesBySchemeid = async (db, schemeId) => {
  const challenges = await db
    .select({
      id: lumenChallenge.challenge_id,
      imageUrl: lumenChallenge.challenge_image_url,
      description: lumenChallenge.challenge_description,
      optionA: lumenChallenge.challenge_option_a,
      consequenceA: lumenChallenge.challenge_consequence_a,
      optionMetric1A: lumenChallenge.challenge_option_metric1_a,
      optionMetric2A: lumenChallenge.challenge_option_metric2_a,
      optionMetric3A: lumenChallenge.challenge_option_metric3_a,
      optionB: lumenChallenge.challenge_option_b,
      consequenceB: lumenChallenge.challenge_consequence_b,
      optionMetric1B: lumenChallenge.challenge_option_metric1_b,
      optionMetric2B: lumenChallenge.challenge_option_metric2_b,
      optionMetric3B: lumenChallenge.challenge_option_metric3_b,
      optionC: lumenChallenge.challenge_option_c,
      consequenceC: lumenChallenge.challenge_consequence_c,
      optionMetric1C: lumenChallenge.challenge_option_metric1_c,
      optionMetric2C: lumenChallenge.challenge_option_metric2_c,
      optionMetric3C: lumenChallenge.challenge_option_metric3_c,
    })
    .from(lumenChallenge)
    .where(eq(lumenChallenge.challenge_scheme_id, schemeId));

  return challenges;
};

export const listChallengeSchemes = async (
  db,
  {
    query,
    offset,
    limit,
    sort = 'scheme_name',
    ascending = true,
    showDisabled = false,
  }
) => {
  const schemes = await db
    .select({
      id: lumenChallengeScheme.scheme_id,
      name: lumenChallengeScheme.scheme_name,
      disabled: lumenChallengeScheme.scheme_disabled,
      created: sql`TO_CHAR(${lumenChallengeScheme.scheme_created_at}, 'YYYY-MM-DD')`,
      total: sql`COUNT(*) OVER()`,
    })
    .from(lumenChallengeScheme)
    .where(
      and(
        like(
          sql`LOWER(${lumenChallengeScheme.scheme_name})`,
          `%${query.toLowerCase()}%`
        ),
        eq(lumenChallengeScheme.scheme_disabled, showDisabled)
      )
    )
    .orderBy(ascending ? asc(sort) : desc(sort))
    .offset(offset)
    .limit(limit);

  return schemes;
};

export const toggleChallengeSchemeDisabled = async (db, id) => {
  await db
    .update(lumenChallengeScheme)
    .set({
      scheme_disabled: sql`NOT scheme_disabled`,
    })
    .where(eq(lumenChallengeScheme.scheme_id, id));
};
