import { drizzle } from 'drizzle-orm/node-postgres';
import pg from 'pg';
import config from '../../config.json' with { type: 'json' };
import * as schema from './schema.js';

const { Client } = pg;

// Create the client
const client = new Client(config.db);

// Initialize connection
const initDB = async () => {
  try {
    await client.connect();
    console.log('✨ Database connected successfully');
    console.log(
      `🔌 Connected to ${config.db.database} at ${config.db.host}:${config.db.port}`
    );
  } catch (error) {
    console.error('❌ Error connecting to the database:', error);
    process.exit(1);
  }
};

// Connect immediately
await initDB();

// Create and export the drizzle db instance
export const db = drizzle(client, { schema });

// Export a way to end the connection during shutdown
export const disconnect = async () => {
  try {
    await client.end();
    console.log('👋 Database connection closed');
  } catch (error) {
    console.error('Error closing database connection:', error);
    throw error;
  }
};

process.on('SIGINT', async () => {
  console.log('Received SIGINT. Closing database connection...');
  await disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM. Closing database connection...');
  await disconnect();
  process.exit(0);
});

export default db;
