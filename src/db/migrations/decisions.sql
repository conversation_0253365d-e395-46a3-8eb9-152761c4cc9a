-- Create decision scheme table
CREATE TABLE IF NOT EXISTS lumen_decision_scheme (
    scheme_id serial PRIMARY KEY,
    scheme_name varchar(253) NOT NULL,
    scheme_created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    scheme_updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    scheme_disabled boolean DEFAULT false
);

-- Create decision page table
CREATE TABLE IF NOT EXISTS lumen_decision_page (
    page_id serial PRIMARY KEY,
    scheme_id integer REFERENCES lumen_decision_scheme(scheme_id),
    page_number integer NOT NULL,
    page_name varchar(255) DEFAULT NULL,
    sliders jsonb NOT NULL,
    incentives jsonb NOT NULL
);

-- Add columns and constraints to clients table
DO $$
BEGIN
    -- Add columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name='clients' AND column_name='decision_scheme_id') THEN
        ALTER TABLE clients ADD COLUMN decision_scheme_id integer;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name='clients' AND column_name='decision_tab_name') THEN
        ALTER TABLE clients ADD COLUMN decision_tab_name VARCHAR(255) DEFAULT 'Decisions';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name='clients' AND column_name='decision_tab_visibility') THEN
        ALTER TABLE clients ADD COLUMN decision_tab_visibility BOOLEAN DEFAULT false;
    END IF;

    -- Add foreign key constraint if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                  WHERE constraint_name='clients_decision_scheme_id_fk') THEN
        ALTER TABLE clients
        ADD CONSTRAINT clients_decision_scheme_id_fk
        FOREIGN KEY (decision_scheme_id) REFERENCES lumen_decision_scheme(scheme_id);
    END IF;
END
$$;



