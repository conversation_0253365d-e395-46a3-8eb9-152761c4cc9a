-- Create a table to preserve goals for each team & client separately
create table team_goals
(
	id serial
		constraint team_goals_pk
			primary key,
	team_id int not null
		constraint team_goals_lumen_team_team_id_fk
			references lumen_team,
	client_id int not null
		constraint team_goals_clients_id_fk
			references clients,
	goal_1 varchar(1096),
	goal_2 varchar(1096),
	goal_3 varchar(1096),
	created_at TIMESTAMP
);

-- Migrate all existing team goals to the new team_goals table
INSERT INTO team_goals (team_id, client_id, goal_1, goal_2, goal_3)
  SELECT team_id, client_id, team_goal1 as goal_1, team_goal2 as goal_2, team_goal3 as goal_3
  FROM lumen_team
  WHERE client_id notnull;
