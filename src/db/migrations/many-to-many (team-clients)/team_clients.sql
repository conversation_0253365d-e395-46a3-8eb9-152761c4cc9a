-- Create table for many-to-many relation between team and clients
create table team_clients
(
	id serial,
	team_id int not null
		constraint team_clients_lumen_team_team_id_fk
			references lumen_team,
	client_id int not null
		constraint team_clients_clients_id_fk
			references clients
);

comment on table team_clients is 'Third table to build many<>many relation between team & clients';

alter table team_clients
	add constraint team_clients_pk
		primary key (id);


-- Migrate all existing teams from one-to-many to many-to-many relations
INSERT INTO team_clients (team_id, client_id)
  SELECT team_id, client_id
  FROM lumen_team
  WHERE client_id notnull;
