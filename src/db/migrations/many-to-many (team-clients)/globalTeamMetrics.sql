-- Add client_id column to global team metrics to support global team metrics based on team & client
alter table global_team_metric
	add client_id int not null;

alter table global_team_metric
	add constraint global_team_metric_clients_id_fk
		foreign key (client_id) references clients;

-- 	Update client_id value for newly created client_id column in global team metrics
UPDATE global_team_metric AS t
SET client_id = (
  SELECT client_id
  FROM global_team_metrics_scheme AS ts
  WHERE t.global_team_metric_scheme_id = ts.id
)
