# Database Migrations

This directory contains SQL migration files for the Lumen simulation platform database schema.

## Overview

Migrations are SQL scripts that define and evolve the database schema over time. They ensure consistent database structure across different environments and provide a version-controlled history of schema changes.

## Migration Files

### Core Schema
- **clients.sql** - Client table schema and initial data
- **decisions.sql** - Decision schemes and pages table structure
- **decision_groups.sql** - Decision grouping functionality
- **create_decision_results_table.sql** - Results storage for simulations

### User & Team Management
- **signUp.sql** - User registration and authentication schema
- **uniqueTeamEmails.sql** - Email uniqueness constraints for teams
- **globalTeamMetrics.sql** - Team performance metrics tables
- **leaderboards.sql** - Leaderboard and ranking schema

### Assessment & Learning
- **selfAssessment.sql** - Self-assessment questionnaire schema
- **meetingTimestamp.sql** - Meeting and session tracking

### Organization Structure
- **orgCharts.sql** - Organizational chart table definitions
- **orgChartsConstraints.sql** - Constraints and relationships for org charts
- **orgChartVisibility.sql** - Visibility and permission settings

### Content & Pages
- **welcomePages.sql** - Welcome page content schema
- **add_page_name_to_decision_page.sql** - Page naming functionality
- **add_page_image_to_decision_page.sql** - Image support for pages
- **add_pnl_to_decision_page.sql** - P&L data integration

### AI & Analytics
- **add_ai_prompt_logs_table.sql** - AI interaction logging
- **add_ai_summary_title_to_clients.sql** - AI-generated client summaries
- **add_pnl_prompt_templates.sql** - AI prompt template management

### Schema Updates
- **20240000_update_decision_fk.sql** - Foreign key relationship updates
- **20240124_add_scheme_order.sql** - Scheme ordering functionality
- **20240125_add_page_pnl.sql** - P&L page integration
- **move_pnl_to_decision_scheme.sql** - P&L data restructuring
- **add_fte_max_investment_max_to_clients.sql** - Client constraint fields

### Team-Client Relationships
The `many-to-many (team-clients)/` subdirectory contains:
- **team_clients.sql** - Many-to-many relationship tables
- **selectedChallenge.sql** - Team challenge selections
- **selectedInitiative.sql** - Team initiative selections
- **teamGoals.sql** - Team goal tracking
- **teamSelectedOrgChart.sql** - Team org chart selections

## Migration Naming Convention

- Descriptive names indicating the change purpose
- Date prefixes for chronological ordering (format: YYYYMMDD_)
- Action prefixes: `add_`, `create_`, `update_`, `move_`

## Running Migrations

Migrations are typically executed through the application's migration runner or database management tools. Check the main application documentation for specific migration commands.

## Best Practices

- Always backup database before running migrations
- Test migrations on development environment first
- Include rollback scripts when possible
- Keep migrations atomic and focused on single changes
- Document complex schema changes with comments