create table leaderboard_scheme
(
	id serial not null
		constraint leaderboard_scheme_pk
			primary key,
	name varchar(255) not null,
	created_at timestamp,
	disabled boolean
);

create table leaderboard_region
(
	id serial not null
		constraint leaderboard_region_pk
			primary key,
	name varchar(255) not null,
	leaderboard_scheme_id integer not null
		constraint leaderboard_region_leaderboard_scheme_id_fk
			references leaderboard_scheme,
    created_at timestamp
);

create table leaderboard_user
(
	id serial not null
		constraint leaderboard_user_pk
			primary key,
	name varchar(512) not null,
	leaderboard_region_id integer not null
		constraint leaderboard_user_leaderboard_region_id_fk
			references leaderboard_region,
	created_at timestamp,
	points integer
);


alter table clients
	add leaderboard_scheme_id int;

alter table clients
	add constraint clients_leaderboard_scheme_id_fk
		foreign key (leaderboard_scheme_id) references leaderboard_scheme;
