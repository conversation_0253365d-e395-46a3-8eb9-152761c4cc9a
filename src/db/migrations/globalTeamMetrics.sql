CREATE TABLE lumen_global_team_metrics(
  id                  SERIAL          NOT NULL,
  name                <PERSON><PERSON><PERSON><PERSON>( 255  ) NOT NULL,
  alias               VA<PERSON>HAR( 255  ) NOT NULL,
  default_value       INT NOT NULL DEFAULT 0,
  value               INT NOT NULL DEFAULT 0,
  team_id             INT NOT NULL,
  created_at          TIMESTAMP,
  CONSTRAINT global_team_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT global_team_metrics_team_id FOREIGN KEY (team_id)
     REFERENCES lumen_team (team_id) MATCH SIMPLE
     ON UPDATE NO ACTION ON DELETE NO ACTION
);

ALTER TABLE lumen_challenge
ADD challenge_option_metric1_a INT DEFAULT 0,
ADD challenge_option_metric2_a INT DEFAULT 0,
ADD challenge_option_metric3_a INT DEFAULT 0,
ADD challenge_option_metric1_b INT DEFAULT 0,
ADD challenge_option_metric2_b INT DEFAULT 0,
ADD challenge_option_metric3_b INT DEFAULT 0,
ADD challenge_option_metric1_c INT DEFAULT 0,
ADD challenge_option_metric2_c INT DEFAULT 0,
ADD challenge_option_metric3_c INT DEFAULT 0;

ALTER TABLE lumen_initiative
ADD metric1 INT DEFAULT 0,
ADD metric2 INT DEFAULT 0,
ADD metric3 INT DEFAULT 0;
