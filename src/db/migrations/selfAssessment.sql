CREATE TABLE
	public.self_assessment_scheme (
		id serial NOT NULL,
		name varchar(255) NOT NULL,
		disabled BOOLEAN DEFAULT FALSE,
		created_at timestamp,
		updated_at timestamp,
		CONSTRAINT self_assessment_scheme_id PRIMARY KEY (id)
	);

CREATE TABLE
	public.self_assessment_pdf_pages_config (
		id serial NOT NULL,
		cover_image varchar(255),
		page_2_quad_1_pdf varchar(255),
		page_2_quad_2_pdf varchar(255),
		page_2_quad_3_pdf varchar(255),
		page_2_quad_4_pdf varchar(255),
		page_3_quad_1_pdf varchar(255),
		page_3_quad_2_pdf varchar(255),
		page_3_quad_3_pdf varchar(255),
		page_3_quad_4_pdf varchar(255),
		scheme_id integer NOT NULL,
		created_at timestamp,
		updated_at timestamp,
		CONSTRAINT self_assessment_report_config_id PRIMARY KEY (id),
		CONSTRAINT self_assessment_report_config_scheme_id_unique UNIQUE (scheme_id)
	);

CREATE TABLE
	public.self_assessment_pdf_paragraphs_config (
		id serial NOT NULL,
		normal_paragraphs jsonb,
		stress_paragraphs jsonb,
		scheme_id integer NOT NULL,
		created_at timestamp,
		updated_at timestamp,
		CONSTRAINT self_assessment_pdf_paragraphs_config_id PRIMARY KEY (id),
		CONSTRAINT self_assessment_pdf_paragraphs_config_scheme_id UNIQUE (scheme_id)
	);

CREATE TABLE
	public.self_assessment_quadrants_config (
		id serial NOT NULL,
		quadrant_1_name varchar(255),
		quadrant_2_name varchar(255),
		quadrant_3_name varchar(255),
		quadrant_4_name varchar(255),
		normal_quadrant_1_questions jsonb,
		normal_quadrant_2_questions jsonb,
		normal_quadrant_3_questions jsonb,
		normal_quadrant_4_questions jsonb,
		stress_quadrant_1_questions jsonb,
		stress_quadrant_2_questions jsonb,
		stress_quadrant_3_questions jsonb,
		stress_quadrant_4_questions jsonb,
		scheme_id integer NOT NULL,
		created_at timestamp,
		updated_at timestamp,
		CONSTRAINT self_assessment_quadrants_config_id PRIMARY KEY (id),
		CONSTRAINT self_assessment_quadrants_config_scheme_id_unique UNIQUE (scheme_id)
	);

CREATE TABLE
	IF NOT EXISTS public.self_assessment_answers (
		id serial NOT NULL,
		normal_quadrant_1_questions numeric(3, 2) NOT NULL,
		normal_quadrant_2_questions numeric(3, 2) NOT NULL,
		normal_quadrant_3_questions numeric(3, 2) NOT NULL,
		normal_quadrant_4_questions numeric(3, 2) NOT NULL,
		stress_quadrant_1_questions numeric(3, 2) NOT NULL,
		stress_quadrant_2_questions numeric(3, 2) NOT NULL,
		stress_quadrant_3_questions numeric(3, 2) NOT NULL,
		stress_quadrant_4_questions numeric(3, 2) NOT NULL,
		created_at timestamp without time zone NOT NULL,
		client_id integer NOT NULL,
		team_id integer NOT NULL,
		scheme_id integer NOT NULL,
		CONSTRAINT self_assessment_answers_pk PRIMARY KEY (id)
	);

ALTER TABLE public.clients ADD self_assessment_tab_name VARCHAR(255) DEFAULT 'Self Assessment',
ADD self_assessment_tab_visibility BOOLEAN DEFAULT FALSE,
ADD self_assessment_scheme_id integer;

ALTER TABLE IF EXISTS public.clients ADD CONSTRAINT clients_self_assessment_scheme_id_fk FOREIGN KEY (self_assessment_scheme_id) REFERENCES self_assessment_scheme (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION NOT VALID;

ALTER TABLE IF EXISTS public.self_assessment_pdf_pages_config ADD CONSTRAINT self_assessment_pdf_pages_config_scheme_id_fk FOREIGN KEY (scheme_id) REFERENCES self_assessment_scheme (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION NOT VALID;

ALTER TABLE IF EXISTS public.self_assessment_pdf_paragraphs_config ADD CONSTRAINT self_assessment_pdf_paragraphs_config_scheme_id_fk FOREIGN KEY (scheme_id) REFERENCES self_assessment_scheme (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION NOT VALID;

ALTER TABLE IF EXISTS public.self_assessment_quadrants_config ADD CONSTRAINT self_assessment_quadrants_config_scheme_id_fk FOREIGN KEY (scheme_id) REFERENCES self_assessment_scheme (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION NOT VALID;

ALTER TABLE IF EXISTS public.self_assessment_answers ADD CONSTRAINT self_assessment_answers_clients_fk FOREIGN KEY (client_id) REFERENCES public.clients (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION NOT VALID;

ALTER TABLE IF EXISTS public.self_assessment_answers ADD CONSTRAINT self_assessment_answers_teams_fk FOREIGN KEY (team_id) REFERENCES public.lumen_team (team_id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION NOT VALID;

ALTER TABLE IF EXISTS public.self_assessment_answers ADD CONSTRAINT self_assessment_answers_self_assessment_scheme_fk FOREIGN KEY (scheme_id) REFERENCES public.self_assessment_scheme (id) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION NOT VALID;

alter table self_assessment_answers add raw_answers jsonb default '{}';