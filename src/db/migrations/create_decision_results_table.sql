-- Create decision_results table
CREATE TABLE decision_results
(
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES lumen_team(team_id),
    decision_scheme_id INTEGER NOT NULL REFERENCES lumen_decision_scheme(scheme_id),
    client_id INTEGER NOT NULL REFERENCES clients(id),
    selected_values JSONB NOT NULL,
    total_fte NUMERIC NOT NULL,
    total_investment NUMERIC NOT NULL,
    ai_analysis JSONB,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Add indexes for foreign keys
CREATE INDEX idx_decision_results_user_id ON decision_results(user_id);
CREATE INDEX idx_decision_results_decision_scheme_id ON decision_results(decision_scheme_id);
CREATE INDEX idx_decision_results_client_id ON decision_results(client_id);

