CREATE TABLE welcome_page
(
	id serial not null
		constraint welcome_page_pk
			primary key,
	name varchar(255) not null,
	text       TEXT NOT NULL,
	image      VARCHAR( 4096 ),
	created_at timestamp,
	disabled boolean
);

ALTER TABLE clients
ADD home_tab_name <PERSON><PERSON><PERSON><PERSON>( 255  ) DEFAULT 'Welcome Page',
ADD home_tab_visibility BOOLEAN DEFAULT TRUE,
ADD home_scheme_id INT;

ALTER TABLE clients
ADD CONSTRAINT clients_welcome_page_id_fk
FOREIGN KEY (home_scheme_id) references welcome_page;

