CREATE TABLE clients(
  id                  SERIAL          NOT NULL CONSTRAINT clients_pkey PRIMARY KEY,
  name                  <PERSON><PERSON><PERSON><PERSON>( 255  ) NOT NULL,
  background_image      VARCHAR( 1000 ) NOT NULL,
  logo_image            VARCHAR( 1000 ) NOT NULL,
  challenges_tab_name   <PERSON><PERSON><PERSON><PERSON>( 255  ) NOT NULL,
  goals_tab_name        VA<PERSON>HAR( 255  ) NOT NULL,
  strategic_tab_name    VARCHAR( 255  ) NOT NULL,
  dark_highlight_color  VARCHAR( 255  ) NOT NULL,
  light_highlight_color VARCHAR( 255  ) NOT NULL,
  created_at          TIMESTAMP,
  disabled            BOOLEAN                  DEFAULT FALSE
);

ALTER TABLE lumen_team ADD client_id INTEGER;

alter table clients alter column background_image type text using background_image::text;
alter table clients alter column logo_image type text using logo_image::text;
