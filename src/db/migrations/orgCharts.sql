create table org_chart_scheme
(
	id serial not null
		constraint org_chart_scheme_pk
			primary key,
	name varchar(255) not null,
	created_at timestamp,
	disabled boolean
);

create table org_chart
(
	id serial not null
		constraint org_charts_pk
			primary key,
	org_chart_scheme_id integer
		constraint org_charts_org_chart_scheme_id_fk
			references org_chart_scheme,
	created_at timestamp
);

create table org_chart_type
(
	id serial not null
		constraint org_chart_type_pk
			primary key,
	name varchar(255) not null,
	type varchar(255) not null,
	org_chart_id integer not null
		constraint org_chart_type_org_chart_id_fk
			references org_chart,
	created_at timestamp
);

create table org_chart_user
(
	id serial not null
		constraint org_chart_user_pk
			primary key,
	name varchar(512) not null,
	title varchar(255) not null,
	status integer not null,
	photo varchar(1024),
	meet_1_text varchar(1024),
	meet_1_points integer,
	meet_2_text varchar(1024),
	meet_2_points integer,
	meet_3_text varchar(1024),
	meet_3_points integer,
	parent_id integer,
	locked_by_id integer,
	created_at timestamp,
	org_chart_type_id integer
		constraint org_chart_user_org_chart_type_id_fk
			references org_chart_type
);

create table team_selected_org_chart
(
	id serial not null
		constraint team_selected_org_chart_pk
			primary key,
	meet_1 boolean,
	meet_2 boolean,
	meet_3 boolean,
	team_id integer
		constraint team_selected_org_chart_lumen_team_team_id_fk
			references lumen_team,
	org_chart_user_id integer
		constraint team_selected_org_chart_org_chart_user_id_fk
			references org_chart_user
				on update cascade on delete cascade,
	created_at timestamp
);

alter table clients
	add org_chart_scheme_id int;

alter table clients
	add constraint clients_org_chart_scheme_id_fk
		foreign key (org_chart_scheme_id) references org_chart_scheme;

create unique index lumen_team_team_email_uindex
	on lumen_team (team_email);
