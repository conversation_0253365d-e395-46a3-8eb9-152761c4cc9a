-- Migration: Add AI Prompt Logs table
-- This table tracks all AI API calls for debugging, analysis, and optimization

CREATE TABLE IF NOT EXISTS ai_prompt_logs (
    id SERIAL PRIMARY KEY,
    final_system_prompt TEXT NOT NULL,
    final_question_prompt TEXT NOT NULL,
    tokens_used INTEGER,
    input_sources JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP
);

-- Add indexes for common queries
CREATE INDEX IF NOT EXISTS idx_ai_prompt_logs_created_at ON ai_prompt_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_prompt_logs_tokens_used ON ai_prompt_logs(tokens_used);

-- Add comment for documentation
COMMENT ON TABLE ai_prompt_logs IS 'Tracks all AI API calls including prompts, responses, and metadata for debugging and analysis';
COMMENT ON COLUMN ai_prompt_logs.final_system_prompt IS 'The complete system prompt sent to the AI';
COMMENT ON COLUMN ai_prompt_logs.final_question_prompt IS 'The complete user prompt sent to the AI';
COMMENT ON COLUMN ai_prompt_logs.tokens_used IS 'Total tokens consumed by this API call';
COMMENT ON COLUMN ai_prompt_logs.input_sources IS 'Array of data sources used to generate the prompt (scheme_id, page_id, user_selections, etc.)';
