import { apiClient } from '../lib/index.js';
import {
  findDecisionGroupById,
  createDecisionGroup,
  deleteDecisionGroup,
  listAllDecisionGroups,
} from '../db/queries/decisionGroups.js';

const DECISION_GROUPS_CONTROLLER = {
  get: async (req, res, next) => {
    try {
      const group = await findDecisionGroupById(req.params.id);

      if (!group) {
        return apiClient.notFound(req, res, next, 'Decision Group not found');
      }

      return apiClient.success(req, res, next, group);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  create: async (req, res, next) => {
    const { name } = req.body;

    try {
      const group = await createDecisionGroup({ name });

      return apiClient.success(req, res, next, group);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  delete: async (req, res, next) => {
    const { id } = req.params;

    try {
      const group = await deleteDecisionGroup(id);

      if (!group) {
        return apiClient.notFound(req, res, next, 'Decision Group not found');
      }

      return apiClient.success(req, res, next, group);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  listAll: async (req, res, next) => {
    try {
      const groups = await listAllDecisionGroups();

      return apiClient.success(req, res, next, groups);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },
};

export default DECISION_GROUPS_CONTROLLER;
