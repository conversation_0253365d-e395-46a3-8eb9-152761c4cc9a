import { apiClient } from '../lib/index.js';
import {
  createSelfAssessmentScheme,
  createSelfAssessmentQuadrantsConfig,
  createSelfAssessmentPdfPagesConfig,
  createSelfAssessmentPdfParagraphsConfig,
  listSelfAssessmentSchemes,
  listAllSelfAssessmentSchemes,
  toggleSelfAssessmentSchemeDisabled,
} from '../db/queries/selfAssessment.js';
import { exportSelfAssessmentPdf } from '../lib/pdf/export_self_assessment_pdf.js';
import config from '../../config.json' with { type: 'json' };
import sgMail from '@sendgrid/mail';
sgMail.setApiKey(config.SENDGRID_API_KEY);
import { db } from '../db/index.js';
import {
  clients,
  lumenTeam,
  selfAssessmentScheme,
  selfAssessmentQuadrantsConfig,
  selfAssessmentAnswers,
  selfAssessmentPdfPagesConfig,
  selfAssessmentPdfParagraphsConfig,
} from '../db/schema.js';
import { eq, and, sql } from 'drizzle-orm';

const SELF_ASSESSMENT_CONTROLLER = {
  getSelfAssesmentsQuestions: async (req, res, next) => {
    const { clientId } = req.query;

    try {
      // Use findClientById but handle the null case explicitly
      const client = await db
        .select({
          id: clients.id,
          selfAssessmentSchemeId: clients.self_assessment_scheme_id,
        })
        .from(clients)
        .where(eq(clients.id, clientId))
        .limit(1);

      if (!client?.length) {
        return apiClient.notFound(req, res, next, 'Client not found');
      }

      const clientData = client[0];

      // Get scheme data
      const schemeData = await db
        .select({
          id: selfAssessmentScheme.id,
          name: selfAssessmentScheme.name,
        })
        .from(selfAssessmentScheme)
        .where(eq(selfAssessmentScheme.id, clientData.selfAssessmentSchemeId))
        .limit(1);

      if (!schemeData?.length) {
        return apiClient.notFound(
          req,
          res,
          next,
          'Self Assessment Scheme not found'
        );
      }

      // Get questions config
      const questionsConfig = await db
        .select({
          normalQuadrant1Questions:
            selfAssessmentQuadrantsConfig.normal_quadrant_1_questions,
          normalQuadrant2Questions:
            selfAssessmentQuadrantsConfig.normal_quadrant_2_questions,
          normalQuadrant3Questions:
            selfAssessmentQuadrantsConfig.normal_quadrant_3_questions,
          normalQuadrant4Questions:
            selfAssessmentQuadrantsConfig.normal_quadrant_4_questions,
          stressQuadrant1Questions:
            selfAssessmentQuadrantsConfig.stress_quadrant_1_questions,
          stressQuadrant2Questions:
            selfAssessmentQuadrantsConfig.stress_quadrant_2_questions,
          stressQuadrant3Questions:
            selfAssessmentQuadrantsConfig.stress_quadrant_3_questions,
          stressQuadrant4Questions:
            selfAssessmentQuadrantsConfig.stress_quadrant_4_questions,
        })
        .from(selfAssessmentQuadrantsConfig)
        .where(eq(selfAssessmentQuadrantsConfig.scheme_id, schemeData[0].id))
        .orderBy(selfAssessmentQuadrantsConfig.id, 'desc')
        .limit(1);

      if (!questionsConfig?.length) {
        return apiClient.notFound(
          req,
          res,
          next,
          'Self Assessment Questions not found'
        );
      }

      // Transform to match original response format
      const result = {
        normal_quadrant_1_questions:
          questionsConfig[0].normalQuadrant1Questions,
        normal_quadrant_2_questions:
          questionsConfig[0].normalQuadrant2Questions,
        normal_quadrant_3_questions:
          questionsConfig[0].normalQuadrant3Questions,
        normal_quadrant_4_questions:
          questionsConfig[0].normalQuadrant4Questions,
        stress_quadrant_1_questions:
          questionsConfig[0].stressQuadrant1Questions,
        stress_quadrant_2_questions:
          questionsConfig[0].stressQuadrant2Questions,
        stress_quadrant_3_questions:
          questionsConfig[0].stressQuadrant3Questions,
        stress_quadrant_4_questions:
          questionsConfig[0].stressQuadrant4Questions,
      };

      return apiClient.success(req, res, next, result);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  get: async (req, res, next) => {
    const { id } = req.params;

    try {
      // First get the scheme
      const schemeData = await db
        .select({
          scheme_id: selfAssessmentScheme.id,
          name: selfAssessmentScheme.name,
          created_at: selfAssessmentScheme.created_at,
          updated_at: selfAssessmentScheme.updated_at,
        })
        .from(selfAssessmentScheme)
        .where(eq(selfAssessmentScheme.id, id))
        .limit(1);

      if (!schemeData.length) {
        return apiClient.notFound(
          req,
          res,
          next,
          'Self Assessment Scheme not found'
        );
      }

      // Get all related configs
      const [quadrantsConfig, pdfPagesConfig, pdfParagraphsConfig] =
        await Promise.all([
          db
            .select({
              quadrants_config_id: selfAssessmentQuadrantsConfig.id,
              quadrant_1_name: selfAssessmentQuadrantsConfig.quadrant_1_name,
              quadrant_2_name: selfAssessmentQuadrantsConfig.quadrant_2_name,
              quadrant_3_name: selfAssessmentQuadrantsConfig.quadrant_3_name,
              quadrant_4_name: selfAssessmentQuadrantsConfig.quadrant_4_name,
              normal_quadrant_1_questions:
                selfAssessmentQuadrantsConfig.normal_quadrant_1_questions,
              normal_quadrant_2_questions:
                selfAssessmentQuadrantsConfig.normal_quadrant_2_questions,
              normal_quadrant_3_questions:
                selfAssessmentQuadrantsConfig.normal_quadrant_3_questions,
              normal_quadrant_4_questions:
                selfAssessmentQuadrantsConfig.normal_quadrant_4_questions,
              stress_quadrant_1_questions:
                selfAssessmentQuadrantsConfig.stress_quadrant_1_questions,
              stress_quadrant_2_questions:
                selfAssessmentQuadrantsConfig.stress_quadrant_2_questions,
              stress_quadrant_3_questions:
                selfAssessmentQuadrantsConfig.stress_quadrant_3_questions,
              stress_quadrant_4_questions:
                selfAssessmentQuadrantsConfig.stress_quadrant_4_questions,
            })
            .from(selfAssessmentQuadrantsConfig)
            .where(eq(selfAssessmentQuadrantsConfig.scheme_id, id))
            .orderBy(selfAssessmentQuadrantsConfig.id, 'desc')
            .limit(1),

          db
            .select({
              pdf_pages_config_id: selfAssessmentPdfPagesConfig.id,
              cover_image: selfAssessmentPdfPagesConfig.cover_image,
              page_2_quad_1_pdf: selfAssessmentPdfPagesConfig.page_2_quad_1_pdf,
              page_2_quad_2_pdf: selfAssessmentPdfPagesConfig.page_2_quad_2_pdf,
              page_2_quad_3_pdf: selfAssessmentPdfPagesConfig.page_2_quad_3_pdf,
              page_2_quad_4_pdf: selfAssessmentPdfPagesConfig.page_2_quad_4_pdf,
              page_3_quad_1_pdf: selfAssessmentPdfPagesConfig.page_3_quad_1_pdf,
              page_3_quad_2_pdf: selfAssessmentPdfPagesConfig.page_3_quad_2_pdf,
              page_3_quad_3_pdf: selfAssessmentPdfPagesConfig.page_3_quad_3_pdf,
              page_3_quad_4_pdf: selfAssessmentPdfPagesConfig.page_3_quad_4_pdf,
            })
            .from(selfAssessmentPdfPagesConfig)
            .where(eq(selfAssessmentPdfPagesConfig.scheme_id, id))
            .orderBy(selfAssessmentPdfPagesConfig.id, 'desc')
            .limit(1),

          db
            .select({
              pdf_paragraphs_config_id: selfAssessmentPdfParagraphsConfig.id,
              normal_paragraphs:
                selfAssessmentPdfParagraphsConfig.normal_paragraphs,
              stress_paragraphs:
                selfAssessmentPdfParagraphsConfig.stress_paragraphs,
            })
            .from(selfAssessmentPdfParagraphsConfig)
            .where(eq(selfAssessmentPdfParagraphsConfig.scheme_id, id))
            .orderBy(selfAssessmentPdfParagraphsConfig.id, 'desc')
            .limit(1),
        ]);

      // Check if we have all required data
      if (
        !quadrantsConfig.length ||
        !pdfPagesConfig.length ||
        !pdfParagraphsConfig.length
      ) {
        return apiClient.notFound(req, res, next, 'Missing configuration data');
      }

      // Combine all the data
      const result = {
        ...schemeData[0],
        ...quadrantsConfig[0],
        ...pdfPagesConfig[0],
        ...pdfParagraphsConfig[0],
      };

      return apiClient.success(req, res, next, result);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  create: async (req, res, next) => {
    const {
      name,
      description,
      quadrantsConfig,
      pdfPagesConfig,
      pdfParagraphsConfig,
    } = req.body;

    console.log('quadrantsConfig', quadrantsConfig, req.query, req.body, req.params);

    try {
      const createdScheme = await db.transaction(async (tx) => {
        const scheme = await createSelfAssessmentScheme({ name, description });

        await createSelfAssessmentQuadrantsConfig({
          schemeId: scheme.id,
          ...quadrantsConfig,
        });

        await createSelfAssessmentPdfPagesConfig({
          schemeId: scheme.id,
          ...pdfPagesConfig,
        });

        await createSelfAssessmentPdfParagraphsConfig({
          schemeId: scheme.id,
          ...pdfParagraphsConfig,
        });

        return scheme;
      });

      const result = {
        id: createdScheme.id,
        name: createdScheme.name,
        description: createdScheme.description,
      };

      return apiClient.success(req, res, next, result);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  createAnswers: async (req, res, next) => {
    const {
      normal_quadrant_1_questions,
      normal_quadrant_2_questions,
      normal_quadrant_3_questions,
      normal_quadrant_4_questions,
      stress_quadrant_1_questions,
      stress_quadrant_2_questions,
      stress_quadrant_3_questions,
      stress_quadrant_4_questions,
      raw_answers,
    } = req.body;

    const clientId = req.user.clientId || req.query.clientId || req.body.clientId;
    const teamId = req.user.id;

    console.log('createAnswers', req.query, req.body, req.params);

    try {
      // Get client and team data in parallel
      const [clientData, teamData] = await Promise.all([
        db
          .select({
            id: clients.id,
            logoImage: clients.logo_image,
            selfAssessmentSchemeId: clients.self_assessment_scheme_id,
          })
          .from(clients)
          .where(eq(clients.id, clientId))
          .limit(1),
        db
          .select({
            teamId: lumenTeam.team_id,
            teamName: lumenTeam.team_name,
            teamEmail: lumenTeam.team_email,
          })
          .from(lumenTeam)
          .where(eq(lumenTeam.team_id, teamId))
          .limit(1),
      ]);

      if (!clientData?.length) {
        return apiClient.notFound(req, res, next, 'Client not found');
      }

      const client = clientData[0];

      // Get self assessment scheme
      const schemeData = await db
        .select({
          id: selfAssessmentScheme.id,
        })
        .from(selfAssessmentScheme)
        .where(eq(selfAssessmentScheme.id, client.selfAssessmentSchemeId))
        .limit(1);

      if (!schemeData?.length) {
        return apiClient.notFound(
          req,
          res,
          next,
          'Self Assessment Scheme not found'
        );
      }

      // Create answers with properly formatted data
      const createdAnswers = await db
        .insert(selfAssessmentAnswers)
        .values({
          client_id: Number(clientId),
          team_id: teamId,
          scheme_id: schemeData[0].id,
          normal_quadrant_1_questions: parseFloat(
            normal_quadrant_1_questions.toFixed(2)
          ),
          normal_quadrant_2_questions: parseFloat(
            normal_quadrant_2_questions.toFixed(2)
          ),
          normal_quadrant_3_questions: parseFloat(
            normal_quadrant_3_questions.toFixed(2)
          ),
          normal_quadrant_4_questions: parseFloat(
            normal_quadrant_4_questions.toFixed(2)
          ),
          stress_quadrant_1_questions: parseFloat(
            stress_quadrant_1_questions.toFixed(2)
          ),
          stress_quadrant_2_questions: parseFloat(
            stress_quadrant_2_questions.toFixed(2)
          ),
          stress_quadrant_3_questions: parseFloat(
            stress_quadrant_3_questions.toFixed(2)
          ),
          stress_quadrant_4_questions: parseFloat(
            stress_quadrant_4_questions.toFixed(2)
          ),
          raw_answers: JSON.stringify(raw_answers),
          created_at: new Date(),
        })
        .returning();

      if (!createdAnswers?.length) {
        throw new Error('Failed to create self assessment answers');
      }

      // Send email notification
      const msg = {
        to: teamData[0].teamEmail,
        from: config.SENDGRID_SENDER,
        subject: 'Download Your Self Assessment Report',
        dynamic_template_data: {
          subject: 'Download Your Self Assessment Report',
          client: {
            logo: client.logoImage,
          },
          name: teamData[0].teamName,
          link: `${config.apiUrl}/user/self-assessment-pdf?clientId=${clientId}&teamId=${teamData[0].teamId}`,
        },
        templateId: 'd-1a8476d80e054a329e72fd7292245145',
      };

      try {
        await sgMail.send(msg);
      } catch (error) {
        throw new Error('Something went wrong while sending the email');
      }

      return apiClient.success(req, res, next, {
        id: createdAnswers[0].id,
      });
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  update: async (req, res, next) => {
    const {
      scheme_id,
      name,
      quadrant_1_name,
      quadrant_2_name,
      quadrant_3_name,
      quadrant_4_name,
      normal_quadrant_1_questions,
      normal_quadrant_2_questions,
      normal_quadrant_3_questions,
      normal_quadrant_4_questions,
      stress_quadrant_1_questions,
      stress_quadrant_2_questions,
      stress_quadrant_3_questions,
      stress_quadrant_4_questions,
      cover_image,
      page_2_quad_1_pdf,
      page_2_quad_2_pdf,
      page_2_quad_3_pdf,
      page_2_quad_4_pdf,
      page_3_quad_1_pdf,
      page_3_quad_2_pdf,
      page_3_quad_3_pdf,
      page_3_quad_4_pdf,
      normal_paragraphs,
      stress_paragraphs,
    } = req.body;

    try {
      const updatedScheme = await db.transaction(async (tx) => {
        // First check if scheme exists
        const existingScheme = await tx
          .select()
          .from(selfAssessmentScheme)
          .where(eq(selfAssessmentScheme.id, scheme_id))
          .limit(1);

        if (!existingScheme.length) {
          throw new Error('Self Assessment Scheme not found');
        }

        // Update all configurations in parallel
        await Promise.all([
          // Update scheme
          tx
            .update(selfAssessmentScheme)
            .set({
              name,
              updated_at: sql`current_timestamp`,
            })
            .where(eq(selfAssessmentScheme.id, scheme_id)),

          // Update quadrants config
          tx
            .update(selfAssessmentQuadrantsConfig)
            .set({
              quadrant_1_name,
              quadrant_2_name,
              quadrant_3_name,
              quadrant_4_name,
              normal_quadrant_1_questions,
              normal_quadrant_2_questions,
              normal_quadrant_3_questions,
              normal_quadrant_4_questions,
              stress_quadrant_1_questions,
              stress_quadrant_2_questions,
              stress_quadrant_3_questions,
              stress_quadrant_4_questions,
              updated_at: sql`current_timestamp`,
            })
            .where(eq(selfAssessmentQuadrantsConfig.scheme_id, scheme_id)),

          // Update PDF pages config
          tx
            .update(selfAssessmentPdfPagesConfig)
            .set({
              cover_image,
              page_2_quad_1_pdf,
              page_2_quad_2_pdf,
              page_2_quad_3_pdf,
              page_2_quad_4_pdf,
              page_3_quad_1_pdf,
              page_3_quad_2_pdf,
              page_3_quad_3_pdf,
              page_3_quad_4_pdf,
              updated_at: sql`current_timestamp`,
            })
            .where(eq(selfAssessmentPdfPagesConfig.scheme_id, scheme_id)),

          // Update PDF paragraphs config
          tx
            .update(selfAssessmentPdfParagraphsConfig)
            .set({
              normal_paragraphs,
              stress_paragraphs,
              updated_at: sql`current_timestamp`,
            })
            .where(eq(selfAssessmentPdfParagraphsConfig.scheme_id, scheme_id)),
        ]);

        return {
          id: scheme_id,
        };
      });

      return apiClient.success(req, res, next, updatedScheme);
    } catch (e) {
      console.error(e);
      if (e.message === 'Self Assessment Scheme not found') {
        return apiClient.notFound(req, res, next, e.message);
      }
      return apiClient.serverError(req, res, next);
    }
  },

  list: async (req, res, next) => {
    const {
      sort = 'name',
      ascending = 'true',
      showDisabled = 'false',
      query = '',
      offset = 0,
      limit = 10,
    } = req.query;

    try {
      const schemes = await listSelfAssessmentSchemes({
        sort,
        ascending,
        showDisabled,
        query,
        offset: parseInt(offset, 10),
        limit: parseInt(limit, 10),
      });

      return apiClient.success(req, res, next, schemes);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  listAll: async (req, res, next) => {
    try {
      const schemes = await listAllSelfAssessmentSchemes();

      return apiClient.success(req, res, next, schemes);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  toggleDisabled: async (req, res, next) => {
    const { id } = req.params;

    try {
      const scheme = await toggleSelfAssessmentSchemeDisabled(id);

      return apiClient.success(req, res, next, scheme);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  exportPDF: async (request, response, next) => {
    const { clientId, teamId } = request.query;

    try {
      // Get answers first
      const answersData = await db
        .select()
        .from(selfAssessmentAnswers)
        .where(
          and(
            eq(selfAssessmentAnswers.team_id, teamId),
            eq(selfAssessmentAnswers.client_id, clientId)
          )
        )
        .limit(1);

      if (!answersData?.length) {
        return apiClient.serverError(
          request,
          response,
          next,
          'No answers found'
        );
      }

      const answers = answersData[0];
      const schemeId = answers.scheme_id;

      // Get all required data in parallel
      const [
        pdfPagesConfig,
        clientData,
        teamData,
        paragraphsConfig,
        schemeData,
        quadrantsConfig,
      ] = await Promise.all([
        db
          .select()
          .from(selfAssessmentPdfPagesConfig)
          .where(eq(selfAssessmentPdfPagesConfig.scheme_id, schemeId))
          .orderBy(selfAssessmentPdfPagesConfig.id, 'desc')
          .limit(1),
        db
          .select()
          .from(clients)
          .where(eq(clients.id, answers.client_id))
          .limit(1),
        db
          .select()
          .from(lumenTeam)
          .where(eq(lumenTeam.team_id, answers.team_id))
          .limit(1),
        db
          .select()
          .from(selfAssessmentPdfParagraphsConfig)
          .where(eq(selfAssessmentPdfParagraphsConfig.scheme_id, schemeId))
          .orderBy(selfAssessmentPdfParagraphsConfig.id, 'desc')
          .limit(1),
        db
          .select()
          .from(selfAssessmentScheme)
          .where(eq(selfAssessmentScheme.id, schemeId))
          .orderBy(selfAssessmentScheme.id, 'desc')
          .limit(1),
        db
          .select()
          .from(selfAssessmentQuadrantsConfig)
          .where(eq(selfAssessmentQuadrantsConfig.scheme_id, schemeId))
          .orderBy(selfAssessmentQuadrantsConfig.id, 'desc')
          .limit(1),
      ]);

      // Check if we have all required data
      if (
        !pdfPagesConfig?.length ||
        !clientData?.length ||
        !teamData?.length ||
        !paragraphsConfig?.length ||
        !schemeData?.length ||
        !quadrantsConfig?.length
      ) {
        throw new Error('Missing required configuration data');
      }

      try {
        const pdfFile = await exportSelfAssessmentPdf({
          answers,
          pdfConfig: pdfPagesConfig[0],
          client: clientData[0],
          paragraphsConfig: paragraphsConfig[0],
          team: teamData[0],
          selfAssessmentName: schemeData[0].name,
          quadrantsConfig: quadrantsConfig[0],
        });

        response.setHeader('Content-Type', 'application/pdf');
        response.setHeader(
          'Content-Disposition',
          'attachment; filename=Your Self Assessment'
        );

        return response.end(pdfFile, 'binary');
      } catch (error) {
        console.error(error);
        return apiClient.serverError(request, response, next, error.message);
      }
    } catch (error) {
      console.error(error);
      return apiClient.serverError(request, response, next, error.message);
    }
  },
};

export default SELF_ASSESSMENT_CONTROLLER;
