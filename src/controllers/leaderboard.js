import { apiClient } from '../lib/index.js';
import { db } from '../db/index.js';
import {
  findLeaderboardSchemeById,
  findLeaderboardRegionsBySchemeId,
  findLeaderboardUsersByRegionIds,
  createLeaderboardScheme,
  createLeaderboardRegion,
  createLeaderboardUser,
  listAllLeaderboardSchemes,
  toggleLeaderboardSchemeDisabled,
} from '../db/queries/leaderboard.js';
import {
  leaderboardScheme,
  leaderboardRegion,
  leaderboardUser,
} from '../db/schema.js';
import { sql, eq, and, asc, desc, inArray } from 'drizzle-orm';

const LEADERBOARD_CONTROLLER = {
  get: async (req, res, next) => {
    const { id } = req.params;

    try {
      const scheme = await findLeaderboardSchemeById(id);

      if (!scheme) {
        return apiClient.serverError(req, res, next);
      }

      const regions = await findLeaderboardRegionsBySchemeId(id);
      const regionIds = regions.map((region) => region.id);
      const users = await findLeaderboardUsersByRegionIds(regionIds);

      for (const region of regions) {
        region.users = users.filter(
          (user) => user.leaderboardRegionId === region.id
        );
      }

      const result = {
        scheme,
        regions,
      };

      return apiClient.success(req, res, next, result);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  create: async (req, res, next) => {
    const { name, regions } = req.body;

    try {
      const createdScheme = await db.transaction(async (tx) => {
        const scheme = await createLeaderboardScheme(name);

        for (const region of regions) {
          const { name: regionName, users } = region;
          const createdRegion = await createLeaderboardRegion(
            regionName,
            scheme.id
          );

          for (const user of users) {
            const { name: userName, points } = user;
            await createLeaderboardUser(userName, points, createdRegion.id);
          }
        }

        return scheme;
      });

      return apiClient.success(req, res, next, createdScheme);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  update: async (req, res, next) => {
    try {
      // Transform the request data to match the schema exactly
      const { id, name, regions } = req.body;

      const transformedBody = {
        id: Number(id),
        name,
        disabled: false, // Add this as it's required by the schema
        regions: regions.map((region) => ({
          id: Number(region.id),
          name: region.name,
          leaderboard_scheme_id: Number(region.leaderboardSchemeId),
          created_at: new Date(region.createdAt),
          users: region.users.map((user) => ({
            id: Number(user.id),
            name: user.name,
            points: Number(user.points),
            leaderboard_region_id: Number(user.leaderboardRegionId),
            created_at: new Date(user.createdAt),
          })),
        })),
      };

      // Replace the request body with transformed data
      req.body = transformedBody;

      const updatedScheme = await db.transaction(async (tx) => {
        // Get the existing scheme to verify it exists
        const existingScheme = await tx
          .select()
          .from(leaderboardScheme)
          .where(eq(leaderboardScheme.id, transformedBody.id))
          .limit(1);

        if (!existingScheme.length) {
          throw new Error('Leaderboard scheme not found');
        }

        // Update leaderboard scheme name
        await tx
          .update(leaderboardScheme)
          .set({ name: transformedBody.name })
          .where(eq(leaderboardScheme.id, transformedBody.id));

        // Get existing regions
        const existingRegions = await tx
          .select()
          .from(leaderboardRegion)
          .where(
            eq(leaderboardRegion.leaderboard_scheme_id, transformedBody.id)
          );

        const existingRegionIds = existingRegions.map((region) => region.id);

        // Delete all users and regions
        if (existingRegionIds.length > 0) {
          await tx
            .delete(leaderboardUser)
            .where(
              inArray(leaderboardUser.leaderboard_region_id, existingRegionIds)
            );
        }

        await tx
          .delete(leaderboardRegion)
          .where(
            eq(leaderboardRegion.leaderboard_scheme_id, transformedBody.id)
          );

        // Insert new regions and users
        for (const region of transformedBody.regions) {
          const [createdRegion] = await tx
            .insert(leaderboardRegion)
            .values({
              name: region.name,
              leaderboard_scheme_id: transformedBody.id,
              created_at: sql`current_timestamp`,
            })
            .returning();

          // Insert users for this region
          if (Array.isArray(region.users)) {
            for (const user of region.users) {
              await tx.insert(leaderboardUser).values({
                name: user.name,
                points: user.points ?? 0,
                leaderboard_region_id: createdRegion.id,
                created_at: sql`current_timestamp`,
              });
            }
          }
        }

        return { id: transformedBody.id };
      });

      return apiClient.success(req, res, next, updatedScheme);
    } catch (e) {
      console.error(e);
      if (e.message === 'Leaderboard scheme not found') {
        return apiClient.notFound(req, res, next);
      }
      return apiClient.serverError(req, res, next);
    }
  },

  list: async (req, res, next) => {
    const {
      sort = 'name',
      ascending = 'true',
      showDisabled = 'false',
      query = '',
      offset = 0,
      limit = 10,
    } = req.query;

    try {
      const sortOrder = ascending === 'true' ? asc : desc;
      const showDisabledCondition = showDisabled === 'true';

      const schemes = await db
        .select({
          id: leaderboardScheme.id,
          name: leaderboardScheme.name,
          created_at: sql`TO_CHAR(${leaderboardScheme.created_at}, 'YYYY-MM-DD')`,
          disabled: leaderboardScheme.disabled,
          total: sql`COUNT(*) OVER()`,
        })
        .from(leaderboardScheme)
        .where(
          and(
            sql`LOWER(${leaderboardScheme.name}) LIKE ${
              '%' + query.toLowerCase() + '%'
            }`,
            eq(leaderboardScheme.disabled, showDisabledCondition)
          )
        )
        .orderBy(sortOrder(leaderboardScheme[sort]))
        .offset(parseInt(offset))
        .limit(parseInt(limit));

      return apiClient.success(req, res, next, schemes);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  listAll: async (req, res, next) => {
    try {
      const schemes = await listAllLeaderboardSchemes();

      return apiClient.success(req, res, next, schemes);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  toggleDisabled: async (req, res, next) => {
    const { id } = req.params;

    try {
      const scheme = await toggleLeaderboardSchemeDisabled(id);

      return apiClient.success(req, res, next, scheme);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },
};

export default LEADERBOARD_CONTROLLER;
