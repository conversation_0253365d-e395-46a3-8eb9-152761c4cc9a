import jwt from 'jsonwebtoken';
import { apiClient } from '../lib/index.js';
import config from '../../config.json' with { type: 'json' };
import { db } from '../db/index.js';
import { sql } from 'drizzle-orm';
import {
  findAdminByEmailAndPassword,
  findAdminById,
} from '../db/queries/admin.js';

const ADMIN_CONTROLLER = {
  signIn: async (req, res, next) => {
    try {
      // Get the hashed password using raw SQL to match exactly how it was done before
      const hashedPassword = sql`MD5(${req.body.password})`;

      const admin = await findAdminByEmailAndPassword(
        db,
        req.body.email,
        hashedPassword
      );

      // If no admin found or error, delay response by 1000ms exactly as before
      if (!admin || admin.length === 0) {
        return setTimeout(() => {
          return apiClient.invalidRequest(req, res, next);
        }, 1000);
      }

      const userObj = {
        id: admin.admin_id, // Match exact field names from original
        email: admin.admin_email,
      };

      const jwtToken = jwt.sign(userObj, config.jwtPassword, {
        expiresIn: '24h',
      });

      return apiClient.success(req, res, next, {
        token: jwtToken,
        user: userObj,
      });
    } catch (error) {
      console.error('Sign in error:', error);
      return apiClient.serverError(req, res, next);
    }
  },

  refreshToken: async (req, res, next) => {
    try {
      const { token } = req.body;

      // Use callback style to match original exactly
      jwt.verify(token, config.jwtPassword, async (err, decodedToken) => {
        console.log('decoding token: ', err, decodedToken); // Keep same logging

        if (err || !decodedToken) {
          return apiClient.invalidRequest(req, res, next);
        }

        try {
          const admin = await findAdminById(db, decodedToken.id);

          console.log('decodedToken:', decodedToken); // Keep same logging
          console.log('result: ', admin, null); // Match original logging pattern

          if (!admin) {
            return apiClient.invalidRequest(req, res, next);
          }

          const userObj = {
            id: admin.admin_id, // Match exact field names from original
            email: admin.admin_email,
          };

          const jwtToken = jwt.sign(userObj, config.jwtPassword, {
            expiresIn: '24h',
          });

          return apiClient.success(req, res, next, {
            token: jwtToken,
            user: userObj,
          });
        } catch (err) {
          console.error(err);
          return apiClient.invalidRequest(req, res, next);
        }
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      return apiClient.invalidRequest(req, res, next);
    }
  },
};

export default ADMIN_CONTROLLER;
