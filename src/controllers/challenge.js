import { apiClient } from '../lib/index.js';
import { db } from '../db/index.js';
import { sql, eq, and, asc } from 'drizzle-orm';
import { lumenChallengeScheme, lumenChallenge, lumenTeam } from '../db/schema.js';

const CHALLENGE_CONTROLLER = {
  get: async (req, res, next) => {
    const that = {};

    try {
      // Get the scheme using Drizzle
      const schemes = await db
        .select({
          id: lumenChallengeScheme.scheme_id,
          name: lumenChallengeScheme.scheme_name,
        })
        .from(lumenChallengeScheme)
        .where(eq(lumenChallengeScheme.scheme_id, parseInt(req.params.id)));

      if (!schemes.length) {
        return apiClient.serverError(req, res, next);
      }

      that.scheme = schemes[0];

      // Get the challenges using Drizzle
      const challengeRows = await db
        .select({
          id: lumenChallenge.challenge_id,
          description: lumenChallenge.challenge_description,
          imageUrl: lumenChallenge.challenge_image_url,
          optionA: lumenChallenge.challenge_option_a,
          consequenceA: lumenChallenge.challenge_consequence_a,
          optionMetric1A: lumenChallenge.challenge_option_metric1_a,
          optionMetric2A: lumenChallenge.challenge_option_metric2_a,
          optionMetric3A: lumenChallenge.challenge_option_metric3_a,
          optionB: lumenChallenge.challenge_option_b,
          consequenceB: lumenChallenge.challenge_consequence_b,
          optionMetric1B: lumenChallenge.challenge_option_metric1_b,
          optionMetric2B: lumenChallenge.challenge_option_metric2_b,
          optionMetric3B: lumenChallenge.challenge_option_metric3_b,
          optionC: lumenChallenge.challenge_option_c,
          consequenceC: lumenChallenge.challenge_consequence_c,
          optionMetric1C: lumenChallenge.challenge_option_metric1_c,
          optionMetric2C: lumenChallenge.challenge_option_metric2_c,
          optionMetric3C: lumenChallenge.challenge_option_metric3_c,
        })
        .from(lumenChallenge)
        .where(eq(lumenChallenge.challenge_scheme_id, parseInt(req.params.id)));

      // Map the challenges to match the previous implementation's structure
      const challenges = challengeRows.map((row) => ({
        id: row.id,
        description: row.description,
        imageUrl: row.imageUrl,
        optionA: row.optionA,
        consequenceA: row.consequenceA,
        optionMetric1A: row.optionMetric1A,
        optionMetric2A: row.optionMetric2A,
        optionMetric3A: row.optionMetric3A,
        optionB: row.optionB,
        consequenceB: row.consequenceB,
        optionMetric1B: row.optionMetric1B,
        optionMetric2B: row.optionMetric2B,
        optionMetric3B: row.optionMetric3B,
        optionC: row.optionC,
        consequenceC: row.consequenceC,
        optionMetric1C: row.optionMetric1C,
        optionMetric2C: row.optionMetric2C,
        optionMetric3C: row.optionMetric3C,
      }));

      that.scheme.challenges = challenges;

      return apiClient.success(req, res, next, that.scheme);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  create: async (req, res, next) => {
    const that = {};

    try {
      // Start a transaction
      await db.transaction(async (tx) => {
        // Create challenge scheme
        const schemeResults = await tx
          .insert(lumenChallengeScheme)
          .values({
            scheme_name: req.body.name,
            scheme_created_at: sql`current_timestamp`,
          })
          .returning({
            scheme_id: lumenChallengeScheme.scheme_id,
          });

        that.schemeId = schemeResults[0].scheme_id;

        // Add challenges sequentially to match previous implementation's behavior
        for (const challenge of req.body.challenges) {
          await tx.insert(lumenChallenge).values({
            challenge_scheme_id: that.schemeId,
            challenge_image_url: challenge.imageUrl,
            challenge_description: challenge.description,
            challenge_option_a: challenge.optionA,
            challenge_consequence_a: challenge.consequenceA,
            challenge_option_metric1_a: challenge.optionMetric1A,
            challenge_option_metric2_a: challenge.optionMetric2A,
            challenge_option_metric3_a: challenge.optionMetric3A,
            challenge_option_b: challenge.optionB,
            challenge_consequence_b: challenge.consequenceB,
            challenge_option_metric1_b: challenge.optionMetric1B,
            challenge_option_metric2_b: challenge.optionMetric2B,
            challenge_option_metric3_b: challenge.optionMetric3B,
            challenge_option_c: challenge.optionC,
            challenge_consequence_c: challenge.consequenceC,
            challenge_option_metric1_c: challenge.optionMetric1C,
            challenge_option_metric2_c: challenge.optionMetric2C,
            challenge_option_metric3_c: challenge.optionMetric3C,
          });

          // Add console.log to match previous implementation's logging
          console.log('challenge insert result: ', null, { rows: [] });
        }

        // Apply to all teams if requested
        if (req.body.applyToAllTeams) {
          await tx.update(lumenTeam).set({
            team_challenge_scheme_id: that.schemeId,
          });
        }
      });

      return apiClient.success(req, res, next, that);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  update: async (req, res, next) => {
    try {
      // Validate required fields
      if (!req.body.id || !req.body.name || !Array.isArray(req.body.challenges)) {
        console.error('Invalid request body:', req.body);
        return apiClient.clientError(req, res, next, 'Missing required fields');
      }

      console.log('Starting update for scheme:', req.body.id);
      
      await db.transaction(async (tx) => {
        // Verify scheme exists
        const existing = await tx
          .select({ id: lumenChallengeScheme.scheme_id })
          .from(lumenChallengeScheme)
          .where(eq(lumenChallengeScheme.scheme_id, req.body.id));

        if (!existing.length) {
          throw new Error(`Scheme ${req.body.id} not found`);
        }

        // Update scheme name
        console.log('Updating scheme name to:', req.body.name);
        await tx
          .update(lumenChallengeScheme)
          .set({
            scheme_name: req.body.name,
          })
          .where(eq(lumenChallengeScheme.scheme_id, req.body.id));

        // Delete existing challenges
        console.log('Deleting existing challenges for scheme:', req.body.id);
        await tx
          .delete(lumenChallenge)
          .where(eq(lumenChallenge.challenge_scheme_id, req.body.id));

        // Re-create challenges
        console.log('Creating new challenges:', req.body.challenges.length);
        for (const challenge of req.body.challenges) {
          await tx.insert(lumenChallenge).values({
            challenge_scheme_id: req.body.id,
            challenge_image_url: challenge.imageUrl || '',
            challenge_description: challenge.description,
            challenge_option_a: challenge.optionA,
            challenge_consequence_a: challenge.consequenceA,
            challenge_option_metric1_a: challenge.optionMetric1A,
            challenge_option_metric2_a: challenge.optionMetric2A,
            challenge_option_metric3_a: challenge.optionMetric3A,
            challenge_option_b: challenge.optionB,
            challenge_consequence_b: challenge.consequenceB,
            challenge_option_metric1_b: challenge.optionMetric1B,
            challenge_option_metric2_b: challenge.optionMetric2B,
            challenge_option_metric3_b: challenge.optionMetric3B,
            challenge_option_c: challenge.optionC,
            challenge_consequence_c: challenge.consequenceC,
            challenge_option_metric1_c: challenge.optionMetric1C,
            challenge_option_metric2_c: challenge.optionMetric2C,
            challenge_option_metric3_c: challenge.optionMetric3C,
          });
        }
      });

      console.log('Update completed successfully');
      return apiClient.success(req, res, next);
    } catch (error) {
      console.error('Update failed:', error.message);
      return apiClient.serverError(req, res, next, error.message);
    }
  },

  list: async (req, res, next) => {
    try {
      let sort = 'scheme_name';

      switch (req.query.sort) {
        case 'scheme_created_at':
          sort = 'scheme_created_at';
          break;
        default:
          break;
      }

      // Handle showDisabled exactly like the previous implementation
      const showDisabled = req.query.showDisabled || 'FALSE';
      const sortOrder = req.query.ascending ? 'ASC' : 'DESC';

      const result = await db
        .select({
          id: lumenChallengeScheme.scheme_id,
          name: lumenChallengeScheme.scheme_name,
          disabled: lumenChallengeScheme.scheme_disabled,
          created: sql`TO_CHAR(${lumenChallengeScheme.scheme_created_at}, 'YYYY-MM-DD')`,
          total: sql`COUNT(*) OVER()`,
        })
        .from(lumenChallengeScheme)
        .where(
          and(
            sql`LOWER(${lumenChallengeScheme.scheme_name}) LIKE ${
              '%' + (req.query.query || '').toLowerCase() + '%'
            }`,
            eq(lumenChallengeScheme.scheme_disabled, showDisabled)
          )
        )
        .orderBy(sql`${sql.identifier(sort)} ${sql.raw(sortOrder)}`)
        .offset(sql`${parseInt(req.query.offset || 0)}`)
        .limit(sql`${parseInt(req.query.limit || 10)}`);

      console.log(req.query);
      return apiClient.success(req, res, next, result);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  listAll: async (req, res, next) => {
    try {
      const schemes = await db
        .select({
          id: lumenChallengeScheme.scheme_id,
          name: lumenChallengeScheme.scheme_name,
        })
        .from(lumenChallengeScheme)
        .where(eq(lumenChallengeScheme.scheme_disabled, false))
        .orderBy(asc(lumenChallengeScheme.scheme_name));

      return apiClient.success(req, res, next, schemes);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  toggleDisabled: async (req, res, next) => {
    try {
      await db
        .update(lumenChallengeScheme)
        .set({
          scheme_disabled: sql`NOT ${lumenChallengeScheme.scheme_disabled}`,
        })
        .where(eq(lumenChallengeScheme.scheme_id, req.params.id));

      console.log(req.body); // Keep original logging
      return apiClient.success(req, res, next);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },
};

export default CHALLENGE_CONTROLLER;
