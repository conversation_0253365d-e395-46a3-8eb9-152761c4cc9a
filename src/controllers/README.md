# Controllers

This directory contains the API controllers that handle HTTP requests for the Lumen simulation platform.

## Overview

Controllers are responsible for handling incoming HTTP requests, processing business logic, and returning appropriate responses. Each controller focuses on a specific domain or resource.

## Controllers

### Core Business Logic
- **decision.js** - Manages decision schemes and pages for simulations
- **decisionGroups.js** - Handles grouping and organization of decisions  
- **decisionResults.js** - Processes and retrieves simulation results
- **client.js** - Client management and configuration
- **team.js** - Team operations and member management
- **user.js** - User authentication, registration, and profile management

### Assessment & Learning
- **challenge.js** - Challenge creation and management
- **initiative.js** - Initiative tracking and progress
- **selfAssessment.js** - Self-assessment questionnaires and scoring
- **leaderboard.js** - Ranking and scoring displays

### Organization Management  
- **orgChart.js** - Organizational chart structure and hierarchy
- **clientOrgChartsMapping.js** - Maps clients to organizational structures

### Content & Resources
- **files.js** - File upload, download, and management
- **welcomePage.js** - Welcome page content and customization
- **openai.js** - AI integration for content generation and analysis

### Administration
- **admin.js** - Administrative functions and system management

### Specialized Controllers
- **user/saveChallenges.js** - Specialized user challenge persistence logic

## Architecture

Controllers follow a consistent pattern:
- Import required database queries and utilities
- Export an object with HTTP method handlers (get, post, put, delete)
- Use async/await for database operations
- Include proper error handling and response formatting
- Leverage database transactions where needed

## Dependencies

- Database queries from `../db/queries/`
- Utility functions from `../utils/`
- Database connection from `../db/index.js`
- External API clients from `../lib/`