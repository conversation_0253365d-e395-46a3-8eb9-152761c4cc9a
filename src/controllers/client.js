import { apiClient } from '../lib/index.js';
import { db } from '../db/index.js';
import {
  findClientById,
  findGlobalTeamMetricsSchemes,
  findInitiativeScheme,
  findChallengeScheme,
  insertGlobalTeamMetric,
  updateGlobalTeamMetric,
  listClients,
  listAllClients,
} from '../db/queries/client.js';
import {
  teamClients as teamClientsSchema,
  teamGoals as teamGoalsSchema,
  clients,
  orgChart,
  orgChartType,
  orgChartUser,
  lumenChallenge,
  lumenChallengeScheme,
  lumenTeam,
  teamSelectedOrgChart,
  lumenTeamSelectedChallenge,
  lumenInitiative,
  lumenTeamSelectedInitiative,
  globalTeamMetricsScheme,
  decisionResults, // Import the decision results table
} from '../db/schema.js';
import { sql, eq, and, inArray, asc } from 'drizzle-orm';
import getExcel from '../lib/client_excel_export.js';
import { calculateOrgChartUsersMeetingOrders } from './clientOrgChartsMapping.js';
import {
  findDecisionPagesBySchemeId,
  findDecisionSchemeById,
} from '../db/queries/decision.js';

const findDecisionScheme = async (db, schemeId) => {
  const [scheme] = await db
    .select()
    .from(lumenDecisionScheme)
    .where(eq(lumenDecisionScheme.scheme_id, schemeId));
  return scheme;
};

const CLIENT_CONTROLLER = {
  get: async (req, res, next) => {
    try {
      // Get client details
      const client = await findClientById(db, req.params.id);

      if (!client) {
        return apiClient.serverError(req, res, next);
      }

      // Get the related schemes concurrently
      const [
        globalTeamMetricSchemes,
        initiativeScheme,
        challengeScheme,
        decisionScheme,
      ] = await Promise.all([
        findGlobalTeamMetricsSchemes(db, req.params.id),
        client.initiativeSchemeId
          ? findInitiativeScheme(db, client.initiativeSchemeId)
          : null,
        client.challengeSchemeId
          ? findChallengeScheme(db, client.challengeSchemeId)
          : null,
        client.decision_scheme_id
          ? findDecisionScheme(db, client.decision_scheme_id)
          : null,
      ]);

      // Build response object matching original format
      const response = {
        ...client,
        globalTeamMetricsSchemes: globalTeamMetricSchemes || [],
        initiativeScheme: initiativeScheme || null,
        challengeScheme: challengeScheme || null,
        decisionScheme: decisionScheme || null,
      };

      return apiClient.success(req, res, next, response);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  create: async (request, response, next) => {
    const {
      backgroundImage,
      challengeSchemeId,
      challengesTabName,
      challengesTabVisibility,
      darkHighlightColor,
      globalTeamMetricsSchemes,
      goalsTabName,
      goalsTabVisibility,
      homeSchemeId,
      homeTabName,
      homeTabVisibility,
      initiativeSchemeId,
      initiativesTabVisibility,
      isSignUpEnabled,
      leaderboardSchemeId,
      leaderboardTabName,
      leaderboardTabVisibility,
      lightHighlightColor,
      logoImage,
      name,
      orgChartSchemeId,
      orgChartTabName,
      orgChartTabVisibility,
      selfAssessmentSchemeId,
      selfAssessmentTabName,
      selfAssessmentTabVisibility,
      signUpEmailDomain,
      strategicInitiativesTabName,
      strategicTabName,
      workshopImage,
      decisionSchemeId,
      decisionsTabName,
      decisionsTabVisibility,
      schemeOrder = [],
      fteMax,
      investmentMax,
      aiSummaryTitle,
    } = request.body;

    // Handle field name mapping - use strategicTabName if strategicInitiativesTabName is not provided
    const strategicTabNameValue = strategicInitiativesTabName || strategicTabName;

    try {
      // Validate challengeSchemeId
      const challengeScheme = await db
        .select()
        .from(lumenChallengeScheme)
        .where(eq(lumenChallengeScheme.scheme_id, challengeSchemeId));

      if (!challengeScheme.length) {
        return apiClient.notFound(
          request,
          response,
          next,
          "Challenge scheme doesn't exist"
        );
      }

      const createdClient = await db.transaction(async (tx) => {
        const [client] = await tx
          .insert(clients)
          .values({
            name,
            logo_image: logoImage,
            background_image: backgroundImage,
            home_tab_name: homeTabName,
            goals_tab_name: goalsTabName,
            challenges_tab_name: challengesTabName,
            strategic_tab_name: strategicTabNameValue,
            org_chart_tab_name: orgChartTabName,
            leaderboard_tab_name: leaderboardTabName,
            dark_highlight_color: darkHighlightColor,
            light_highlight_color: lightHighlightColor,
            home_scheme_id: homeSchemeId,
            initiative_scheme_id: initiativeSchemeId,
            challenge_scheme_id: challengeSchemeId,
            leaderboard_scheme_id: leaderboardSchemeId,
            org_chart_scheme_id: orgChartSchemeId,
            home_tab_visibility: homeTabVisibility,
            goals_tab_visibility: goalsTabVisibility,
            challenges_tab_visibility: challengesTabVisibility,
            initiatives_tab_visibility: initiativesTabVisibility,
            org_chart_tab_visibility: orgChartTabVisibility,
            leaderboard_tab_visibility: leaderboardTabVisibility,
            sign_up_email_domain: signUpEmailDomain,
            workshop_image: workshopImage,
            is_sign_up_enabled: isSignUpEnabled,
            self_assessment_scheme_id: selfAssessmentSchemeId,
            self_assessment_tab_name: selfAssessmentTabName,
            self_assessment_tab_visibility: selfAssessmentTabVisibility,
            decision_scheme_id: decisionSchemeId,
            decision_tab_name: decisionsTabName,
            decision_tab_visibility: decisionsTabVisibility,
            scheme_order: schemeOrder,
            fte_max: fteMax,
            investment_max: investmentMax,
            ai_summary_title: aiSummaryTitle,
            created_at: sql`current_timestamp`,
          })
          .returning();

        // Save Global Team Metrics
        for (const globalTeamMetric of globalTeamMetricsSchemes) {
          const { name, alias, defaultValue } = globalTeamMetric;

          if (!name) {
            throw new Error('Name is a required field for global team metrics');
          }

          await insertGlobalTeamMetric(tx, {
            clientId: client.id,
            name,
            alias,
            defaultValue: parseInt(defaultValue) || 0,
          });
        }

        return client;
      });

      return apiClient.success(request, response, next, createdClient);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(request, response, next);
    }
  },

  update: async (req, res, next) => {
    const {
      id,
      name,
      logoImage,
      backgroundImage,
      homeTabName,
      goalsTabName,
      challengesTabName,
      strategicTabName,
      strategicInitiativesTabName,
      orgChartTabName,
      leaderboardTabName,
      darkHighlightColor,
      lightHighlightColor,
      homeSchemeId,
      initiativeSchemeId,
      challengeSchemeId,
      leaderboardSchemeId,
      orgChartSchemeId,
      homeTabVisibility,
      goalsTabVisibility,
      challengesTabVisibility,
      initiativesTabVisibility,
      orgChartTabVisibility,
      leaderboardTabVisibility,
      signUpEmailDomain,
      workshopImage,
      isSignUpEnabled,
      selfAssessmentSchemeId,
      selfAssessmentTabName,
      selfAssessmentTabVisibility,
      globalTeamMetricsSchemes,
      decisionSchemeId,
      decisionsTabName,
      decisionsTabVisibility,
      schemeOrder = [],
      fteMax,
      investmentMax,
      aiSummaryTitle,
    } = req.body;

    // Handle field name mapping - use strategicTabName if strategicInitiativesTabName is not provided
    const strategicTabNameValue = strategicInitiativesTabName || strategicTabName;

    try {
      const updatedClient = await db.transaction(async (tx) => {
        const [client] = await tx
          .update(clients)
          .set({
            name,
            logo_image: logoImage,
            background_image: backgroundImage,
            home_tab_name: homeTabName,
            goals_tab_name: goalsTabName,
            challenges_tab_name: challengesTabName,
            strategic_tab_name: strategicTabNameValue,
            org_chart_tab_name: orgChartTabName,
            leaderboard_tab_name: leaderboardTabName,
            dark_highlight_color: darkHighlightColor,
            light_highlight_color: lightHighlightColor,
            home_scheme_id: homeSchemeId,
            initiative_scheme_id: initiativeSchemeId,
            challenge_scheme_id: challengeSchemeId,
            leaderboard_scheme_id: leaderboardSchemeId,
            org_chart_scheme_id: orgChartSchemeId,
            home_tab_visibility: homeTabVisibility,
            goals_tab_visibility: goalsTabVisibility,
            challenges_tab_visibility: challengesTabVisibility,
            initiatives_tab_visibility: initiativesTabVisibility,
            org_chart_tab_visibility: orgChartTabVisibility,
            leaderboard_tab_visibility: leaderboardTabVisibility,
            sign_up_email_domain: signUpEmailDomain,
            workshop_image: workshopImage,
            is_sign_up_enabled: isSignUpEnabled,
            self_assessment_scheme_id: selfAssessmentSchemeId,
            self_assessment_tab_name: selfAssessmentTabName,
            self_assessment_tab_visibility: selfAssessmentTabVisibility,
            decision_scheme_id: decisionSchemeId,
            decision_tab_name: decisionsTabName,
            decision_tab_visibility: decisionsTabVisibility,
            scheme_order: schemeOrder,
            fte_max: fteMax,
            investment_max: investmentMax,
            ai_summary_title: aiSummaryTitle,
          })
          .where(eq(clients.id, id))
          .returning();

        // Update Global Team Metrics
        for (const globalTeamMetric of globalTeamMetricsSchemes) {
          const { name, alias, defaultValue, metricId, id: globalMetricId } = globalTeamMetric;

          if (!name) {
            throw new Error('Name is a required field for global team metrics');
          }

          // Use metricId if provided, otherwise use id
          const metricIdValue = metricId || globalMetricId;

          await updateGlobalTeamMetric(tx, {
            clientId: id,
            name,
            alias,
            defaultValue: parseInt(defaultValue) || 0,
            id: metricIdValue,
          });

          // console.log('Updated metric:', metricIdValue, alias, id, parseInt(defaultValue) || 0);
        }

        return client;
      });

      return apiClient.success(req, res, next, updatedClient);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  list: async (req, res, next) => {
    const { query, offset, limit, sort, ascending, showDisabled } = req.query;

    try {
      const clientsList = await listClients(db, {
        query: query || '',
        offset: parseInt(offset) || 0,
        limit: parseInt(limit) || 10,
        sort: sort || 'name',
        ascending: ascending === 'true',
        showDisabled: showDisabled === 'true',
      });

      return apiClient.success(req, res, next, clientsList);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  listAll: async (req, res, next) => {
    try {
      const clientsList = await listAllClients(db);

      return apiClient.success(req, res, next, clientsList);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  toggleDisabled: async (req, res, next) => {
    const { id } = req.params;

    try {
      const [client] = await db
        .update(clients)
        .set({
          disabled: sql`NOT disabled`,
        })
        .where(eq(clients.id, id))
        .returning();

      return apiClient.success(req, res, next, client);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  exportExcel: async (req, res, next) => {
    const clientId = parseInt(req.params.id);

    if (!clientId || isNaN(clientId)) {
      return apiClient.badRequest(req, res, next, 'Invalid client ID');
    }

    try {
      const client = await db
        .select()
        .from(clients)
        .where(eq(clients.id, clientId))
        .limit(1);

      if (!client || client.length === 0) {
        return apiClient.serverError(req, res, next);
      }

      // Fetch decision scheme and pages if a decision scheme is assigned
      let decisions = {
        scheme: null,
        pages: [],
      };

      if (client[0]?.decision_scheme_id) {
        try {
          // Fetch decision scheme
          const scheme = await findDecisionSchemeById(
            client[0].decision_scheme_id
          );
          
          if (scheme) {
            decisions.scheme = scheme;
            
            // Fetch decision pages
            const pages = await findDecisionPagesBySchemeId(scheme.id);
            if (pages && pages.length > 0) {
              decisions.pages = pages;
            }
            
            // Fetch decision results for all teams
            const teamClients = await db
              .select()
              .from(teamClientsSchema)
              .where(eq(teamClientsSchema.client_id, clientId));
              
            const teamIds = teamClients.map(tc => tc.team_id);
            
            if (teamIds.length > 0) {
              const results = await db
                .select()
                .from(decisionResults)
                .where(
                  and(
                    inArray(decisionResults.user_id, teamIds),
                    eq(decisionResults.decision_scheme_id, client[0].decision_scheme_id)
                  )
                );
                
              if (results && results.length > 0) {
                decisions.results = results;
              }
            }
          }
        } catch (error) {
          console.error("Error fetching decision data:", error);
          // Continue without decision data
        }
      }

      const teamClients = await db
        .select()
        .from(teamClientsSchema)
        .where(eq(teamClientsSchema.client_id, clientId));

      const teamIds = teamClients.map((tc) => tc.team_id);

      const teams = await db
        .select()
        .from(lumenTeam)
        .where(
          and(
            eq(lumenTeam.team_disabled, false),
            inArray(lumenTeam.team_id, teamIds)
          )
        )
        .orderBy(asc(lumenTeam.team_id));

      const teamGoals = await db
        .select()
        .from(teamGoalsSchema)
        .where(
          and(
            inArray(teamGoalsSchema.team_id, teamIds),
            eq(teamGoalsSchema.client_id, clientId)
          )
        );

      const orgCharts = await db
        .select()
        .from(orgChart)
        .where(eq(orgChart.org_chart_scheme_id, client.org_chart_scheme_id));

      const orgChartIds = orgCharts.map((oc) => oc.org_chart_id);

      const orgChartTypes = await db
        .select()
        .from(orgChartType)
        .where(inArray(orgChartType.org_chart_id, orgChartIds));

      const orgChartTypeIds = orgChartTypes.map((oct) => oct.org_chart_id);

      const orgChartUsers = await db
        .select()
        .from(orgChartUser)
        .where(inArray(orgChartUser.org_chart_type_id, orgChartTypeIds));

      const teamSelectedOrgCharts = await db
        .select()
        .from(teamSelectedOrgChart)
        .where(
          and(
            inArray(teamSelectedOrgChart.team_id, teamIds),
            eq(teamSelectedOrgChart.client_id, clientId)
          )
        );

      const challenges = await db
        .select()
        .from(lumenChallenge)
        .where(
          eq(lumenChallenge.challenge_scheme_id, client.challenge_scheme_id)
        );

      const selectedChallenges = await db
        .select()
        .from(lumenTeamSelectedChallenge)
        .where(
          and(
            inArray(lumenTeamSelectedChallenge.selected_team_id, teamIds),
            eq(lumenTeamSelectedChallenge.client_id, clientId)
          )
        );

      const initiatives = await db
        .select()
        .from(lumenInitiative)
        .where(
          eq(lumenInitiative.initiative_scheme_id, client.initiative_scheme_id)
        );

      const selectedInitiatives = await db
        .select()
        .from(lumenTeamSelectedInitiative)
        .where(
          and(
            inArray(lumenTeamSelectedInitiative.selected_team_id, teamIds),
            eq(lumenTeamSelectedInitiative.client_id, clientId)
          )
        );

      const globalTeamMetricsSchemes = await db
        .select()
        .from(globalTeamMetricsScheme)
        .where(eq(globalTeamMetricsScheme.client_id, clientId));

      const result = {
        client,
        teams,
        teamGoals,
        orgCharts,
        orgChartTypes,
        orgChartUsers,
        teamSelectedOrgCharts,
        challenges,
        selectedChallenges,
        initiatives,
        selectedInitiatives,
        globalTeamMetricsSchemes,
        decisions, // Add the decisions object
      };

      console.log('Client export data prepared:', {
        clientId,
        teamsCount: teams.length,
        hasDecisionScheme: !!client[0]?.decision_scheme_id,
        decisionPagesCount: decisions?.pages?.length || 0,
        decisionResultsCount: decisions?.results?.length || 0
      });

      const mappedResults = mapExportResults(result);
      const excelBuffer = getExcel(mappedResults);

      res.setHeader('Content-Type', 'application/vnd.openxmlformats');
      res.setHeader(
        'Content-Disposition',
        'attachment; filename=' + `${mappedResults.clientName}.xlsx`
      );
      res.end(excelBuffer, 'binary');
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  exportData: async (req, res, next) => {
    const clientId = parseInt(req.params.id);

    if (!clientId || isNaN(clientId)) {
      return apiClient.badRequest(req, res, next, 'Invalid client ID');
    }

    try {
      const client = await db
        .select()
        .from(clients)
        .where(eq(clients.id, clientId))
        .limit(1);

      if (!client) {
        return apiClient.serverError(req, res, next);
      }

      // Fetch decision scheme and pages if a decision scheme is assigned
      let decisionData = null;
      if (client[0]?.decision_scheme_id) {
        const scheme = await findDecisionSchemeById(
          client[0].decision_scheme_id
        );
        if (scheme) {
          const pages = await findDecisionPagesBySchemeId(scheme.id);
          decisionData = {
            scheme,
            pages,
          };
        }
      }

      const teamClients = await db
        .select()
        .from(teamClientsSchema)
        .where(eq(teamClientsSchema.client_id, clientId));

      const teamIds = teamClients.map((tc) => tc.team_id);

      // First get teams and orgCharts since they're needed for subsequent queries
      const [teams, orgCharts] = await Promise.all([
        db
          .select()
          .from(lumenTeam)
          .where(
            and(
              eq(lumenTeam.team_disabled, false),
              inArray(lumenTeam.team_id, teamIds)
            )
          )
          .orderBy(asc(lumenTeam.team_id)),
        db
          .select()
          .from(orgChart)
          .where(
            eq(orgChart.org_chart_scheme_id, client[0].org_chart_scheme_id)
          ),
      ]);

      const orgChartIds = orgCharts.map((oc) => oc.org_chart_id);

      // Get orgChartTypes using orgChartIds
      const orgChartTypes = await db
        .select()
        .from(orgChartType)
        .where(inArray(orgChartType.org_chart_id, orgChartIds));

      const orgChartTypeIds = orgChartTypes.map((oct) => oct.org_chart_type_id);

      // Now we can run the rest of the queries in parallel
      const [
        teamGoals,
        orgChartUsers,
        teamSelectedOrgCharts,
        challenges,
        selectedChallenges,
        initiatives,
        selectedInitiatives,
        globalTeamMetricsSchemes,
      ] = await Promise.all([
        db
          .select()
          .from(teamGoalsSchema)
          .where(
            and(
              inArray(teamGoalsSchema.team_id, teamIds),
              eq(teamGoalsSchema.client_id, clientId)
            )
          ),
        db
          .select()
          .from(orgChartUser)
          .where(inArray(orgChartUser.org_chart_type_id, orgChartTypeIds)),
        db
          .select()
          .from(teamSelectedOrgChart)
          .where(
            and(
              inArray(teamSelectedOrgChart.team_id, teamIds),
              eq(teamSelectedOrgChart.client_id, clientId)
            )
          ),
        db
          .select()
          .from(lumenChallenge)
          .where(
            eq(
              lumenChallenge.challenge_scheme_id,
              client[0].challenge_scheme_id
            )
          ),
        db
          .select()
          .from(lumenTeamSelectedChallenge)
          .where(
            and(
              inArray(lumenTeamSelectedChallenge.selected_team_id, teamIds),
              eq(lumenTeamSelectedChallenge.client_id, clientId)
            )
          ),
        db
          .select()
          .from(lumenInitiative)
          .where(
            eq(
              lumenInitiative.initiative_scheme_id,
              client[0].initiative_scheme_id
            )
          ),
        db
          .select()
          .from(lumenTeamSelectedInitiative)
          .where(
            and(
              inArray(lumenTeamSelectedInitiative.selected_team_id, teamIds),
              eq(lumenTeamSelectedInitiative.client_id, clientId)
            )
          ),
        db
          .select()
          .from(globalTeamMetricsScheme)
          .where(eq(globalTeamMetricsScheme.client_id, clientId)),
      ]);

      const result = {
        client,
        teams,
        teamGoals,
        orgCharts,
        orgChartTypes,
        orgChartUsers,
        teamSelectedOrgCharts,
        challenges,
        selectedChallenges,
        initiatives,
        selectedInitiatives,
        globalTeamMetricsSchemes,
        decisionData,
      };

      return apiClient.success(req, res, next, result);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },
};

function mapExportResults(response) {
  const {
    client: [clientData],
    teams = [],
    challenges = [],
    selectedChallenges = [],
    initiatives = [],
    selectedInitiatives = [],
    globalTeamMetricsSchemes = [],
    orgChartUsers = [],
    selfAssessments = [],
    selfAssessmentScheme,
    teamSelectedOrgCharts = [],
    teamGoals = [],
    decisions = {},
  } = response;

  if (!clientData) {
    throw new Error('Client data not found');
  }

  const result = {
    clientName: clientData.name,
    challenges: [],
    initiatives: [],
    teams: [],
    selfAssessments: {
      name: selfAssessmentScheme?.name || '',
      data: [],
    },
    orgChartUsers: [],
    decisions: {
      name: decisions?.scheme?.name || 'Decisions',
      pages: decisions?.pages || [],
      results: decisions?.results || [],
    },
  };

  // Map all challenges
  challenges.forEach((challenge, index) => {
    result.challenges.push({
      id: challenge.challenge_id,
      name: `Challenge ${index + 1}`,
    });
  });

  // Map all initiatives
  initiatives.forEach((initiative) => {
    result.initiatives.push({
      id: initiative.initiative_id,
      name: initiative.initiative_name,
    });
  });

  // Map teams
  teams.forEach((team) => {
    const teamGoal = teamGoals.find(({ team_id }) => team_id === team.team_id);
    const teamMetrics = globalTeamMetricsSchemes.filter(
      (metric) => metric.team_id === team.team_id
    );
    const teamChallenges = selectedChallenges.filter(
      (challenge) => challenge.selected_team_id === team.team_id
    );
    const teamInitiatives = selectedInitiatives.filter(
      (initiative) => initiative.selected_team_id === team.team_id
    );
    
    // Find team's decision results
    const teamDecisions = decisions?.results?.find(
      (result) => result.user_id === team.team_id
    );

    const mappedTeam = {
      id: team.team_id,
      name: team.team_name,
      goal1: teamGoal?.goal_1 || '',
      goal2: teamGoal?.goal_2 || '',
      goal3: teamGoal?.goal_3 || '',
      metric1: 0,
      metric2: 0,
      metric3: 0,
      challenges: result.challenges.map((v) => ({ ...v, option: 'N/A' })),
      initiatives: result.initiatives.map((v) => ({
        ...v,
        option: 'Not chosen',
      })),
      decisions: {
        totalFTE: teamDecisions?.total_fte || 0,
        totalInvestment: teamDecisions?.total_investment || 0,
        pnlImpact: teamDecisions?.pnl_impact || 0,
        selectedValues: teamDecisions?.selected_values || { pages: [] },
      },
    };

    // Map metrics
    teamMetrics.forEach((metric) => {
      const value = parseInt(metric.value || 0, 10);
      const defaultValue = parseInt(metric.default_value || 0, 10);
      const key = (metric.alias || '').split(' ').join('').toLowerCase();
      if (key) {
        mappedTeam[key] = value + defaultValue;
      }
    });

    // Map challenges
    teamChallenges.forEach((challenge) => {
      const mappedChallenge = mappedTeam.challenges.find(
        ({ id }) => id === challenge.selected_challenge_id
      );
      if (mappedChallenge) {
        if (challenge.selected_option_a) mappedChallenge.option = 'Option A';
        else if (challenge.selected_option_b)
          mappedChallenge.option = 'Option B';
        else if (challenge.selected_option_c)
          mappedChallenge.option = 'Option C';
      }
    });

    // Map initiatives
    teamInitiatives.forEach((initiative) => {
      const mappedInitiative = mappedTeam.initiatives.find(
        ({ id }) => id === initiative.selected_initiative_id
      );
      if (mappedInitiative) {
        mappedInitiative.option = 'Chosen';
      }
    });

    result.teams.push(mappedTeam);
  });

  // Map org chart users
  result.orgChartUsers = orgChartUsers.map((user) => ({
    id: user.id,
    name: `${user.name} (${user.title})`,
    teams: teams.map((team) => {
      const selectedOrgCharts = teamSelectedOrgCharts.filter(
        (chart) => chart && chart.team_id === team.team_id
      );
      const meetingsOrder =
        calculateOrgChartUsersMeetingOrders(selectedOrgCharts);
      return {
        id: team.team_id,
        value: meetingsOrder[user.id] ? meetingsOrder[user.id].join(', ') : '',
      };
    }),
  }));

  return result;
}

export default CLIENT_CONTROLLER;
