import { apiClient } from '../lib/index.js';
import {
  createOrgChartScheme,
  createOrg<PERSON><PERSON>,
  createOrg<PERSON>hartType,
  listOrgChartSchemes,
  listAllOrgChartSchemes,
  toggleOrgChartSchemeDisabled,
} from '../db/queries/orgChart.js';
import {
  orgChartScheme,
  orgChart,
  orgChartType,
  orgChartUser,
  teamSelectedOrgChart,
} from '../db/schema.js';
import { db } from '../db/index.js';
import { eq, inArray, sql, asc, and } from 'drizzle-orm';

const ORG_CHART_CONTROLLER = {
  get: async (req, res, next) => {
    const { id } = req.params;

    try {
      // Fetch the org chart scheme
      const scheme = await db
        .select()
        .from(orgChartScheme)
        .where(eq(orgChartScheme.id, id))
        .limit(1);

      if (!scheme) {
        return apiClient.notFound(req, res, next, 'Org Chart Scheme not found');
      }

      // Fetch the org charts
      const charts = await db
        .select()
        .from(orgChart)
        .where(eq(orgChart.org_chart_scheme_id, id));

      const chartIds = charts.map((chart) => chart.id);

      // Fetch the org chart types
      const types = await db
        .select()
        .from(orgChartType)
        .where(inArray(orgChartType.org_chart_id, chartIds));

      const typeIds = types.map((type) => type.id);

      // Fetch the org chart users
      const users = await db
        .select()
        .from(orgChartUser)
        .where(inArray(orgChartUser.org_chart_type_id, typeIds))
        .orderBy(asc(orgChartUser.id));

      // Map users to org chart types
      for (const type of types) {
        type.users = users.filter((user) => user.org_chart_type_id === type.id);
      }

      // Map org chart types to org charts
      for (const chart of charts) {
        chart.orgChartTypes = types.filter(
          (type) => type.org_chart_id === chart.id
        );
      }

      const result = {
        id: scheme.id,
        name: scheme.name,
        orgCharts: charts,
      };

      return apiClient.success(req, res, next, result);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  create: async (req, res, next) => {
    const { name, orgCharts } = req.body;

    try {
      const createdScheme = await db.transaction(async (tx) => {
        // Create the org chart scheme
        const [scheme] = await tx
          .insert(orgChartScheme)
          .values({
            name,
            disabled: false,
            created_at: sql`current_timestamp`,
          })
          .returning();

        // Create org charts and their types
        for (const chart of orgCharts) {
          const [createdChart] = await tx
            .insert(orgChart)
            .values({
              org_chart_scheme_id: scheme.id,
              created_at: sql`current_timestamp`,
            })
            .returning();

          // Create types for this chart
          for (const type of chart.orgChartTypes) {
            const [createdType] = await tx
              .insert(orgChartType)
              .values({
                name: type.name,
                type: type.type,
                is_visible: type.is_visible ?? true,
                org_chart_id: createdChart.id,
                created_at: sql`current_timestamp`,
              })
              .returning();

            // Create users for this type
            await createOrgChartUsers({
              tx,
              users: type.users,
              orgChartTypeId: createdType.id,
            });
          }
        }

        return scheme;
      });

      return apiClient.success(req, res, next, createdScheme);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  update: async (req, res, next) => {
    const { id, name, orgCharts } = req.body;

    try {
      const updatedScheme = await db.transaction(async (tx) => {
        // Update org chart scheme name
        await tx
          .update(orgChartScheme)
          .set({ name })
          .where(eq(orgChartScheme.id, id));

        // Get existing org charts
        const existingCharts = await tx
          .select()
          .from(orgChart)
          .where(eq(orgChart.org_chart_scheme_id, id));

        const existingChartIds = existingCharts.map((chart) => chart.id);

        // Get existing org chart types
        const existingTypes = await tx
          .select()
          .from(orgChartType)
          .where(inArray(orgChartType.org_chart_id, existingChartIds));

        const existingTypeIds = existingTypes.map((type) => type.id);

        // Delete all org chart users, types, and charts
        await tx
          .delete(orgChartUser)
          .where(inArray(orgChartUser.org_chart_type_id, existingTypeIds));
        await tx
          .delete(orgChartType)
          .where(inArray(orgChartType.org_chart_id, existingChartIds));
        await tx.delete(orgChart).where(eq(orgChart.org_chart_scheme_id, id));

        // Get existing team selected org charts before deletion
        const teamSelectedOrgCharts = await tx
          .select()
          .from(teamSelectedOrgChart);

        // Insert new org charts, types, and users
        for (const chart of orgCharts) {
          // Create new org chart
          const [createdChart] = await tx
            .insert(orgChart)
            .values({
              org_chart_scheme_id: id,
              created_at: sql`current_timestamp`,
            })
            .returning();

          // Create types for this chart
          for (const type of chart.orgChartTypes) {
            const [createdType] = await tx
              .insert(orgChartType)
              .values({
                name: type.name,
                type: type.type,
                is_visible: type.is_visible,
                org_chart_id: createdChart.id,
                created_at: sql`current_timestamp`,
              })
              .returning();

            // Create users for this type
            const createdUsersMap = await createOrgChartUsers({
              tx,
              users: type.users,
              orgChartTypeId: createdType.id,
            });

            // Update team selected org chart references
            if (teamSelectedOrgCharts.length > 0) {
              for (const oldUserId of Object.keys(createdUsersMap)) {
                const relatedTeamSelectedOrgChart = teamSelectedOrgCharts.find(
                  ({ org_chart_user_id }) =>
                    org_chart_user_id === parseInt(oldUserId, 10)
                );

                if (!relatedTeamSelectedOrgChart) continue;

                const newUserId = createdUsersMap[oldUserId];

                await tx
                  .update(teamSelectedOrgChart)
                  .set({ org_chart_user_id: parseInt(newUserId, 10) })
                  .where(
                    eq(
                      teamSelectedOrgChart.org_chart_user_id,
                      parseInt(oldUserId, 10)
                    )
                  );
              }
            }
          }
        }

        return { id };
      });

      return apiClient.success(req, res, next, { id });
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  list: async (req, res, next) => {
    const {
      sort = 'name',
      ascending = 'true',
      showDisabled = 'false',
      query = '',
    } = req.query;

    try {
      const schemes = await listOrgChartSchemes({
        sort,
        ascending,
        showDisabled,
        query,
      });

      return apiClient.success(req, res, next, schemes);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  listAll: async (req, res, next) => {
    try {
      const schemes = await listAllOrgChartSchemes();

      return apiClient.success(req, res, next, schemes);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  toggleDisabled: async (req, res, next) => {
    const { id } = req.params;

    try {
      const scheme = await toggleOrgChartSchemeDisabled(id);

      return apiClient.success(req, res, next, scheme);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },
};

async function createOrgChartUsers({ tx, users, orgChartTypeId }) {
  const createdUsersMap = {};

  for (const user of users) {
    const {
      name,
      title,
      status,
      photo,
      meet_1_text,
      meet_1_points,
      meet_2_text,
      meet_2_points,
      meet_3_text,
      meet_3_points,
      parent_id,
    } = user;

    const [createdUser] = await tx
      .insert(orgChartUser)
      .values({
        name,
        title,
        status,
        photo,
        meet_1_text,
        meet_1_points,
        meet_2_text,
        meet_2_points,
        meet_3_text,
        meet_3_points,
        org_chart_type_id: orgChartTypeId,
        parent_id,
        created_at: sql`current_timestamp`,
      })
      .returning();

    createdUsersMap[user.id] = createdUser.id;
  }

  return createdUsersMap;
}

export default ORG_CHART_CONTROLLER;
