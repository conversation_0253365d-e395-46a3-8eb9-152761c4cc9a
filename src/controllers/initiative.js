import { apiClient } from '../lib/index.js';
import {
  findInitiativeSchemeById,
  findInitiativesBySchemeId,
  createInitiativeScheme,
  createInitiative,
  updateInitiativeScheme,
  updateInitiative,
  listAllInitiativeSchemes,
  toggleInitiativeSchemeDisabled,
} from '../db/queries/initiative.js';
import { db } from '../db/index.js';
import { lumenInitiativeScheme } from '../db/schema.js';
import { sql, eq, and, asc, desc } from 'drizzle-orm';

const INITIATIVE_CONTROLLER = {
  get: async (req, res, next) => {
    try {
      const scheme = await findInitiativeSchemeById(req.params.id);

      if (!scheme) {
        return apiClient.serverError(req, res, next);
      }

      const initiatives = await findInitiativesBySchemeId(req.params.id);

      const response = {
        scheme,
        initiatives,
      };

      return apiClient.success(req, res, next, response);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  create: async (req, res, next) => {
    const { name, initiativeNumber, initiatives } = req.body;

    try {
      const createdScheme = await db.transaction(async (tx) => {
        const scheme = await createInitiativeScheme(name, initiativeNumber);

        for (const initiative of initiatives) {
          const { name, description, metric1, metric2, metric3 } = initiative;

          await createInitiative(
            scheme.scheme_id,
            name,
            description,
            metric1,
            metric2,
            metric3
          );
        }

        return scheme;
      });

      return apiClient.success(req, res, next, createdScheme);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  update: async (req, res, next) => {
    const { id, name, initiativeNumber, initiatives } = req.body;

    try {
      const updatedScheme = await db.transaction(async (tx) => {
        const scheme = await updateInitiativeScheme(id, name, initiativeNumber);

        for (const initiative of initiatives) {
          const {
            id: initiativeId,
            name,
            description,
            metric1,
            metric2,
            metric3,
          } = initiative;

          await updateInitiative(
            initiativeId,
            name,
            description,
            metric1,
            metric2,
            metric3
          );
        }

        return scheme;
      });

      return apiClient.success(req, res, next, updatedScheme);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  list: async (req, res, next) => {
    const {
      query = '',
      offset = 0,
      limit = 10,
      sort = 'scheme_name',
      ascending = 'true',
      showDisabled = 'false',
    } = req.query;

    try {
      const sortOrder = ascending === 'true' ? asc : desc;
      const showDisabledCondition = showDisabled === 'true' ? true : false;

      const schemes = await db
        .select({
          id: lumenInitiativeScheme.scheme_id,
          name: lumenInitiativeScheme.scheme_name,
          disabled: lumenInitiativeScheme.scheme_disabled,
          created: sql`TO_CHAR(${lumenInitiativeScheme.scheme_created_at}, 'YYYY-MM-DD')`,
          total: sql`COUNT(*) OVER()`,
        })
        .from(lumenInitiativeScheme)
        .where(
          and(
            sql`LOWER(${lumenInitiativeScheme.scheme_name}) LIKE ${
              '%' + query.toLowerCase() + '%'
            }`,
            eq(lumenInitiativeScheme.scheme_disabled, showDisabledCondition)
          )
        )
        .orderBy(sortOrder(lumenInitiativeScheme[sort]))
        .offset(parseInt(offset))
        .limit(parseInt(limit));

      return apiClient.success(req, res, next, schemes);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  listAll: async (req, res, next) => {
    try {
      const schemes = await listAllInitiativeSchemes();

      return apiClient.success(req, res, next, schemes);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  toggleDisabled: async (req, res, next) => {
    const { id } = req.params;

    try {
      const scheme = await toggleInitiativeSchemeDisabled(id);

      return apiClient.success(req, res, next, scheme);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },
};

export default INITIATIVE_CONTROLLER;
