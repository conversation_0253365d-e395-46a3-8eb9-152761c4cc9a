import AWS from 'aws-sdk';
import multer from 'multer';
import { apiClient } from '../lib/index.js';
import config from '../../config.json' with { type: 'json' };

AWS.config.update({
  accessKeyId: config.aws.accessKeyId,
  secretAccessKey: config.aws.secretAccessKey,
});

const FILES_CONTROLLER = {
  uploadFile: (req, res, next) => {
    var upload = multer({ storage: multer.memoryStorage() }).single('file');
    var s3 = new AWS.S3();

    upload(req, res, function (err) {
      console.log(req.file);

      // file : { fieldname, originalname, name, encoding, mimetype, path, extension, size, truncated, buffer }
      var params = {
        Bucket: config.aws.bucketName,
        Key: config.aws.dirName + req.file.originalname,
        Body: req.file.buffer,
        ACL: 'public-read',
      };

      s3.putObject(params, function (err, pres) {
        if (err) {
          return apiClient.invalidRequest(req, res, next);
        } else {
          return apiClient.success(req, res, next, {
            url:
              'https://s3.amazonaws.com/' +
              config.aws.bucketName +
              '/' +
              config.aws.dirName +
              req.file.originalname,
          });
        }
      });
    });
  },
};

export default FILES_CONTROLLER;
