import { apiClient } from '../lib/index.js';
import { db } from '../db/index.js';
import {
  updateTeamDetails,
  updateTeamPassword,
  findTeamGoals,
  findGlobalTeamMetric,
  findTeamClientRelations,
  findTeamsPaginated,
  findAllTeams,
  toggleTeamDisabled,
  resetTeamHistory,
  findTeamById,
  deleteTeamClients,
  insertTeam,
  insertTeamClient,
  getGlobalTeamMetricsSchemes,
  insertGlobalTeamMetric,
  findSelfAssessmentAnswers,
} from '../db/queries/team.js';
import { findDecisionResultsByUserId } from '../db/queries/decisionResults.js';
import { findDecisionSchemeById, findDecisionPagesBySchemeId } from '../db/queries/decision.js';
import { teamGoals, clients } from '../db/schema.js';
import { sql } from 'drizzle-orm';
import getExcel from '../lib/team_excel_export.js';
import getExcelAll from '../lib/all_teams_export_excel.js';
import multer from 'multer';
import { parseCsv } from '../services/parser.js';

const TEAM_CONTROLLER = {
  /**
   * Import teams from .csv or .xlsx files
   * @param req
   * @param res
   * @param next
   */
  import: (req, res, next) => {
    const upload = multer({ storage: multer.memoryStorage() }).single('file');

    upload(req, res, async (err) => {
      if (!req.file)
        return apiClient.serverError(
          req,
          res,
          next,
          'File should be provided while importing the team'
        );

      if (err) return apiClient.serverError(req, res, next, err);

      const { mimetype, buffer } = req.file;

      const allowedMimeTypes = ['text/csv'];

      if (!allowedMimeTypes.includes(mimetype.toLowerCase()))
        return apiClient.serverError(
          req,
          res,
          next,
          `${mimetype} is not allowed`
        );

      const results = await parseCsv(buffer);

      if (!results.length)
        return apiClient.serverError(req, res, next, `File is empty`);

      try {
        const teamsWithoutClients = [];
        const [clients, teams] = await Promise.all([
          db.select().from(clients),
          db.select().from(lumenTeam),
        ]);

        for (const team of results) {
          const { name, email, password, client: teamClient } = team;

          if (!name || !email || !password)
            console.error(
              `[Team Import]: Some field is missing in the CSV. Name: ${name}, email: ${email}, password: ${password}`
            );

          const relatedClient = clients.find(({ name }) => {
            if (!name) return false;

            if (!teamClient) return false;

            return (
              name.toString().toLowerCase() ===
              teamClient.toString().toLowerCase()
            );
          });

          const clientId = relatedClient ? relatedClient.id : null;
          // Check whether the team with the same name already exists in the system,
          // If yes - skip the iteration
          const isTeamWithTheSameNameExists = teams.some((team) => {
            if (!name) return false;

            const { team_name } = team;

            if (!team_name) return false;

            const isTeamExist =
              team_name.toString().toLowerCase() ===
              name.toString().toLowerCase();

            if (isTeamExist)
              console.log(
                `[Team Import]: Team with the same name exists: ${email}`
              );

            return isTeamExist;
          });

          // Check if the team with the same email exists in the system
          // If yes - skip the iteration
          const isTeamWithTheSameEmailExists = teams.some((team) => {
            if (!email) return false;

            const { team_email } = team;

            if (!team_email) return false;

            const isTeamExist =
              team_email.toString().toLowerCase() ===
              email.toString().toLowerCase();

            if (isTeamExist)
              console.log(
                `[Team Import]: Team with the same email exists: ${email}`
              );

            return isTeamExist;
          });

          if (isTeamWithTheSameNameExists || isTeamWithTheSameEmailExists)
            continue;

          const payload = { name, email, password };

          const createdTeam = await insertTeam(db, payload);
          const teamId = createdTeam[0].team_id;

          // If the client isn't present in the system, skip the iteration
          if (!clientId) {
            teamsWithoutClients.push(teamId);
            continue;
          }

          const globalTeamMetricsSchemes = await getGlobalTeamMetricsSchemes(
            db,
            clientId
          );

          for (const globalTeamMetricScheme of globalTeamMetricsSchemes) {
            const { id } = globalTeamMetricScheme;
            const metricPayload = { value: 0, schemeId: id, teamId, clientId };

            await insertGlobalTeamMetric(db, metricPayload);
          }
        }

        return apiClient.success(req, res, next, {
          teamsWithoutClients,
          total: results.length,
        });
      } catch (e) {
        console.error(e);
        return apiClient.serverError(req, res, next, e);
      }
    });
  },

  /**
   * Reset history for the team
   * @param req
   * @param res
   * @param next
   */
  resetHistory: async (req, res, next) => {
    try {
      const { source } = req.body;
      const teamId = source === 'admin' ? req.body.teamId : req.user.id;

      await resetTeamHistory(db, teamId);

      return apiClient.success(req, res, next, 'OK');
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  get: async (req, res, next) => {
    try {
      const teamResponse = await findTeamById(db, req.params.id);
      const { id, name, email } = teamResponse[0] || {};

      if (!id) {
        return apiClient.notFound(req, res, next);
      }

      const currentTeam = {
        id,
        name,
        email,
        selectedClients: [],
        isCompletedSelfAssessment: false,
      };

      teamResponse.forEach(({ clientid }) => {
        currentTeam.selectedClients.push(clientid);
      });

      const selfAssessmentAnswersResponse = await findSelfAssessmentAnswers(
        db,
        req.params.id,
        currentTeam.selectedClients[0]
      );

      if (selfAssessmentAnswersResponse[0]) {
        currentTeam.isCompletedSelfAssessment = true;
      }

      return apiClient.success(req, res, next, currentTeam);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  create: async (req, res, next) => {
    try {
      const result = {};

      const createdTeam = await insertTeam(db, {
        name: req.body.name,
        email: req.body.email,
        password: req.body.password,
      });

      result.teamId = createdTeam[0].team_id;

      // Go through each client exactly as original
      for (const clientId of req.body.selectedClients) {
        const [globalTeamMetricsSchemes] = await Promise.all([
          getGlobalTeamMetricsSchemes(db, clientId),
          insertTeamClient(db, result.teamId, clientId),
          db.insert(teamGoals).values({
            team_id: result.teamId,
            client_id: clientId,
            created_at: sql`current_timestamp`,
          }),
        ]);

        // Create global team metrics exactly as original
        for (const globalTeamMetricScheme of globalTeamMetricsSchemes) {
          await insertGlobalTeamMetric(db, {
            value: 0,
            schemeId: globalTeamMetricScheme.id,
            teamId: result.teamId,
            clientId: clientId,
          });
        }
      }

      return apiClient.success(req, res, next, result.teamId);
    } catch (error) {
      // Match original error handling exactly
      if (error && error.detail && error.detail.includes('already exists')) {
        return apiClient.invalidRequest(
          req,
          res,
          next,
          'Team with this email already exists.'
        );
      }
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  update: async (req, res, next) => {
    try {
      const { id, name, email, password, selectedClients } = req.body;
      const promises = [
        deleteTeamClients(db, id),
        updateTeamDetails(db, id, { name, email }),
      ];

      if (password) {
        promises.push(updateTeamPassword(db, id, password));
      }

      for (const clientId of selectedClients) {
        const [teamGoalsResponse, globalTeamMetricResponse] = await Promise.all(
          [
            findTeamGoals(db, id, clientId),
            findGlobalTeamMetric(db, id, clientId),
          ]
        );

        if (!(teamGoalsResponse && teamGoalsResponse.length)) {
          promises.push(
            db.insert(teamGoals).values({
              team_id: id,
              client_id: clientId,
              created_at: sql`current_timestamp`,
            })
          );
        }

        if (!(globalTeamMetricResponse && globalTeamMetricResponse.length)) {
          const globalTeamMetricsSchemes = await getGlobalTeamMetricsSchemes(
            db,
            clientId
          );

          for (const scheme of globalTeamMetricsSchemes) {
            promises.push(
              insertGlobalTeamMetric(db, {
                value: 0,
                schemeId: scheme.id,
                teamId: id,
                clientId,
              })
            );
          }
        }

        // Create new team client relationship
        promises.push(insertTeamClient(db, id, clientId));
      }

      await Promise.all(promises.map((fn) => fn));

      return apiClient.success(req, res, next);
    } catch (err) {
      console.error(err);

      if (err && err.detail && err.detail.includes('already exists')) {
        return apiClient.invalidRequest(
          req,
          res,
          next,
          'Team with this email already exists.'
        );
      }

      return apiClient.serverError(req, res, next);
    }
  },

  list: async (req, res, next) => {
    try {
      const teamIds = new Set();

      const isClientIdPresent =
        req.query.clientId &&
        req.query.clientId !== -1 &&
        req.query.clientId !== '-1' &&
        req.query.clientId !== 0 &&
        req.query.clientId !== '0';

      if (isClientIdPresent) {
        const teamClientsResponse = await findTeamClientRelations(
          db,
          req.query.clientId
        );

        if (!(teamClientsResponse && teamClientsResponse.length)) {
          return apiClient.success(req, res, next, []);
        }

        teamClientsResponse.forEach(({ team_id }) => teamIds.add(team_id));
      }

      const teamsResponse = await findTeamsPaginated(db, {
        query: req.query.query,
        offset: req.query.offset,
        limit: req.query.limit,
        teamIds: Array.from(teamIds),
        showDisabled: req.query.showDisabled || false,
        sort: req.query.sort,
        sortOrder: req.query.ascending ? 'ASC' : 'DESC',
      });

      if (!(teamsResponse && teamsResponse.length)) {
        return apiClient.success(req, res, next, []);
      }

      return apiClient.success(req, res, next, teamsResponse);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  listAll: async (req, res, next) => {
    try {
      const result = await findAllTeams(db);
      return apiClient.success(req, res, next, result);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  toggleDisabled: async (req, res, next) => {
    try {
      await toggleTeamDisabled(db, req.params.id);
      return apiClient.success(req, res, next);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  exportExcel: async (req, res, next) => {
    try {
      const teamId = req.params.id;

      const team = await db
        .select({
          id: lumenTeam.team_id,
          name: lumenTeam.team_name,
          email: lumenTeam.team_email,
          goal1: lumenTeam.team_goal1,
          goal2: lumenTeam.team_goal2,
          goal3: lumenTeam.team_goal3,
          challengeSchemeId: lumenTeam.team_challenge_scheme_id,
          initiativeSchemeId: lumenTeam.team_initiative_scheme_id,
          clientId: lumenTeam.client_id,
          clientName: clients.name,
          backgroundImage: clients.background_image,
          logoImage: clients.logo_image,
          challengesTabName: clients.challenges_tab_name,
          goalsTabName: clients.goals_tab_name,
          strategicTabName: clients.strategic_tab_name,
        })
        .from(lumenTeam)
        .leftJoin(clients, eq(lumenTeam.client_id, clients.id))
        .where(eq(lumenTeam.team_id, teamId))
        .limit(1);

      if (!team.length) {
        return apiClient.notFound(req, res, next, 'Team not found');
      }

      const challenges = await db
        .select({
          id: challenges.challenge_id,
          name: challenges.challenge_name,
          description: challenges.challenge_description,
          status: challenges.challenge_status,
          startDate: challenges.challenge_start_date,
          endDate: challenges.challenge_end_date,
          teamId: challenges.challenge_team_id,
        })
        .from(challenges)
        .where(eq(challenges.challenge_team_id, teamId));

      const initiatives = await db
        .select({
          id: initiatives.initiative_id,
          name: initiatives.initiative_name,
          description: initiatives.initiative_description,
          status: initiatives.initiative_status,
          startDate: initiatives.initiative_start_date,
          endDate: initiatives.initiative_end_date,
          teamId: initiatives.initiative_team_id,
        })
        .from(initiatives)
        .where(eq(initiatives.initiative_team_id, teamId));

      // Fetch decision data for the team
      let decisionData = null;
      const clientData = team[0];

      try {
        if (clientData.clientId) {
          // Get the client's decision scheme
          const clientInfo = await db
            .select({ decision_scheme_id: clients.decision_scheme_id })
            .from(clients)
            .where(eq(clients.id, clientData.clientId))
            .limit(1);

          if (clientInfo.length > 0 && clientInfo[0].decision_scheme_id) {
            // Get decision results for this team
            const decisionResults = await findDecisionResultsByUserId(teamId);

            if (decisionResults.length > 0) {
              // Get decision scheme and pages
              const decisionScheme = await findDecisionSchemeById(clientInfo[0].decision_scheme_id);
              const decisionPages = await findDecisionPagesBySchemeId(clientInfo[0].decision_scheme_id);

              decisionData = {
                scheme: decisionScheme,
                pages: decisionPages,
                results: decisionResults[0], // Get the latest result
              };

              console.log('Decision data found for team export:', {
                teamId,
                clientId: clientData.clientId,
                decisionSchemeId: clientInfo[0].decision_scheme_id,
                hasResults: decisionResults.length > 0,
                pagesCount: decisionPages?.length || 0
              });
            } else {
              console.log('No decision results found for team:', teamId);
            }
          } else {
            console.log('No decision scheme configured for client:', clientData.clientId);
          }
        }
      } catch (decisionError) {
        console.error('Error fetching decision data for export:', decisionError);
        // Continue with export even if decision data fails
      }

      const teamData = {
        ...team[0],
        challenges,
        initiatives,
        decisions: decisionData,
      };

      const report = getExcel(teamData);
      res.setHeader('Content-Type', 'application/vnd.openxmlformats');
      res.setHeader(
        'Content-Disposition',
        'attachment; filename=' + 'Team_Report.xlsx'
      );
      res.end(report, 'binary');
    } catch (err) {
      console.error(err);
      return apiClient.serverError(req, res, next);
    }
  },

  exportExcelAll: async (req, res, next) => {
    try {
      const teams = await db
        .select({
          id: lumenTeam.team_id,
          name: lumenTeam.team_name,
          email: lumenTeam.team_email,
          goal1: lumenTeam.team_goal1,
          goal2: lumenTeam.team_goal2,
          goal3: lumenTeam.team_goal3,
          challengeSchemeId: lumenTeam.team_challenge_scheme_id,
          initiativeSchemeId: lumenTeam.team_initiative_scheme_id,
          clientId: lumenTeam.client_id,
        })
        .from(lumenTeam);

      const clients = await db
        .select({
          id: clients.id,
          name: clients.name,
          backgroundImage: clients.background_image,
          logoImage: clients.logo_image,
          challengesTabName: clients.challenges_tab_name,
          goalsTabName: clients.goals_tab_name,
          strategicTabName: clients.strategic_tab_name,
          darkHighlightColor: clients.dark_highlight_color,
        })
        .from(clients);

      const teamData = teams.map((team) => {
        const client = clients.find((client) => client.id === team.clientId);
        return {
          ...team,
          clientName: client.name,
          backgroundImage: client.backgroundImage,
          logoImage: client.logoImage,
          challengesTabName: client.challengesTabName,
          goalsTabName: client.goalsTabName,
          strategicTabName: client.strategicTabName,
          darkHighlightColor: client.darkHighlightColor,
        };
      });

      const report = getExcelAll(teamData);
      res.setHeader('Content-Type', 'application/vnd.openxmlformats');
      res.setHeader(
        'Content-Disposition',
        'attachment; filename=' + 'All_Teams_Report.xlsx'
      );
      res.end(report, 'binary');
    } catch (err) {
      console.error(err);
      return apiClient.serverError(req, res, next);
    }
  },
};

export default TEAM_CONTROLLER;
