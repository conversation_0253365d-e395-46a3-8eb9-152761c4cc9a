import { apiClient } from '../lib/index.js';
import {
  createDecisionResult,
  findDecisionResultsByUserId,
  findDecisionResultById,
  findExistingDecisionResult,
  updateDecisionResult,
} from '../db/queries/decisionResults.js';

const DECISION_RESULTS_CONTROLLER = {
  store: async (req, res, next) => {
    const {
      userId,
      clientId,
      decisionSchemeId,
      selectedValues,
      totalFTE,
      totalInvestment,
      aiAnalysis,
    } = req.body;

    try {
      const existingResult = await findExistingDecisionResult(
        userId,
        clientId,
        decisionSchemeId
      );

      let result;
      if (existingResult) {
        result = await updateDecisionResult(
          existingResult.id,
          selectedValues,
          totalFTE,
          totalInvestment,
          aiAnalysis
        );
      } else {
        result = await createDecisionResult(
          userId,
          clientId,
          decisionSchemeId,
          selectedValues,
          totalFTE,
          totalInvestment,
          aiAnalysis
        );
      }
      // test
      return apiClient.success(req, res, next, result);
    } catch (error) {
      console.error('Error in decision results controller:', error);
      return apiClient.serverError(req, res, next);
    }
  },

  getUserResults: async (req, res, next) => {
    const { userId } = req.query;

    try {
      const results = await findDecisionResultsByUserId(userId);
      return apiClient.success(req, res, next, results);
    } catch (error) {
      console.error('Error in get user results:', error);
      return apiClient.serverError(req, res, next);
    }
  },

  getResult: async (req, res, next) => {
    const { id } = req.params;

    try {
      const result = await findDecisionResultById(id);

      if (!result) {
        return apiClient.notFound(req, res, next);
      }

      return apiClient.success(req, res, next, result);
    } catch (error) {
      console.error('Error in get result:', error);
      return apiClient.serverError(req, res, next);
    }
  },
};

export default DECISION_RESULTS_CONTROLLER;
