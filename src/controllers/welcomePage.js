import { apiClient } from '../lib/index.js';
import {
  findWelcomePageById,
  createWelcomePage,
  updateWelcomePage,
  listAllWelcomePages,
  toggleWelcomePageDisabled,
} from '../db/queries/welcomePage.js';
import { welcomePage } from '../db/schema.js';
import { db } from '../db/index.js';
import { sql, eq, and, inArray, asc, desc } from 'drizzle-orm';

const WELCOME_PAGE_CONTROLLER = {
  get: async (req, res, next) => {
    try {
      const welcomePage = await findWelcomePageById(req.params.id);

      if (!welcomePage) {
        return apiClient.notFound(req, res, next, 'Welcome Page not found');
      }

      const { id, name, text, image } = welcomePage;
      const result = {
        id,
        name,
        text,
        image,
      };

      return apiClient.success(req, res, next, result);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  create: async (req, res, next) => {
    const { name, text, image } = req.body;

    try {
      const welcomePage = await createWelcomePage({ name, text, image });

      const result = {
        id: welcomePage.id,
        name: welcomePage.name,
        text: welcomePage.text,
        image: welcomePage.image,
      };

      return apiClient.success(req, res, next, result);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  update: async (req, res, next) => {
    const { id } = req.params;
    const { name, text, image } = req.body;

    try {
      const welcomePage = await updateWelcomePage(id, { name, text, image });

      const result = {
        id: welcomePage.id,
        name: welcomePage.name,
        text: welcomePage.text,
        image: welcomePage.image,
      };

      return apiClient.success(req, res, next, result);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  list: async (req, res, next) => {
    const {
      query = '',
      offset = 0,
      limit = 10,
      sort = 'name',
      ascending = 'true',
      showDisabled = 'false',
    } = req.query;

    try {
      const sortOrder = ascending === 'true' ? asc : desc;
      const showDisabledCondition = showDisabled === 'true' ? true : false;

      const pages = await db
        .select({
          id: welcomePage.id,
          name: welcomePage.name,
          created_at: sql`TO_CHAR(${welcomePage.created_at}, 'YYYY-MM-DD')`,
          disabled: welcomePage.disabled,
          total: sql`COUNT(*) OVER()`,
        })
        .from(welcomePage)
        .where(
          and(
            sql`LOWER(${welcomePage.name}) LIKE ${
              '%' + query.toLowerCase() + '%'
            }`,
            eq(welcomePage.disabled, showDisabledCondition)
          )
        )
        .orderBy(sortOrder(welcomePage[sort]))
        .offset(parseInt(offset))
        .limit(parseInt(limit));

      return apiClient.success(req, res, next, pages);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  listAll: async (req, res, next) => {
    try {
      const pages = await listAllWelcomePages();

      return apiClient.success(req, res, next, pages);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  toggleDisabled: async (req, res, next) => {
    const { id } = req.params;

    try {
      const page = await toggleWelcomePageDisabled(id);

      return apiClient.success(req, res, next, page);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },
};

export default WELCOME_PAGE_CONTROLLER;
