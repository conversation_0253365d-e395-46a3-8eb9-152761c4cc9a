/**
 * This function sorts and place meeting in the order they were done
 * E.g. 1st Meet: 1 Meeting with CEO, 2nd Meet: 1 Meeting with CTO, 3rd Meet: 2nd Meeting with CEO
 * @param teamSelectedOrgCharts
 * @returns {{}}
 */
export function calculateOrgChartUsersMeetingOrders(
  teamSelectedOrgCharts = []
) {
  const result = {};

  if (!teamSelectedOrgCharts.length) return result;

  const initialMap = teamSelectedOrgCharts.reduce((acc, curr) => {
    const {
      org_chart_user_id,
      meet_1,
      meet_2,
      meet_3,
      meet_1_time,
      meet_2_time,
      meet_3_time,
    } = curr;
    const key = { org_chart_user_id };

    if (meet_1) acc.set({ ...key, meet: 1 }, new Date(meet_1_time).getTime());

    if (meet_2) acc.set({ ...key, meet: 2 }, new Date(meet_2_time).getTime());

    if (meet_3) acc.set({ ...key, meet: 3 }, new Date(meet_3_time).getTime());

    return acc;
  }, new Map());

  initialMap[Symbol.iterator] = function* () {
    yield* [...this.entries()].sort((a, b) => a[1] - b[1]);
  };

  let index = 1;
  for (const [key] of initialMap) {
    const { org_chart_user_id, meet } = key;

    if (!result.hasOwnProperty(org_chart_user_id))
      result[org_chart_user_id] = [];

    result[org_chart_user_id].push(
      `${getSuffix(index)} Meet: ${getSuffix(meet)} Meeting`
    );

    ++index;
  }

  return result;
}

function getSuffix(i) {
  const j = i % 10;
  const k = i % 100;

  if (j == 1 && k != 11) return i + 'st';

  if (j == 2 && k != 12) return i + 'nd';

  if (j == 3 && k != 13) return i + 'rd';

  return i + 'th';
}
