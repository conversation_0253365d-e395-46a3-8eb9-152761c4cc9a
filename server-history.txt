    1  git
    2  npm
    3  node
    4  sudo apt install npm nodejs
    5  sudo apt install postgres
    6  exit
    7  sudo apt-get dist-upgrade -y; sudo apt-get update --fix-missing -y; sudo apt-get upgrade -y; sudo apt autoremove -y
    8  sudo apt-get install postgresql
    9  ls /etc/postgresql/
   10  ls /etc/postgresql/9.5/
   11  ls /etc/postgresql/9.5/main
   12  ls /etc/postgresql/9.5/main/
   13  sudo apt-get install postgresql-contrib
   14  exit
   15  git clone https://<EMAIL>/bloodandtreasure/lumen-simulation-api.git app
   16  cd /app
   17  ls
   18  cd app
   19  ls
   20  npm install && npm run init
   21  sudo apt-get install npm
   22  npm install && npm run init
   23  npm install
   24  git pull
   25  npm install
   26  cat /home/<USER>/app/npm-debug.log
   27  npm owner ls nodemon
   28  npm install nodemon
   29  node
   30  sudo apt install nodejs-legacy
   31  npm install && npm run init
   32  babel-node ./ops/init_db.js --presets es2015,stage-0
   33  npm bugs lumen-simulation-api
   34  npm run init
   35  npm install babel-node
   36  node
   37  node -v
   38  sudo apt-get uninstall nodejs nodejs-legacy
   39  sudo apt uninstall nodejs nodejs-legacy
   40  sudo apt-get install -y build-essential
   41  curl -sL https://deb.nodesource.com/setup_9.x | sudo -E bash -
   42  npm install
   43  npm run init
   44  node -v
   45  curl -sL https://deb.nodesource.com/setup_8.x | sudo -E bash -
   46  sudo apt-get install -y nodejs
   47  node -v
   48  nvm
   49  npm run init
   50  nano config.json
   51  npm run init
   52  npm run -s build
   53  nodemon --help
   54  node_modules/nodemon/bin/nodemon.js --help
   55  node_modules/nodemon/bin/nodemon.js -w src --exec \"babel-node src --presets es2015,stage-0\"
   56  npm run dev
   57  git pull
   58  git reset --hard
   59  npm run dev
   60  npm install
   61  npm run dev
   62  git pull origin master
   63  npm run dev
   64  cd /app
   65  ls
   66  cd app
   67  ls
   68  cat package.json
   69  ps aux | grep nodemon
   70  sudo kill -9 28017
   71  nodemon
   72  node_modules/.bin/nodemon
   73  node_modules/.bin/nodemon -w src --exec babel-node src --presets es2015,stage-0
   74  nano config.json
   75  npm start
   76  sudo npm start
   77  cd /app
   78  ps aux | grep nodemon
   79  cd app
   80  sudo npm start
   81  cd /app
   82  cd app
   83  ps aux | grep nodemon
   84  node_modules/.bin/nodemon -w src --exec babel-node src --presets es2015,stage-0
   85  sudo npm start
   86  cd /app
   87  mv -R app /app
   88  mv  app /app
   89  sudo mv app /app
   90  cd /app
   91  sudo npm start
   92  cd /app
   93  ps aux | grep node
   94  sudo npm start
   95  cd /app
   96  cat package
   97  cat package.json
   98  node --help
   99  npm run -s build; sudo node dist &
  100  sudo node dist &
  101  ps aux | grep node
  102  sudo kill -9 20289 20290
  103  npm run -s build; sudo node dist &
  104  ps aux | grep node
  105  exit
  106  cd /app
  107  killall node
  108  killall nodemon
  109  npm run -s build; sudo node dist &
  110  exit
  111  cd /app
  112  killall node
  113  ps aux | grgep node
  114  ps aux | grep node
  115  sudo node dist &
  116  cd /app
  117  npm install forever --save
  118  nano forever/development.json
  119  touch forever/development.json
  120  touch ./forever/development.json
  121  mkdir forever
  122  touch ./forever/development.json
  123  nano forever/development.json
  124  nano package.json
  125  sudo npm run start-daemon
  126  ps aux | grep node
  127  exxit
  128  exit
  129  cd /app
  130  killall node
  131  ps aux | grep node
  132  sudo killall node
  133  git pull
  134  git reset --hard origin/master
  135  sudo npm run start-daemon
  136  ls
  137  cat package.json
  138  sudo npm run staging
  139  exit
  140  cd /app
  141  cat package.json
  142  npm run init
  143  exit
  144  cd /app
  145  cat package.json
  146  npm run mocha
  147  exit
  148  cd /app
  149  cat config.json
  150  exit
  151  cd /app
  152  ps aux | grep puma
  153  ps aux | grep node
  154  killall node
  155  sudo killall node
  156  sudo npm run staging
  157  ps aux | grep mocha
  158  exi
  159  exit
  160  cd /app
  161  git status
  162  git add .
  163  git status
  164  exit
  165  tail /var/log/syslog
  166  ll /etc/int.d/
  167  ll /etc/init.d/
  168  ps aux | grep lumen
  169  ps aux | grep node
  170  forever list
  171  sudo forever list
  172  ll /app/list
  173  ll /app/dist
  174  forever-service
  175  netstat -anp | grep 80
  176  sudo netstat -anp | grep 80
  177  sudo forever status
  178  /app/node_modules/forever/bin/forever status
  179  /app/node_modules/forever/bin/forever list
  180  sudo ps aux | grep node
  181  ll /app/node_modules/
  182  ll /app/node_modules/ | grep forever
  183  sudo updatedb
  184  locate lumen
  185  locate simulation
  186  strace
  187  sudo netstat -anp | grep 80
  188  strace -p 16743
  189  sudo strace -p 16743
  190  ll /var/log/
  191  tail -f /var/log/syslog
  192  jobs
  193  sudo jobs
  194  su -i
  195  sudo forever
  196  locate node_modules
  197  ll /usr/lib/node_modules/
  198  ll /usr/lib/node_modules/npm
  199  sudo ps aux | grep node
  200  sudo forever list
  201  /app/node_modules/forever/bin/monitor
  202  /app/node_modules/forever/bin/monitor --help
  203  ll ~/.forever
  204  cat ~/.forever FYH9.log
  205  tail ~/.forever/
  206  tail ~/.forever/FYH9.log -f
  207  tail ~/.forever/On5S.log
  208  tail ~/.forever/On5S.log -n 200
  209  cat ~/.forever/On5S.log | grep error
  210  cat ~/.forever/On5S.log | grep error -C 50
  211  ll ~/.forever
  212  cat ~/.forever/gAP8.log | grep error -C 50
  213  tail ~/.forever/FYH9.log -f
  214  uptime
  215  ll ~/.forever
  216  tail -f  ~/.forever/gAP8.log
  217  cat ~/.forever/gAP8.log | grep ERROR -C 10
  218  cat ~/.forever/gAP8.log | grep ERROR -C 50
  219  cat ~/.forever/gAP8.log | grep 'Script restart attempt'
  220  ll ~/.forever
  221  df
  222  tail ~/.forever/On5S.log
  223  tail ~/.forever/On5S.log -n 200
  224  tail /var/log/messages
  225  tail /var/log/syslog
  226  tail /var/log/syslog -n 2000
  227  date
  228  dmesg
  229  tail /var/log/postgresql/postgresql-9.5-main.log
  230  tail /var/log/postgresql/postgresql-9.5-main.log.1
  231  cat /var/log/postgresql/
  232  ll /var/log/postgresql/
  233  ll /var/log/kern.log
  234  tail /var/log/kern.log
  235  tail /var/log/auth.log
  236  tail ~/.forever/On5S.log -n 200
  237  tail ~/.forever/On5S.log -n 2000
  238  tail -f  ~/.forever/gAP8.log
  239  ll ~/.forever
  240  tail -f FYH9.log
  241  tail -f ~/.forever/FYH9.log
  242  sudo forever
  243  su -i
  244  su -I
  245  su -
  246  surever
  247  forever
  248  npm list -G
  249  sudo npm list -G
  250  npm list -g --depth=0
  251  sudo npm install -g forever
  252  npm list -g --depth=0
  253  sudo forever list -p ~/.forever
  254  uptime
  255  sudo npm install -g forever-service
  256  npm list -g --depth=0
  257  ll /app/dist
  258  cd /app/dist/
  259  ll
  260  sudo forever-service install api-staging --script index.js
  261  sudo service api-staging status
  262  sudo forever list -p ~/.forever
  263  sudo forever stop 0 -p ~/.forever
  264  sudo forever list -p ~/.forever
  265  sudo service api-staging start
  266  sudo service api-staging status
  267  cat /var/log/api-staging.log
  268  tail -f /var/log/api-staging.log
  269  exit
  270  cd /app
  271  history