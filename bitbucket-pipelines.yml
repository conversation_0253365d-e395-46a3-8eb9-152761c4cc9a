# This is a sample build configuration for JavaScript.
# Check our guides at https://confluence.atlassian.com/x/14UWN for more examples.
# Only use spaces to indent your .yml configuration.
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: node:14

# Whitelists
# https://support.atlassian.com/bitbucket-cloud/docs/what-are-the-bitbucket-cloud-ip-addresses-i-should-use-to-configure-my-corporate-firewall/
#
# *************/32, ************/32, **************/32, *************/32, **************/32, ***********/32, **************/32, ************/32, ************/32, **************/32, **************/32, ************/32, **************/32, *************/32, *************/32

pipelines:
  branches:
    staging:
      - step:
          deployment: staging
          caches:
            - node
          script:
            - echo -e "$SSH_PRIVATE_KEY" > connect.pem
            - chmod 0400 connect.pem
            - zip_files="$( echo $( ls -a | awk '{ print $1 }' ) | perl -pe 's/(\. |\.\.| config.json |.DS_Store|.git |.gitignore |node_modules )//ig' )"
            # - npm install
            - apt-get update
            - apt-get -y install zip
            - zip -r build.zip . -i "$zip_files"
            - sftp -i connect.pem "ubuntu@$STAGING_SERVER_IPA" <<< "put build.zip /app"
            - ssh -i connect.pem "ubuntu@$STAGING_SERVER_IPA" <<< 'PATH='$SERVER_PATH'; cd /app; sudo chown -R $USER .; sudo unzip -o build.zip; rm build.zip; sudo npm run -s build; pm2 stop index; pm2 restart index'
