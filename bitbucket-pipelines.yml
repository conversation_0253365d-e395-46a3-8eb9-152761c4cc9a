# This is a sample build configuration for JavaScript.
# Check our guides at https://confluence.atlassian.com/x/14UWN for more examples.
# Only use spaces to indent your .yml configuration.
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: node:14

# AWS_ACCESS_KEY_ID & AWS_SECRET_ACCESS_KEY are set automatically

pipelines:
  branches:
    staging:
      - step:
          deployment: staging
          caches:
            - node
          script:
            - unset CI
            - npm i -g react react-scripts
            - npm install --force
            - npm run build
            - apt-get update
            - apt-get -y install python3-pip
            - pip3 install awscli
            - aws s3 sync ./build $S3_STAGING_DESTINATION --region us-east-1 --acl public-read --exclude .DS_Store
            - aws cloudfront create-invalidation --distribution-id $CDN_DISTRIBUTION_ID --paths "/*" --region us-east-1
