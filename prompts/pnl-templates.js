// Available template variables for custom prompts
export const TEMPLATE_VARIABLES = {
  PNL_DATA: '{{PNL_DATA}}',
  TOTAL_FTE: '{{TOTAL_FTE}}',
  TOTAL_INVESTMENT: '{{TOTAL_INVESTMENT}}',
  FORMATTED_INVESTMENT: '{{FORMATTED_INVESTMENT}}',
  USER_SELECTIONS: '{{USER_SELECTIONS}}',
  SLIDER_DETAILS: '{{SLIDER_DETAILS}}',
  INCENTIVE_DETAILS: '{{INCENTIVE_DETAILS}}',
  PAGE_NAME: '{{PAGE_NAME}}',
  SCHEME_NAME: '{{SCHEME_NAME}}',
  RESPONSE_FORMAT: '{{RESPONSE_FORMAT}}',
  ANALYSIS_INSTRUCTIONS: '{{ANALYSIS_INSTRUCTIONS}}',
  FORMAT_NOTE: '{{FORMAT_NOTE}}',
};

export const PNL_ANALYSIS_INSTRUCTIONS = `The analysis should explain the impact of the FTE and investment changes on the P&L.
Remember to respond with ONLY valid JSON - no additional text before or after.`;

export const PNL_RESPONSE_EXAMPLE = `"updatedPnL": [
  [...existingHeaderRow, "Updated Value"],
  [...existingDataRow1, "125000"],
  [...existingDataRow2, "250000"],
  [...existingTotalRow, "375000"]
]`;

export const PNL_FORMAT_NOTE = `Note: Your response should preserve all existing PNL columns and data exactly as provided, adding only the "Updated Value" column with your calculated adjustments based on the FTE and investment changes.`;

// Default template for P&L analysis with intelligent business logic
export const DEFAULT_PNL_TEMPLATE = `
  You must respond with ONLY a valid JSON object in the following format:
  {{RESPONSE_FORMAT}}

  Analyze this P&L data and apply the proposed changes using the following business logic:

  P&L Data:
  {{PNL_DATA}}

  User Decisions Made:
  {{USER_SELECTIONS}}

  Detailed Breakdown:
  - Total FTE Change: {{TOTAL_FTE}} employees
  - Total Investment: {{FORMATTED_INVESTMENT}}
  - Slider Selections: {{SLIDER_DETAILS}}
  - Initiative Selections: {{INCENTIVE_DETAILS}}
  - Page: {{PAGE_NAME}} (Scheme: {{SCHEME_NAME}})

  BUSINESS LOGIC FOR P&L IMPACT:

  1. ANALYZE USER FTE DECISIONS AND INVESTMENT BUDGET:
     - Review the specific slider selections: {{SLIDER_DETAILS}}
     - Review the specific initiatives chosen: {{INCENTIVE_DETAILS}}
     - Each FTE decision directly impacts COGS, Gross Margin, and Corporate Expenses
     - Investment budget allocation affects operational efficiency and cost structure

  2. COGS (COST OF GOODS SOLD) IMPACT:
     - Production/Operations FTE: Add $80,000-$120,000 per FTE to COGS (direct labor)
     - Quality Control FTE: Add $70,000-$100,000 per FTE to COGS (quality assurance costs)
     - Supply Chain FTE: Add $85,000-$115,000 per FTE to COGS (procurement and logistics)
     - Manufacturing Technology Investment: Reduce COGS by 8-15% through automation
     - Process Improvement Investment: Reduce COGS by 5-12% through efficiency gains
     - COGS changes directly affect Gross Margin calculation

  3. GROSS MARGIN CALCULATION:
     - Gross Margin = Revenue - COGS
     - Revenue increases from sales/marketing FTE: $180,000-$250,000 per FTE
     - COGS increases from operational FTE as specified above
     - Technology investments in production reduce COGS percentage
     - Calculate new Gross Margin percentage: (Revenue - COGS) / Revenue * 100

  4. CORPORATE EXPENSES IMPACT:
     - Administrative FTE: Add $60,000-$90,000 per FTE to Corporate Expenses
     - Management FTE: Add $140,000-$200,000 per FTE to Corporate Expenses
     - HR/Finance FTE: Add $75,000-$110,000 per FTE to Corporate Expenses
     - IT/Technology FTE: Add $120,000-$180,000 per FTE to Corporate Expenses
     - Office/Infrastructure Investment: Add to Corporate Expenses (facilities, equipment)
     - Training Investment: Add to Corporate Expenses (employee development)

  5. CASCADING P&L EFFECTS:
     - Operating Income = Gross Margin - Corporate Expenses
     - EBITDA = Operating Income + Depreciation/Amortization
     - Net Income = Operating Income - Interest - Taxes
     - Calculate percentage changes for all derived metrics
     - Update margin percentages based on new revenue base

  6. SPECIFIC CALCULATION REQUIREMENTS:
     - Identify FTE type from slider field names (sales, operations, admin, technical, etc.)
     - Apply appropriate cost per FTE to correct P&L category (COGS vs Corporate Expenses)
     - Calculate revenue impact and add to top line
     - Recalculate all dependent metrics (margins, operating income, etc.)
     - Show both absolute dollar changes and percentage impacts
     - Ensure mathematical consistency across all P&L line items

  {{ANALYSIS_INSTRUCTIONS}}

  Provide specific, quantified impacts rather than generic statements. Explain your assumptions and calculations clearly.

  {{FORMAT_NOTE}}
`;
