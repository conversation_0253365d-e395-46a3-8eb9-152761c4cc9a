```
    "globalTeamMetrics": [
        {
            "id": 169,
            "alias": "metric 1",
            "name": "Metric 1",
            "defaultValue": 10,
            "value": 11
        },
        {
            "id": 170,
            "alias": "metric 2",
            "name": "Metric 2",
            "defaultValue": 10,
            "value": 4
        },
        {
            "id": 171,
            "alias": "metric 3",
            "name": "Metric 3",
            "defaultValue": 30,
            "value": 70
        }
    ]
```

When I first load the page, if no initiatives are selected, I should see 10%, 10%, & 30%. What I see right now is 20%, 20%, 60%.

When I have not yet selected anythig I see 20% instead of 10%, when I select the first initiative, save and come back I see 21% instead of 20%

When I check and uncheck an initiative I get the right metric values. When I check an initative again and save I see the right values, but when I leave and come back, in they're slightly wrong.

If I haven't selected any and I load this view for the first time, the metrics are double their default, can you see what's going wrong here?

here's the server side code:

```
{
    "id": 2230,
    "name": "<EMAIL>",
    "goal1": "",
    "goal2": "",
    "goal3": "",
    "client": {
        "name": "Oscar Test 2502 #1",
        "backgroundImage": "https://s3.amazonaws.com/lumen-simulation-api-files/uploads/LJXmMLM2OmTVdR",
        "logoImage": "https://s3.amazonaws.com/lumen-simulation-api-files/uploads/MTqinA9yXuyl1l",
        "challengesTabName": "Challenges #1",
        "homeTabName": "Home",
        "goalsTabName": "Goals",
        "strategicInitiativesTabName": "Initiatives",
        "orgChartTabName": "Org Chart",
        "leaderboardTabName": "Leaderboard",
        "selfAssessmentTabName": "Assessment",
        "homeTabVisibility": true,
        "goalsTabVisibility": true,
        "challengesTabVisibility": true,
        "initiativesTabVisibility": true,
        "orgChartTabVisibility": true,
        "selfAssessmentTabVisibility": true,
        "leaderboardTabVisibility": true,
        "darkHighlightColor": "#2ac8b0",
        "lightHighlightColor": "#0098e3",
        "selfAssessmentAnswersId": 637
    },
    "globalTeamMetrics": [
        {
            "id": 169,
            "alias": "metric 1",
            "name": "Metric 1",
            "defaultValue": 10,
            "value": 11
        },
        {
            "id": 170,
            "alias": "metric 2",
            "name": "Metric 2",
            "defaultValue": 10,
            "value": 4
        },
        {
            "id": 171,
            "alias": "metric 3",
            "name": "Metric 3",
            "defaultValue": 30,
            "value": 70
        }
    ]
}
```

By default the metrics should be as set in the team:

```
{
    "id": 2230,
    "name": "<EMAIL>",
    "goal1": "",
    "goal2": "",
    "goal3": "",
    "client": {
        "name": "Oscar Test 2502 #1",
        "backgroundImage": "https://s3.amazonaws.com/lumen-simulation-api-files/uploads/LJXmMLM2OmTVdR",
        "logoImage": "https://s3.amazonaws.com/lumen-simulation-api-files/uploads/MTqinA9yXuyl1l",
        "challengesTabName": "Challenges #1",
        "homeTabName": "Home",
        "goalsTabName": "Goals",
        "strategicInitiativesTabName": "Initiatives",
        "orgChartTabName": "Org Chart",
        "leaderboardTabName": "Leaderboard",
        "selfAssessmentTabName": "Assessment",
        "homeTabVisibility": true,
        "goalsTabVisibility": true,
        "challengesTabVisibility": true,
        "initiativesTabVisibility": true,
        "orgChartTabVisibility": true,
        "selfAssessmentTabVisibility": true,
        "leaderboardTabVisibility": true,
        "darkHighlightColor": "#2ac8b0",
        "lightHighlightColor": "#0098e3",
        "selfAssessmentAnswersId": 637
    },
    "globalTeamMetrics": [
        {
            "id": 169,
            "alias": "metric 1",
            "name": "Metric 1",
            "defaultValue": 10,
            "value": 11
        },
        {
            "id": 170,
            "alias": "metric 2",
            "name": "Metric 2",
            "defaultValue": 10,
            "value": 4
        },
        {
            "id": 171,
            "alias": "metric 3",
            "name": "Metric 3",
            "defaultValue": 30,
            "value": 70
        }
    ]
}
```

-

When I first load the page, if no initiatives are selected, I should see 10%, 10%, & 30% because that's the default value for each globalTeamMetric. What I see right now is 11%, 12%, 60%.

-
