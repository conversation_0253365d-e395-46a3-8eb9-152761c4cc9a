# Prompts

This directory contains AI prompt templates and configuration for the Lumen simulation platform.

## Overview

Prompts define the AI behavior and response patterns used throughout the application. They include system instructions, template variables, and formatted examples for consistent AI interactions.

## Files

### Core Configuration
- **system.js** - System-level AI prompts and instructions
  - Defines AI persona and behavior guidelines
  - Sets context for business simulation scenarios
  - Establishes response tone and style

### Template Management
- **pnl-templates.js** - P&L (Profit & Loss) analysis templates
  - Template variable definitions for dynamic content
  - P&L analysis instructions and guidelines
  - Response format examples and structures
  - Default template configurations

### Examples & Documentation
- **example.md** - Example prompts and usage patterns
  - Sample prompt structures
  - Best practice demonstrations
  - Template usage examples

## Key Components

### System Prompts
Centralized configuration for AI behavior:
- Business simulation context
- Response formatting requirements
- Professional communication standards
- Domain expertise guidelines

### P&L Templates
Specialized templates for financial analysis:
- **TEMPLATE_VARIABLES** - Dynamic content placeholders
- **PNL_ANALYSIS_INSTRUCTIONS** - Analysis methodology guidelines
- **PNL_RESPONSE_EXAMPLE** - Structured response examples
- **PNL_FORMAT_NOTE** - Output formatting specifications
- **DEFAULT_PNL_TEMPLATE** - Base template for P&L analysis

## Usage

Prompts are imported and used by utility functions:
```javascript
import { SYSTEM_PROMPTS } from './prompts/system.js';
import { DEFAULT_PNL_TEMPLATE } from './prompts/pnl-templates.js';
```

Templates support variable substitution for dynamic content generation based on user inputs and simulation data.

## Best Practices

- Keep prompts focused and specific to their purpose
- Use consistent formatting and structure
- Include clear examples for expected outputs
- Document template variables thoroughly
- Test prompt variations for optimal AI responses